#!/usr/bin/env python3

import hmac
import hashlib

# Exact values from Rails logs
timestamp = "1750244233"
payload = '{"original_blob_key":"gu0oc5wt0utc5c0r7ijnayo3k61m.pdf","thumbnail":{"key":"thumbnails/gu0oc5wt0utc5c0r7ijnayo3k61m.png","filename":"gu0oc5wt0utc5c0r7ijnayo3k61m.png","content_type":"image/png","byte_size":12345,"checksum":"base64encodedmd5hash"}}'
secret = "ye0fYc3~]d^v[l5Y]c:$g]:&)(nS_a0VfzwGDy$gX&asj3,2dGHIP~n$%-=8j+w]"

# Create string-to-sign exactly like Rails
string_to_sign = f"{timestamp}.{payload}"

print("=== Python HMAC Debug ===")
print(f"Timestamp: {timestamp}")
print(f"Payload: {payload}")
print(f"String to sign: {string_to_sign}")
print(f"String to sign length: {len(string_to_sign)}")
print(f"Secret (first 10 chars): {secret[:10]}...")

# Calculate HMAC like Lambda does
signature = hmac.new(
    secret.encode('utf-8'),
    string_to_sign.encode('utf-8'),
    hashlib.sha256
).hexdigest()

print(f"Python calculated signature: {signature}")
print(f"Received from test script: b0eb93fdbc9f5a3feb43021c7ca9dcdf4ae7bd5b0b6ecde18c284cd76f7f7d71")
print(f"Rails calculated signature: 2d2836cf9a20651e5c20e6b5cd1cb9e31b505867b9ec708eff21bce268658c35")

# Test if they match
if signature == "b0eb93fdbc9f5a3feb43021c7ca9dcdf4ae7bd5b0b6ecde18c284cd76f7f7d71":
    print("✅ Python matches received signature")
else:
    print("❌ Python does NOT match received signature")

if signature == "2d2836cf9a20651e5c20e6b5cd1cb9e31b505867b9ec708eff21bce268658c35":
    print("✅ Python matches Rails signature")
else:
    print("❌ Python does NOT match Rails signature")