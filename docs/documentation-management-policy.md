# Documentation Management Policy

## 📋 **Core Documentation Principle**
**One Topic = One File with Clear References**. Avoid creating multiple files for the same topic.

## 📚 **Primary Documentation Structure**

### **For Security (CONSOLIDATED) ✅**
- **`SECURITY.md`** - Security policy and entry point
- **`docs/security/THREAT_MODEL.md`** - Technical security architecture and controls
- **`docs/security/PLAYBOOK.md`** - Incident response procedures

### **For Implementation**
- **`SECURE_INLINE_FILE_DISPLAY_IMPLEMENTATION_PLAN.md`** - Master implementation plan
- **File System Architecture** - See implementation plan for details

### **For Operations**
- **`CLAUDE.md`** - This file: settings, quick reference, and documentation policy
- **`README.md`** - User-facing project documentation

## 🚫 **Deprecated Files (To Be Archived)**
The following files have been consolidated into the new security structure:
- ~~`SECURITY_GUIDE.md`~~ → Content moved to new structure
- ~~`GEMINI_SECURITY_REVIEW_FIXES_IMPLEMENTATION.md`~~ → Merged into THREAT_MODEL.md
- ~~`SECURE_INLINE_FILE_DISPLAY_CRITICAL_SECURITY_FIXES.md`~~ → Merged into THREAT_MODEL.md
- ~~`COMPREHENSIVE_SECURITY_FIXES_IMPLEMENTATION.md`~~ → Merged into THREAT_MODEL.md
- ~~`SECURE_FILE_DISPLAY_SYSTEM_MASTER_GUIDE.md`~~ → Security aspects in THREAT_MODEL.md
- ~~`AUTHORIZATION_SECURITY_TESTING_FRAMEWORK.md`~~ → Test details in THREAT_MODEL.md

## 📝 **Documentation Creation Rules**

### **Before Creating New Documentation:**
1. **Check if topic fits in existing file** - Most content should extend existing docs
2. **One file per major topic** - Don't create multiple files for same domain
3. **Reference in primary file** - All specialized docs must be referenced from primary doc
4. **Clear naming convention** - `TOPIC_GUIDE.md` format for primary references

### **When to Create New File:**
- **Large, standalone topic** (>500 lines) that would bloat primary file
- **Specialized procedures** that need detailed, step-by-step instructions  
- **Cross-referenced content** used by multiple teams/roles

### **Documentation Hierarchy:**
```
├── SECURITY.md (Security Policy & Entry Point)
├── docs/
│   ├── security/
│   │   ├── THREAT_MODEL.md (Architecture & Controls)
│   │   └── PLAYBOOK.md (Incident Response)
│   ├── architecture/
│   │   └── application-architecture.md
│   ├── features/
│   │   ├── project-sharing/
│   │   └── file-system/
│   └── maintenance/
│       └── troubleshooting-guide.md
├── CLAUDE.md (Settings & Quick Reference)
├── Implementation Guides
│   └── SECURE_INLINE_FILE_DISPLAY_IMPLEMENTATION_PLAN.md
└── Operational Guides
    ├── AR_QUERY_GUIDE.md
    ├── BACKGROUND_JOBS_GUIDE.md
    └── FILES_STORAGE_TRANSITION.md
```

## 🔄 **Maintenance Protocol**

### **For Claude Code AI:**
1. **Always check existing docs first** before creating new files
2. **Update primary references** when adding new security/implementation details
3. **Consolidate when possible** - merge related content instead of creating new files
4. **Reference this policy** when making documentation decisions

### **For Security Updates:**
- **Primary location**: `docs/security/THREAT_MODEL.md`
- **Testing details**: Include in threat model
- **Never create separate security fix files**

### **For Implementation Updates:**
- **Architecture changes**: Update `docs/architecture/application-architecture.md`
- **New features**: Extend implementation plan or create in `docs/features/`
- **Bug fixes**: Update relevant primary doc

### **For Maintenance Updates:**
- **Troubleshooting**: Update `docs/maintenance/troubleshooting-guide.md`
- **Known issues**: Document in troubleshooting guide
- **Performance fixes**: Update architecture or maintenance docs

## ✅ **Quality Standards**
- **Single source of truth** for each topic
- **Clear cross-references** between related docs  
- **Searchable structure** with good headers and TOCs
- **Actionable information** with specific commands/procedures
- **Current status** clearly indicated with dates and review schedules

## 📁 **File Organization Standards**

### **Naming Conventions**
- **Primary guides**: `TOPIC_GUIDE.md` (uppercase)
- **Feature docs**: `kebab-case.md` in `docs/features/feature-name/`
- **Architecture docs**: `application-architecture.md` in `docs/architecture/`
- **Maintenance docs**: `troubleshooting-guide.md` in `docs/maintenance/`

### **Content Standards**
- **Headers**: Use proper markdown hierarchy (# ## ###)
- **Code blocks**: Always specify language for syntax highlighting
- **Links**: Use relative paths for internal documentation
- **Status indicators**: Use ✅ ❌ 🚧 for clear status communication

### **Review Schedule**
- **Security docs**: Monthly review required
- **Architecture docs**: Review on major feature changes
- **Troubleshooting**: Update as issues are resolved
- **Implementation plans**: Mark completed sections clearly