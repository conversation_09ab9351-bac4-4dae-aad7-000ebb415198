# Thumbnail Display Investigation - January 4, 2025

## Issue Summary

Production environment experienced intermittent thumbnail display failures where:
- Thumbnails that previously worked stopped displaying
- All files showed the same thumbnail (first in directory) due to flawed fallback logic
- **Even newly uploaded files failed initially**, then started working later
- No code changes were made between working and non-working states

## Critical Findings

### 1. Security Vulnerability Fixed
Removed dangerous directory-wide fallback in `Project#thumbnail_for_file`:
```ruby
# REMOVED: This was selecting ANY thumbnail in same directory
thumbnail = pdf_thumbnails
  .joins(:blob)
  .where("active_storage_blobs.key LIKE ?", "#{directory_path}/%.png")
  .order(created_at: :desc)
  .first
```

### 2. Root Cause Analysis

#### What We Ruled Out
- **Not a Lambda issue**: Lambda function unchanged, works correctly
- **Not a naming pattern issue**: New uploads work with current naming
- **Not a code issue**: No deployments between working/broken states

#### Most Likely Cause: Rails/Active Storage Caching
Evidence pointing to caching/association issues:
1. System worked → stopped working → works again (no code changes)
2. **New uploads also failed initially**, then worked later
3. Thumbnails exist in S3 and database but Rails can't find them
4. After removing fallback, old files show no thumbnail (lookup fails)
5. New uploads now work perfectly

### 3. Technical Details

Current thumbnail lookup uses two methods:
1. Legacy pattern: `thumb_{secure_hash[0..8]}%` 
2. Lambda pattern: S3 key with .png extension

When both fail, it previously fell back to directory search (security issue - now removed).

## Next Investigation Steps

### Caching Theory Investigation
1. **Active Storage association caching**
   - Check if `pdf_thumbnails` association gets stale
   - Test with `.reload` on associations
   
2. **Rails query caching**
   - Cached negative results from failed lookups
   - Database connection pool with stale data
   
3. **Application-level caching**
   - Check for any Rails.cache usage around thumbnails
   - CDN/proxy caching of thumbnail URLs

### Diagnostic Commands
```ruby
# In production console for failing project:
p = Project.find(ID)
file = p.private_files.first

# Force reload associations
p.pdf_thumbnails.reload
p.reload

# Check if thumbnail now found
p.thumbnail_for_file(file)

# Clear Rails cache
Rails.cache.clear

# Check database directly
ActiveStorage::Attachment.where(record: p, name: 'pdf_thumbnails').count
```

## Temporary Workaround
Users can re-upload files to generate new thumbnails that work with current system.

## Long-term Considerations
While current system works when not affected by caching, consider:
- Adding cache-busting mechanisms
- Monitoring for association staleness
- Implementing explicit thumbnail refresh capability

---

**Status**: Investigation ongoing. Security vulnerability fixed. System operational for new uploads.