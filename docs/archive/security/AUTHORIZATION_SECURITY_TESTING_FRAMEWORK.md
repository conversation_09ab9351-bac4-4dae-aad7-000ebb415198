# Authorization Security Testing Framework

## Overview
This document provides a comprehensive testing framework for validating the authorization security of the secure inline file display system, with a focus on preventing Insecure Direct Object Reference (IDOR) vulnerabilities.

## Date Created
- **Date**: January 11, 2025
- **Purpose**: Validate authorization security after implementing critical security fixes
- **Scope**: JWT authentication, file access authorization, token security

## Critical Authorization Test Cases

### 🔴 **Test Suite 1: IDOR (Insecure Direct Object Reference) Protection**

#### **Test Case 1.1: Cross-User File Access Prevention**
```gherkin
Feature: Prevent cross-user file access
  As a security requirement
  I want to ensure users cannot access files from other users' projects
  So that file privacy is maintained

Scenario: User A attempts to access User B's file
  Given User A is authenticated with valid JWT
  And User B has uploaded "confidential.pdf" to Project B
  And User A does not have access to Project B
  When User A requests a file token for "confidential.pdf" from Project B
  Then the request should return HTTP 403 Forbidden
  And no file token should be provided
  And the attempt should be logged for security monitoring

Scenario: User A attempts direct file stream access to User B's file
  Given User A somehow obtains a valid JWT token for User B's file
  When User A attempts to access "/secure/stream?t={user_b_token}"
  Then the request should return HTTP 403 Forbidden
  And the authorization check should validate the JWT user matches the resource owner
```

#### **Test Case 1.2: Project Permission Validation**
```gherkin
Feature: Validate project-level permissions
  As a security requirement
  I want to ensure file access requires proper project authorization
  So that project privacy is maintained

Scenario: User with summary-only access attempts full file access
  Given User A has "summary_only" access to Project X
  And Project X contains file "document.pdf"
  When User A requests a file token for "document.pdf"
  Then the request should return HTTP 403 Forbidden
  And the error should indicate insufficient permissions

Scenario: User with full access can access files
  Given User A has "full_details" access to Project X
  And Project X contains file "document.pdf"
  When User A requests a file token for "document.pdf"
  Then the request should return HTTP 200 OK
  And a valid file token should be provided
```

### 🔴 **Test Suite 2: JWT Token Security**

#### **Test Case 2.1: Token Tampering Prevention**
```gherkin
Feature: Prevent JWT token tampering
  As a security requirement
  I want to ensure tampered JWTs are rejected
  So that authentication integrity is maintained

Scenario: User modifies JWT payload
  Given User A has a valid JWT with user_id: 123
  When User A modifies the JWT payload to user_id: 456
  And attempts to access a file with the tampered token
  Then the request should return HTTP 401 Unauthorized
  And the signature validation should fail

Scenario: User attempts to use expired token
  Given User A has a JWT that expired 1 hour ago
  When User A attempts to access a file with the expired token
  Then the request should return HTTP 401 Unauthorized
  And the expiration check should fail
```

#### **Test Case 2.2: Token Replay Attack Prevention**
```gherkin
Feature: Prevent token replay attacks
  As a security requirement
  I want to limit token lifetime and usage
  So that stolen tokens have minimal impact

Scenario: Token used after user permissions revoked
  Given User A has valid file access token for Project X
  And User A's access to Project X is revoked by admin
  When User A attempts to use the existing token
  Then the request should return HTTP 403 Forbidden
  And the real-time authorization check should fail

Scenario: Token used beyond expiration window
  Given a file access token with 5-minute expiration
  When the token is used 6 minutes after creation
  Then the request should return HTTP 401 Unauthorized
  And the token expiration check should fail
```

### 🔴 **Test Suite 3: File Hash Security**

#### **Test Case 3.1: Hash Enumeration Prevention**
```gherkin
Feature: Prevent file hash enumeration
  As a security requirement
  I want to ensure file hashes cannot be guessed or enumerated
  So that unauthorized file discovery is prevented

Scenario: Attacker attempts hash enumeration
  Given a project with 10 files
  When an attacker generates 1000 random file hashes
  And attempts to request tokens for each hash
  Then all invalid hash requests should return HTTP 404 Not Found
  And response times should be consistent (no timing attacks)
  And no file information should be leaked in error messages

Scenario: Hash uniqueness across projects
  Given identical file "test.pdf" uploaded to Project A and Project B
  When hashes are generated for both files
  Then the hashes should be different due to project scoping
  And neither hash should work for the other project's file
```

#### **Test Case 3.2: Timing Attack Prevention**
```gherkin
Feature: Prevent timing attacks on hash comparison
  As a security requirement
  I want hash comparisons to be constant-time
  So that valid hashes cannot be discovered through timing analysis

Scenario: Constant-time hash comparison
  Given a project with file hash "abc123..."
  When comparing valid hash "abc123..." vs invalid hash "xyz789..."
  Then both comparisons should take approximately the same time
  And the secure_compare function should be used for all comparisons
```

## Implementation Guide

### **Setting Up Test Environment**

#### **Required Test Data Setup**
```ruby
# spec/support/authorization_test_setup.rb

RSpec.shared_context "authorization test setup" do
  let!(:user_a) { create(:user, email: "<EMAIL>") }
  let!(:user_b) { create(:user, email: "<EMAIL>") }
  let!(:admin_user) { create(:user, :admin, email: "<EMAIL>") }
  
  let!(:project_a) { create(:project, user: user_a) }
  let!(:project_b) { create(:project, user: user_b) }
  let!(:shared_project) { create(:project, user: admin_user) }
  
  let!(:file_a) do
    project_a.private_files.attach(
      io: StringIO.new("User A's confidential content"),
      filename: "confidential_a.pdf",
      content_type: "application/pdf"
    )
    project_a.private_files.last
  end
  
  let!(:file_b) do
    project_b.private_files.attach(
      io: StringIO.new("User B's confidential content"),
      filename: "confidential_b.pdf", 
      content_type: "application/pdf"
    )
    project_b.private_files.last
  end
  
  # Set up project permissions
  before do
    # User A has full access to their own project
    create(:project_auth, user: user_a, project: project_a, access_level: 'full_details')
    
    # User B has full access to their own project  
    create(:project_auth, user: user_b, project: project_b, access_level: 'full_details')
    
    # User A has only summary access to shared project
    create(:project_auth, user: user_a, project: shared_project, access_level: 'summary_only')
    
    # User B has full access to shared project
    create(:project_auth, user: user_b, project: shared_project, access_level: 'full_details')
  end
end
```

#### **Authorization Test Implementation**
```ruby
# spec/requests/authorization_security_spec.rb

require 'rails_helper'

RSpec.describe "Authorization Security", type: :request do
  include_context "authorization test setup"
  
  describe "IDOR Protection" do
    it "prevents cross-user file access" do
      # User A attempts to access User B's file
      sign_in user_a
      file_hash = project_b.generate_secure_file_hash(file_b)
      
      post request_file_token_project_path(project_b), 
           params: { file_hash: file_hash },
           headers: { 'Content-Type' => 'application/json' }
      
      expect(response).to have_http_status(:forbidden)
      expect(response.body).not_to include("token")
    end
    
    it "validates project-level permissions" do
      # User A with summary-only access attempts file access
      sign_in user_a
      file_hash = shared_project.generate_secure_file_hash(shared_project.private_files.first)
      
      post request_file_token_project_path(shared_project),
           params: { file_hash: file_hash },
           headers: { 'Content-Type' => 'application/json' }
      
      expect(response).to have_http_status(:forbidden)
    end
    
    it "allows access with proper permissions" do
      # User B with full access can access shared project files
      shared_project.private_files.attach(
        io: StringIO.new("Shared content"),
        filename: "shared.pdf",
        content_type: "application/pdf"
      )
      shared_file = shared_project.private_files.last
      
      sign_in user_b
      file_hash = shared_project.generate_secure_file_hash(shared_file)
      
      post request_file_token_project_path(shared_project),
           params: { file_hash: file_hash },
           headers: { 'Content-Type' => 'application/json' }
      
      expect(response).to have_http_status(:ok)
      expect(JSON.parse(response.body)).to have_key("token")
    end
  end
  
  describe "JWT Token Security" do
    it "rejects tampered tokens" do
      # Get a valid token first
      sign_in user_a
      file_hash = project_a.generate_secure_file_hash(file_a)
      
      post request_file_token_project_path(project_a),
           params: { file_hash: file_hash },
           headers: { 'Content-Type' => 'application/json' }
      
      token = JSON.parse(response.body)["token"]
      
      # Tamper with the token (modify payload)
      payload = JWT.decode(token, nil, false)[0]
      payload["user_id"] = user_b.id
      tampered_token = JWT.encode(payload, "wrong_secret", "HS256")
      
      # Attempt to use tampered token
      get secure_stream_path, params: { t: tampered_token }
      
      expect(response).to have_http_status(:forbidden)
    end
    
    it "rejects expired tokens" do
      # Create an expired token manually
      expired_payload = {
        user_id: user_a.id,
        file_id: file_a.id,
        project_id: project_a.id,
        exp: 1.hour.ago.to_i
      }
      expired_token = JWT.encode(expired_payload, Rails.application.secret_key_base, "HS256")
      
      get secure_stream_path, params: { t: expired_token }
      
      expect(response).to have_http_status(:forbidden)
    end
  end
  
  describe "Hash Security" do
    it "prevents hash enumeration" do
      sign_in user_a
      
      # Generate 100 random hashes and verify all fail
      100.times do
        random_hash = SecureRandom.hex(16)
        
        post request_file_token_project_path(project_a),
             params: { file_hash: random_hash },
             headers: { 'Content-Type' => 'application/json' }
        
        expect(response).to have_http_status(:not_found)
        expect(response.body).not_to include(random_hash)
      end
    end
    
    it "ensures hash uniqueness across projects" do
      # Upload identical files to different projects
      identical_content = StringIO.new("Identical content")
      
      project_a.private_files.attach(
        io: identical_content.clone,
        filename: "identical.pdf",
        content_type: "application/pdf"
      )
      
      project_b.private_files.attach(
        io: identical_content.clone,
        filename: "identical.pdf", 
        content_type: "application/pdf"
      )
      
      file_a_identical = project_a.private_files.last
      file_b_identical = project_b.private_files.last
      
      hash_a = project_a.generate_secure_file_hash(file_a_identical)
      hash_b = project_b.generate_secure_file_hash(file_b_identical)
      
      expect(hash_a).not_to eq(hash_b)
    end
  end
end
```

### **Performance Security Testing**

#### **DoS Protection Validation**
```ruby
# spec/requests/dos_protection_spec.rb

require 'rails_helper'

RSpec.describe "DoS Protection", type: :request do
  include_context "authorization test setup"
  
  describe "Resource Exhaustion Prevention" do
    it "handles large file collections gracefully" do
      # Create project with 150 files (above the 100 file limit)
      project_with_many_files = create(:project, user: user_a)
      
      150.times do |i|
        project_with_many_files.private_files.attach(
          io: StringIO.new("File content #{i}"),
          filename: "file_#{i}.pdf",
          content_type: "application/pdf"
        )
      end
      
      sign_in user_a
      
      # Attempt to find a file should return nil due to DoS protection
      random_hash = SecureRandom.hex(16)
      
      expect {
        post request_file_token_project_path(project_with_many_files),
             params: { file_hash: random_hash },
             headers: { 'Content-Type' => 'application/json' }
      }.to change { 
        Rails.logger.messages.count { |msg| msg.include?("DoS Protection") } 
      }.by(1)
      
      expect(response).to have_http_status(:not_found)
    end
    
    it "maintains performance within acceptable limits" do
      # Create project with 99 files (under the limit)
      project_within_limits = create(:project, user: user_a)
      
      99.times do |i|
        project_within_limits.private_files.attach(
          io: StringIO.new("File content #{i}"),
          filename: "file_#{i}.pdf",
          content_type: "application/pdf"
        )
      end
      
      sign_in user_a
      
      # Test that search completes within reasonable time
      file_hash = project_within_limits.generate_secure_file_hash(
        project_within_limits.private_files.first
      )
      
      start_time = Time.current
      
      post request_file_token_project_path(project_within_limits),
           params: { file_hash: file_hash },
           headers: { 'Content-Type' => 'application/json' }
      
      end_time = Time.current
      
      expect(response).to have_http_status(:ok)
      expect(end_time - start_time).to be < 1.second
    end
  end
end
```

### **Browser Security Testing**

#### **XSS Prevention Validation**
```ruby
# spec/requests/xss_prevention_spec.rb

require 'rails_helper'

RSpec.describe "XSS Prevention", type: :request do
  include_context "authorization test setup"
  
  describe "Content Security Policy" do
    it "sets restrictive CSP headers for inline content" do
      sign_in user_a
      file_hash = project_a.generate_secure_file_hash(file_a)
      
      # Get a valid token
      post request_file_token_project_path(project_a),
           params: { file_hash: file_hash },
           headers: { 'Content-Type' => 'application/json' }
      
      token = JSON.parse(response.body)["token"]
      
      # Access the file stream
      get secure_stream_path, params: { t: token }
      
      expect(response.headers['Content-Security-Policy']).to include("default-src 'none'")
      expect(response.headers['X-Content-Type-Options']).to eq('nosniff')
      expect(response.headers['X-Frame-Options']).to eq('DENY')
    end
    
    it "prevents script execution in malicious SVG files" do
      # Create malicious SVG file
      malicious_svg = <<~SVG
        <svg xmlns="http://www.w3.org/2000/svg" onload="alert('XSS')">
          <script>alert('XSS in SVG')</script>
        </svg>
      SVG
      
      project_a.private_files.attach(
        io: StringIO.new(malicious_svg),
        filename: "malicious.svg",
        content_type: "image/svg+xml"
      )
      
      malicious_file = project_a.private_files.last
      
      sign_in user_a
      file_hash = project_a.generate_secure_file_hash(malicious_file)
      
      post request_file_token_project_path(project_a),
           params: { file_hash: file_hash },
           headers: { 'Content-Type' => 'application/json' }
      
      token = JSON.parse(response.body)["token"]
      
      get secure_stream_path, params: { t: token }
      
      # Verify security headers prevent script execution
      expect(response.headers['Content-Security-Policy']).to include("script-src 'none'")
      expect(response.headers['X-Content-Type-Options']).to eq('nosniff')
      expect(response).to have_http_status(:ok)
    end
  end
end
```

## Automated Security Testing

### **CI/CD Integration**
```yaml
# .github/workflows/security_tests.yml

name: Security Tests
on: [push, pull_request]

jobs:
  authorization_security:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup Ruby
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: 3.1.2
          bundler-cache: true
      - name: Setup Database
        run: |
          bin/rails db:create db:migrate
      - name: Run Authorization Security Tests
        run: |
          bundle exec rspec spec/requests/authorization_security_spec.rb
          bundle exec rspec spec/requests/dos_protection_spec.rb
          bundle exec rspec spec/requests/xss_prevention_spec.rb
      - name: Security Headers Check
        run: |
          bundle exec rspec spec/requests/security_headers_spec.rb
```

### **Performance Monitoring**
```ruby
# spec/support/performance_monitor.rb

module PerformanceMonitor
  def self.measure_request_time(&block)
    start_time = Time.current
    result = yield
    end_time = Time.current
    
    {
      result: result,
      duration: end_time - start_time,
      performance_acceptable: (end_time - start_time) < 1.second
    }
  end
end
```

## Security Testing Checklist

### **Pre-deployment Security Validation**
- [ ] IDOR protection tests pass
- [ ] JWT token security validated
- [ ] Hash enumeration prevented
- [ ] Timing attack protection verified
- [ ] XSS prevention headers present
- [ ] DoS protection limits enforced
- [ ] Rate limiting functional
- [ ] Authorization logic validated
- [ ] Error messages don't leak information
- [ ] Performance within acceptable limits

### **Production Security Monitoring**
- [ ] Failed authorization attempts logged
- [ ] Rate limiting violations tracked
- [ ] DoS protection activations monitored
- [ ] Unusual access patterns detected
- [ ] Token validation failures logged
- [ ] Performance degradation alerts configured

## Conclusion

This authorization security testing framework provides comprehensive validation of the secure file display system's security posture. It covers all critical attack vectors including IDOR, JWT tampering, hash enumeration, timing attacks, XSS, and DoS scenarios.

The framework should be run:
- Before every deployment
- After any security-related code changes
- As part of regular security audits
- When onboarding new team members

Regular execution of these tests ensures the system maintains its security integrity over time and prevents regression of security fixes.