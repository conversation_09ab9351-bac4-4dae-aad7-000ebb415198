# User Invitations Feature

## Overview

The Unlisters platform implements an invitation-only user registration system using the `devise_invitable` gem. This feature allows existing users to invite new members to join the platform, creating a curated community of professionals and investors.

## Key Components

### Invitation Flow

1. **Sender Initiates**: An authenticated user enters the recipient's email address
2. **System Check**: The system verifies if the email is already registered
   - If registered: Creates a connection request instead
   - If new: Sends an invitation email
3. **Email Dispatch**: Personalized invitation email sent with sender's name
4. **Recipient Action**: Recipient clicks the invitation link
5. **Account Creation**: Recipient sets password and completes profile
6. **Automatic Connection**: Network connection established between inviter and invitee

### Personalized Email Subject

The invitation emails include the sender's name in the subject line for a more personal touch:

**Format**: `"[Sender's First Name] [Sender's Last Name] invites you to join the Unlisters Network"`

**Example**: `"<PERSON> invites you to join the Unlisters Network"`

This personalization is achieved through Rails I18n interpolation, pulling the sender's name from their user profile (`current_user.profile.first_name` and `current_user.profile.last_name`).

### Multi-language Support

The invitation system supports multiple languages:
- Slovak (sk) - Default
- English (en) - Fallback

Users can select the language for the invitation email when sending it, ensuring recipients receive communications in their preferred language.

## Technical Implementation

### Controllers

**InvitationsController** (`app/controllers/invitations_controller.rb`)
- Handles invitation creation and management
- Differentiates between new users (invitation) and existing users (connection request)
- Passes sender's profile information to the mailer

### Mailer Configuration

The system uses a custom Devise mailer to enable personalization:
- **Custom Mailer Class**: `app/mailers/custom_devise_mailer.rb`
- Overrides `invitation_instructions` method to fetch inviter's profile data
- Manually generates personalized subject using I18n interpolation
- Passes inviter information as instance variables to email templates

### Locale Files

- **English**: `config/locales/devise_invitable.en.yml`
- **Slovak**: `config/locales/devise_invitable.sk.yml`

### Email Templates

**HTML Template** (`app/views/devise/mailer/invitation_instructions.html.erb`)
- Styled HTML email with personalized header
- Uses instance variables `@inviter_first_name` and `@inviter_last_name`
- Includes invitation acceptance link and feature highlights

**Text Template** (`app/views/devise/mailer/invitation_instructions.text.erb`)
- Plain text version for email clients that don't support HTML
- Contains same personalization and invitation details
- Maintains consistent messaging across formats

### Views

**Invitation Form** (`app/views/network_connections/_invite_users.html.erb`)
- Email input field
- Language selector dropdown
- Displays list of sent invitations with status

### Models

**User Model** (`app/models/user.rb`)
- Configured with `devise :invitable`
- Automatically creates network connections upon invitation acceptance
- Tracks invitation status and timestamps

**UserProfile Model** (`app/models/user_profile.rb`)
- Contains `first_name` and `last_name` fields used for personalization
- Associated with User model via `belongs_to :user`

## Email Preview Feature

Developers and administrators can preview invitation emails without sending them:

**URLs**: 
- HTML English: `/rails/mailers/custom_devise_mailer/invitation_instructions.html?locale=en`
- HTML Slovak: `/rails/mailers/custom_devise_mailer/invitation_instructions.html?locale=sk`
- Text English: `/rails/mailers/custom_devise_mailer/invitation_instructions.text?locale=en`
- Text Slovak: `/rails/mailers/custom_devise_mailer/invitation_instructions.text?locale=sk`

**Preview Configuration**: `test/mailers/previews/custom_devise_mailer_preview.rb`

This allows for:
- Visual verification of email formatting in both HTML and text versions
- Testing of personalization variables with mock inviter data
- Language switching via locale parameter
- Verification of interpolated inviter names in subject and body

## Security Considerations

1. **Invitation-Only Registration**: When enabled via `ENV['INVITE_ONLY']`, only invited users can register
2. **Token Expiration**: Invitation tokens expire after a configured period
3. **Rate Limiting**: Consider implementing rate limits on invitation sending
4. **Email Validation**: Email addresses are validated before sending invitations

## Best Practices

1. **User Experience**
   - Clear feedback when invitation is sent
   - Status tracking for pending invitations
   - Resend capability for expired invitations

2. **Data Privacy**
   - Only show recipient email addresses to the inviter
   - Don't expose user presence through invitation flow

3. **Monitoring**
   - Track invitation conversion rates
   - Monitor for invitation abuse
   - Log failed invitation attempts

## Future Enhancements

Potential improvements to consider:

1. **Bulk Invitations**: Allow users to invite multiple people at once
2. **Invitation Templates**: Customizable invitation messages
3. **Social Proof**: Show mutual connections in invitation emails
4. **Analytics Dashboard**: Track invitation metrics for admins
5. **Mobile App Support**: Deep linking for mobile invitation acceptance