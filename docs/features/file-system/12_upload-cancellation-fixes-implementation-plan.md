# Upload Cancellation Fixes Implementation Plan

## Overview
This plan implements a realistic approach for a startup app that handles upload cancellation honestly - allowing cancellation only when technically possible, while ensuring uploads complete reliably even if the user closes their browser.

## Core Principle: Honest UX
- Users can cancel uploads before S3 transfer begins
- Once S3 upload starts, it cannot be stopped (show "Uploading to cloud...")
- Uploads complete in background even if browser closes
- Users can delete unwanted files after upload completes

## Phase 1: Fix Cancel Button Behavior (Priority: CRITICAL)

### Task 1.1: Update Can Cancel Logic
**File**: `app/models/upload.rb`
```ruby
def can_cancel?
  # Only allow cancel before S3 upload starts
  # Remove 'processing' from this list
  pending? || transferred?
end
```

### Task 1.2: Remove File Cleanup from Controller
**File**: `app/models/upload.rb`
```ruby
def mark_cancelled!
  raise InvalidStateTransition, "Cannot cancel completed upload" if completed?
  update!(status: :cancelled)
  # REMOVE: cleanup_temp_file! - Let job handle its own file
end
```
**Reason**: Prevents race condition where controller deletes file while job is reading it.

### Task 1.3: Update Frontend Cancel Button
**File**: `app/frontend/js/upload_handler.js`
```javascript
updateUploadProgress(data) {
  const uploadElement = document.getElementById(`upload-${data.id}`);
  if (!uploadElement) return;

  // ... existing progress update code ...

  // Update cancel button based on status
  const cancelBtn = uploadElement.querySelector('.cancel-upload-btn');
  if (cancelBtn) {
    if (data.status === 'processing') {
      // Hide cancel button during S3 upload
      cancelBtn.style.display = 'none';
      
      // Show informative message
      const statusText = uploadElement.querySelector('.upload-status');
      if (statusText) {
        statusText.textContent = 'Uploading to cloud...';
        statusText.classList.add('upload-status-processing');
      }
    } else if (data.status === 'pending' || data.status === 'transferred') {
      // Keep cancel button visible
      cancelBtn.style.display = 'block';
    }
  }
}
```

## Phase 2: Fix WebSocket Race Condition (Priority: HIGH)

### Task 2.1: Remove 2-Second Delay
**File**: `app/controllers/uploads_controller.rb`
```ruby
# Change this:
FileUploadJob.set(wait: 2.seconds).perform_later(upload.id, persistent_temp_path)

# To this:
FileUploadJob.perform_later(upload.id, persistent_temp_path)
```

### Task 2.2: Add Resilient Progress Tracking
**File**: `app/frontend/js/upload_handler.js`
```javascript
subscribeToUploadProgress(upload) {
  if (window.App && window.App.cable) {
    const subscription = window.App.cable.subscriptions.create(
      {
        channel: 'UploadChannel',
        upload_signed_id: upload.signed_id
      },
      {
        received: (data) => {
          upload.lastUpdateTime = Date.now();
          upload.isStalled = false;
          this.updateUploadProgress(data);
        },
        
        connected: () => {
          console.log(`Connected to upload ${upload.id}`);
          // Immediately poll to catch any missed updates
          setTimeout(() => this.pollUploadStatus(upload), 100);
        },
        
        disconnected: () => {
          console.log(`Disconnected from upload ${upload.id}`);
          // Fall back to polling
          this.startPollingUpload(upload);
        }
      }
    );

    upload.subscription = subscription;
  } else {
    // No WebSocket available, use polling
    this.startPollingUpload(upload);
  }
}

// Add status endpoint polling as backup
async pollUploadStatus(upload) {
  try {
    const response = await fetch(`/uploads/${upload.id}`);
    if (response.ok) {
      const data = await response.json();
      this.updateUploadProgress(data);
    }
  } catch (error) {
    console.error(`Status poll error for upload ${upload.id}:`, error);
  }
}
```

### Task 2.3: Ensure Job Handles Early Cancellation
**File**: `app/jobs/file_upload_job.rb`
```ruby
def perform(upload_id, temp_file_path)
  @upload = Upload.find(upload_id)
  @temp_file_path = temp_file_path
  
  # Check if cancelled before we start
  if @upload.cancelled?
    Rails.logger.info "Upload #{@upload.id} was cancelled before processing"
    cleanup_temp_file
    return
  end
  
  # Mark as transferred (file ready for S3)
  @upload.mark_transferred!
  
  # Check again after state change
  @upload.reload
  if @upload.cancelled?
    Rails.logger.info "Upload #{@upload.id} was cancelled after transfer"
    cleanup_temp_file
    return
  end
  
  # Now mark as processing - no more cancellation allowed
  @upload.mark_processing!
  
  # Continue with S3 upload...
end
```

## Phase 3: Handle Browser Close Gracefully (Priority: HIGH)

### Task 3.1: Ensure Background Jobs Complete
**Current behavior is already correct** - GoodJob runs in separate process and continues even if browser closes. No changes needed.

### Task 3.2: Show Completed Uploads on Return
**File**: `app/views/projects/_form.html.erb`
Add clear status indicators for files:
```erb
<% @project.private_files.each do |file| %>
  <div class="file-item" data-file-id="<%= file.id %>">
    <!-- existing file display -->
    
    <div class="file-status">
      <% if file.created_at > 1.hour.ago %>
        <span class="recently-uploaded">New</span>
      <% end %>
    </div>
    
    <!-- Make delete action prominent -->
    <div class="file-actions">
      <%= button_to t('common.actions.delete'), 
          destroy_file_project_path(@project, file_id: file.id),
          method: :delete,
          class: "delete-btn",
          data: { confirm: t('projects.form.delete_confirm') } %>
    </div>
  </div>
<% end %>
```

## Phase 4: Add Safety Nets (Priority: MEDIUM)

### Task 4.1: S3 Lifecycle Rule for True Orphans
**AWS S3 Configuration**:
```yaml
Rule Name: cleanup-failed-uploads
Status: Enabled
Prefix: uploads/
Conditions:
  - Objects older than 7 days
  - No corresponding ActiveStorage::Blob record
Actions:
  - Permanently delete
```

### Task 4.2: Add Upload Status Visibility
**File**: `app/controllers/uploads_controller.rb`
```ruby
# Add endpoint to check upload status
def show
  @upload = current_user.uploads.find(params[:id])
  
  render json: {
    id: @upload.id,
    status: @upload.status,
    progress_percentage: @upload.progress_percentage,
    original_filename: @upload.original_filename,
    can_cancel: @upload.can_cancel?,
    created_at: @upload.created_at,
    message: status_message(@upload)
  }
end

private

def status_message(upload)
  case upload.status
  when 'pending'
    'Preparing upload...'
  when 'transferred'
    'Ready to upload...'
  when 'processing'
    'Uploading to cloud (cannot cancel)...'
  when 'completed'
    'Upload complete!'
  when 'cancelled'
    'Upload cancelled'
  when 'failed'
    'Upload failed'
  end
end
```

## Implementation Status

### ✅ COMPLETED: Simple Background Upload Notification
- [x] **Added upload count query** - Backend counts active uploads for project
- [x] **Simple notification display** - Shows "X uploads being processed in the background"
- [x] **Non-interactive approach** - No WebSockets, no complex restoration, just information
- [x] **Styled notification** - Clean, informative blue notification box
- [x] **Zero complexity** - Doesn't break existing logic, simple page refresh shows status

### ✅ COMPLETED: Stuck Upload Detection and Manual Cleanup
**Approach**: Startup-optimized simple solution with manual user cleanup

#### **Problem Analysis**:
- **Root Cause**: Upload 23 stuck in 'transferred' status since June 25 (11+ days)
- **User Impact**: Confusing "1 upload being processed in the background" notification
- **Scale Context**: 20-user startup, ~50-100 uploads/month maximum

#### **✅ Solution Implementation** (Startup-Optimized):

**✅ Backend Implementation**:
- [x] **Added 'aborted' status** to Upload enum (`app/models/upload.rb:30`)
- [x] **Added stuck detection logic** - `stuck?` method with 1+ hour timeout (`upload.rb:143-145`)
- [x] **Added manual cleanup method** - `cleanup_stuck_upload!` with safety checks (`upload.rb:148-161`)
- [x] **Added controller action** - `cleanup_stuck` with authorization and validation (`uploads_controller.rb:69-89`)
- [x] **Added route** - `POST /uploads/:id/cleanup_stuck` (`config/routes.rb:78`)
- [x] **Updated query logic** - excluded aborted uploads from notification count (`projects_controller.rb:160`)

**✅ Frontend Implementation**:
- [x] **Enhanced notification display** - shows stuck upload count and cleanup button (`_form.html.erb:152-164`)
- [x] **Added cleanup JavaScript** - `cleanupStuckUploads()` function with error handling (`_form.html.erb:411-445`)
- [x] **User feedback** - loading states, error handling, and page refresh

**✅ Core Logic**:
```ruby
# Simple time-based detection (no job queue complexity)
def stuck?
  (transferred? || processing?) && updated_at < 1.hour.ago
end

# Manual user-initiated cleanup with comprehensive safety
def cleanup_stuck_upload!
  return false unless stuck?
  
  Rails.logger.info "Cleaning up stuck upload #{id} (#{original_filename}) - stuck since #{updated_at}"
  update!(status: :aborted)
  cleanup_temp_file! if temp_file_exists?
  Rails.logger.info "Successfully cleaned up stuck upload #{id}"
  true
end
```

**✅ What Gets Cleaned Up**:
- ✅ **Database state**: Upload status → 'aborted'
- ✅ **Temp files**: Remove orphaned temp files if they exist
- ❌ **S3 objects**: Let AWS lifecycle policies handle (safer)
- ❌ **Background jobs**: No job exists to clean (they're already gone/failed)

**✅ Safety Features Implemented**:
- **Manual trigger only** - user initiates, no automatic cleanup
- **1+ hour buffer** - huge safety margin for normal processing  
- **Authorization checks** - only upload owner can cleanup their uploads
- **Validation** - checks upload is actually stuck before allowing cleanup
- **Idempotent operation** - safe to run multiple times
- **Error handling** - graceful failure with user feedback
- **Status clarity** - 'aborted' vs 'failed' vs 'cancelled' distinctions

**✅ User Experience**:
When user visits project with stuck uploads:
```
📤 1 upload being processed in the background

1 upload appears stuck (no progress for 1+ hour)
[Cleanup Stuck Uploads]
```

**✅ What We DON'T Implement** (Intentionally Simple for Startup):
- ❌ Job queue monitoring (serialized params queries)
- ❌ Race condition handling (active_job_id column)
- ❌ Automatic cleanup jobs (background processing)
- ❌ Complex monitoring/alerting systems

**✅ Result**: Simple time-based detection + manual cleanup solves 99% of the problem with 10% of the complexity. Perfect for 20-user startup context.

### ✅ COMPLETED: Frontend Cancel Button Fixes
- [x] **Added cancel button visibility logic** - Button now hides when upload cannot be cancelled
- [x] **Implemented status-based cancel button control** - Shows only in 'pending' and 'transferred' states
- [x] **Added informative status messages** - "Uploading to cloud (cannot cancel)..." for processing state
- [x] **Improved cancel function error handling** - Better user feedback when cancellation fails
- [x] **Fixed initial button state** - Cancel button properly initialized when upload elements are created

### ✅ COMPLETED: Code Review Fixes (Priority 1 & 2)
- [x] **Fixed failing test for pending uploads** - Updated test to verify pending → completed transition
- [x] **Aligned mark_cancelled! with can_cancel? logic** - Consistent state transition rules  
- [x] **Removed redundant authorization check** - Simplified controller, eliminated duplicate security check
- [x] **Removed debug puts statements** - Cleaned up FileUploadJob for production readiness

### ✅ COMPLETED: Day 1 - Core Cancellation Fix (TDD Approach)
- [x] **Update `can_cancel?` to exclude 'processing'** - Fixed via TDD
  - Tests written first to verify only `pending` and `transferred` states allow cancellation
  - Implementation changed to remove `processing?` from condition
  - Result: Cancel button only works before S3 upload starts (honest UX)

- [x] **Remove `cleanup_temp_file!` from `mark_cancelled!`** - Fixed via TDD
  - Tests written to verify temp file is NOT cleaned up by controller
  - Implementation removed `cleanup_temp_file!` call from `mark_cancelled!`
  - Result: Eliminates race condition where controller deletes file while job reads it

- [x] **Add early cancellation handling to FileUploadJob** - Fixed via TDD
  - Tests written for multiple cancellation scenarios (before start, after transfer)
  - Implementation added guard clauses to detect cancelled uploads and exit gracefully
  - Result: Jobs handle cancellation without attempting S3 upload

### ✅ COMPLETED: Day 2 - WebSocket Race Fix (TDD Approach)
- [x] **Remove 2-second delay** - Completed via TDD
  - Tests written to verify immediate job enqueueing without delays
  - Implementation changed to call `FileUploadJob.perform_later()` immediately
  - Added explicit Action Cable broadcast after upload creation
  - Result: Uploads start processing immediately, no artificial delays

- [ ] Add resilient progress tracking (Future: if WebSocket issues arise in production)
- [ ] Test with slow network connections (Future: manual testing)

### 📋 PENDING: Day 3 - Browser Close Handling
- [ ] Verify background jobs complete when browser closes
- [ ] Add "New" indicator for recent uploads
- [ ] Make delete buttons prominent
- [ ] Test full user journey

### 📋 PENDING: Day 4 - Safety Nets
- [ ] Configure S3 lifecycle rule
- [ ] Add status endpoint
- [ ] Deploy and monitor

## Critical Issues Resolved (TDD Implementation)

### ✅ **Race Condition Eliminated**
The major race condition where controller deleted temp files while background jobs were reading them has been completely resolved:
- Controller no longer touches temp files during cancellation
- Background jobs manage their own file cleanup
- Zero "file not found" errors expected in production

### ✅ **Honest Cancellation UX**  
Users now get predictable, honest feedback about what can/cannot be cancelled:
- Cancel works reliably in `pending` and `transferred` states
- Cancel button disappears during S3 upload with clear "Uploading to cloud..." message
- No more confusion about "long cancel waits"

### ✅ **Early Cancellation Detection**
Background jobs now detect cancellation immediately and exit gracefully:
- Multiple guard clauses prevent unnecessary S3 uploads
- Proper temp file cleanup on early exit
- Reduced resource waste and S3 costs

## Remaining Work

### ✅ **WebSocket Race Condition Resolved**
The 2-second delay has been completely removed. Jobs now start immediately and proper Action Cable broadcasting ensures real-time updates without artificial delays.

### 📋 **Browser Close Handling** (Lower Priority)
Current behavior is already correct - jobs complete in background. Need UI improvements to show completed uploads.

### 📋 **S3 Orphan Cleanup** (Safety Net)
Configure lifecycle rules to automatically clean up orphaned S3 objects.

## What This Doesn't Solve (By Design)

1. **In-flight S3 Cancellation**: We accept this limitation
2. **Resource Waste**: Cancelled uploads may complete (rare, acceptable)
3. **Instant Everything**: Some operations take time (that's OK)

## Success Metrics

- Zero "file not found" errors in logs
- Cancel button hidden during S3 upload
- All uploads complete even with browser closes
- Users successfully delete unwanted files
- No increase in support tickets

## Timeline

- Day 1: Core cancellation fix
- Day 2: WebSocket race condition fix  
- Day 3: Browser close handling
- Day 4: Safety nets and monitoring

Total: 4 days implementation

## Future Considerations

If upload cancellation becomes a frequent user need:
1. Consider multipart uploads for files > 100MB
2. Add upload pause/resume functionality
3. Show time estimates for large uploads

But for now, this simple, honest approach is the right solution.