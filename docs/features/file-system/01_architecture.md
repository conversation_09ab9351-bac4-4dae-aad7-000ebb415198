# File System Architecture

## File Name Processing Architecture

**Rails Built-in Check**: File naming IS available in ActiveStorage framework ✅

### Current System
- **Original Filenames**: Preserved in `ActiveStorage::Blob.filename` 
- **S3 Storage Keys**: ✅ **IMPLEMENTED**: Now include file extensions (e.g., `ufgj2p0eje2ooqy21d167jn9j0et.pdf`)
- **Display Names**: Original filename for authorized users, generic pattern for unauthorized
- **Thumbnail Names**: Pattern `thumb_[9-char-hash]_[basename].png`

### File Format Suffix Support
✅ **WORKING** via `before_create` callback on `ActiveStorage::Blob`

### Key Implementation Details
- **Upload Method**: Server-side form uploads (Rails handles all file processing)
- **S3 Key Generation**: `config/initializers/active_storage_key_extension.rb` 
- **Implementation**: `before_create` callback appends extension to Rails-generated key
- **Process Flow**: Form upload → Rails creates blob → Callback adds extension → S3 upload with extended key

### Key Files
- **S3 Key Extension**: `config/initializers/active_storage_key_extension.rb` (callback-based extension)
- **Display Logic**: `app/models/concerns/secure_file_access.rb:132` (display_name generation)
- **Thumbnail Logic**: `app/jobs/thumbnail_generation_job.rb:58` (thumbnail filename creation)
- **Helper Methods**: `app/helpers/projects_helper.rb` (URL generation)

### Benefits
- **Lambda Compatibility**: S3 keys include extensions for AWS Lambda triggers
- **AWS Service Integration**: Better compatibility with external tools and services
- **Debugging**: Easier S3 bucket inspection and manual file identification
- **Security**: No impact (file type already available via content_type)

### Testing
Use `ruby test_s3_keys.rb` to verify new uploads have extensions

## Core Components

### File Proxy Controller
`app/controllers/file_proxy_controller.rb` - Handles inline viewing, downloads, thumbnails

### File Security
`app/models/concerns/secure_file_access.rb` - Hashing + token system

### Frontend Integration  
`app/frontend/entrypoints/application.js` - InlineFileViewer + SecureFileViewer classes

### View Templates
`app/views/projects/_full_details_project.html.erb` - File grid with thumbnails

### Helper Methods
`app/helpers/projects_helper.rb` - URL generation and file type support