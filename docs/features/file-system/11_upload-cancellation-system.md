# Upload Cancellation System

## Overview
This document analyzes the upload cancellation feature, detailing how the system handles user-initiated cancellations and manages temporary file cleanup on <PERSON><PERSON>'s disk storage.

## Cancel Button Flow

### 1. Frontend Trigger (upload_handler.js)
When user clicks cancel:
```javascript
async cancelUpload(uploadId) {
  // DELETE request to /uploads/:id
  const response = await fetch(`/uploads/${uploadId}`, {
    method: 'DELETE',
    headers: { 'X-CSRF-Token': token }
  });
  
  // Cleanup UI and subscriptions
  this.cleanupUploadSubscription(upload);
  this.activeUploads.delete(uploadId);
  this.removeUploadElement(uploadId);
}
```

### 2. Controller Processing (UploadsController#destroy)
```ruby
def destroy
  unless @upload.can_cancel?
    render json: { errors: ["Upload cannot be cancelled in #{@upload.status} state"] }
    return
  end
  
  @upload.mark_cancelled!
  render json: { message: "Upload cancelled successfully" }
end
```

### 3. Model State Management (Upload#mark_cancelled!)
```ruby
def mark_cancelled!
  raise InvalidStateTransition, "Cannot cancel completed upload" if completed?
  update!(status: :cancelled)
  cleanup_temp_file!  # Immediate cleanup
end

def can_cancel?
  pending? || transferred? || processing?
end
```

### 4. Temp File Cleanup
```ruby
def cleanup_temp_file!
  return unless temp_file_path.present?
  
  begin
    File.delete(temp_file_path) if File.exist?(temp_file_path)
    update_column(:temp_file_path, nil)  # Clear DB reference
  rescue => e
    Rails.logger.error "Failed to cleanup temp file #{temp_file_path}: #{e.message}"
  end
end
```

## Render Disk Storage Behavior

### Storage Location
- **Path**: `ENV['RENDER_DISK_UPLOAD_PATH']` or default `/tmp/uploads/`
- **Pattern**: `YYYYMMDDHHMMSS_[random].[ext]`
- **Persistence**: Files persist until explicitly deleted

### Key Characteristics
1. **Synchronous Deletion**: Files are deleted immediately, not deferred
2. **Database Cleanup**: `temp_file_path` nullified to prevent orphaned references
3. **Idempotent Operations**: Multiple cleanup attempts are safe
4. **Error Resilience**: Failures logged but don't break the flow

## Background Job Interaction

### FileUploadJob Cleanup
```ruby
ensure
  # Always cleanup temp file
  if @temp_file_path && File.exist?(@temp_file_path)
    FileUtils.rm_f(@temp_file_path)
    Rails.logger.info "Cleaned up temp file: #{@temp_file_path}"
  end
end
```

### Race Condition Handling
- **Double Cleanup**: Both cancel and job cleanup check file existence
- **Cancel During Upload**: S3 upload may continue but result is discarded
- **State Validation**: Jobs check upload status before processing

## Potential Issues & Edge Cases

### 1. Cancel During S3 Upload
**Issue**: User experiences delay when cancelling during active S3 upload
**Cause**: S3 upload is not interruptible once started
**Impact**: Cancel appears slow, file still uploads to S3

### 2. WebSocket Disconnection
**Issue**: Cancel request succeeds but UI doesn't update
**Cause**: Action Cable connection lost
**Impact**: User sees stale progress indicators

### 3. Temp File Orphaning
**Scenarios**:
- Process crash after file save but before DB update
- Network partition during cancellation
- Disk full preventing deletion

### 4. State Machine Edge Cases
- Cancel request during state transition
- Multiple concurrent cancel requests
- Cancel after job retry exhaustion

## Observed Issues

### "Long Cancel Wait" Problem
**User Report**: "Hitting cancel and waiting long time like the file had to be uploaded first"

**Root Causes Identified**:

#### 1. Non-Interruptible S3 Upload
- AWS SDK's `s3_object.put()` is a blocking, atomic operation
- No mechanism to check cancellation status during transfer
- Once started, entire file uploads regardless of cancel status
- Job only checks cancelled flag AFTER S3 upload completes

#### 2. File Handle Persistence (Unix Behavior)
- Controller's `File.delete()` only removes directory entry
- Job's open file handle remains valid (inode persists)
- Job continues reading from "ghost" file and uploads to S3
- Disk space reclaimed only after job closes file handle

#### 3. 2-Second Job Delay Race Condition
The `FileUploadJob.set(wait: 2.seconds)` creates inconsistent behavior:

**Fast Cancel (< 2 seconds)**:
- Job hasn't started yet
- Sees cancelled status immediately
- Never uploads to S3
- Appears instant to user

**Slow Cancel (> 2 seconds)**:
- Job already in S3 upload
- Must complete entire upload
- Only then checks cancelled status
- User waits minutes for "cancellation"

**Current Behavior**:
- Cancel updates DB status immediately
- Temp file "deleted" (but handle persists)
- S3 upload continues to completion
- Results in orphaned S3 objects
- User perceives long delay with no feedback

## Architectural Solutions

### 1. Implement Multipart Upload for Cancellation
Replace atomic `s3_object.put()` with interruptible multipart upload:
```ruby
multipart_upload = s3_object.initiate_multipart_upload
parts = []

File.open(@temp_file_path, 'rb') do |file|
  part_number = 1
  while chunk = file.read(5.megabytes)
    # Check cancellation before each part
    if @upload.reload.cancelled?
      s3_object.abort_multipart_upload(upload_id: multipart_upload.upload_id)
      raise UploadCancelled
    end
    
    part = s3_object.upload_part(
      body: chunk,
      upload_id: multipart_upload.upload_id,
      part_number: part_number
    )
    parts << { etag: part.etag, part_number: part_number }
    part_number += 1
    
    # Update progress after each chunk
    progress = ((part_number - 1) * 5.0 / (file.size / 1.megabyte)) * 100
    @upload.update_progress!(progress.clamp(0, 99))
  end
end

s3_object.complete_multipart_upload(
  upload_id: multipart_upload.upload_id,
  multipart_upload: { parts: parts }
)
```

### 2. Fix Race Conditions
- **Remove file cleanup from controller** - let job handle its own temp file
- **Remove 2-second delay** - fix WebSocket race properly
- **Add cooperative cancellation** - job checks status during operations

### 3. Prevent Orphaned S3 Objects

#### Option A: S3 Lifecycle Rules (Recommended)
```yaml
# S3 Bucket Configuration
- Upload to: uploads/pending/[timestamp]/[file]
- On success: Move to uploads/completed/[timestamp]/[file]
- Lifecycle rule: Delete uploads/pending/* after 24 hours
```

#### Option B: S3 Object Tagging
```ruby
# Tag during upload
s3_object.put(
  body: file,
  metadata: { 
    'upload-id' => @upload.id,
    'status' => 'pending'
  }
)

# Cleanup rake task
namespace :uploads do
  task cleanup_orphaned: :environment do
    s3_bucket.objects(prefix: 'uploads/').each do |object|
      if object.metadata['status'] == 'pending'
        upload = Upload.find_by(id: object.metadata['upload-id'])
        if upload.nil? || upload.cancelled? || object.last_modified < 24.hours.ago
          object.delete
        end
      end
    end
  end
end
```

### 4. Improve User Feedback
- Add "Cancelling..." state with spinner
- Show estimated time remaining for cancellation
- Disable cancel button if job hasn't started yet
- Add toast notification when cancellation completes

## Security Considerations

1. **Temp File Access**: Files in `/tmp/uploads/` need proper permissions
2. **Path Traversal**: Temp filename generation prevents directory escape
3. **Resource Exhaustion**: Rate limiting prevents disk fill attacks
4. **Abandoned Files**: No automatic cleanup could fill disk over time

## Critical Design Flaws Summary

1. **Non-Cooperative Cancellation**: S3 upload cannot be interrupted
2. **Race Condition**: Controller deletes file while job is reading it
3. **Orphaned S3 Objects**: No cleanup for partially uploaded files
4. **Inconsistent UX**: 2-second delay causes unpredictable behavior
5. **Resource Waste**: Cancelled uploads complete anyway

## Testing Recommendations

1. **Cancel Timing Tests**
   - Cancel within 2-second window (before job starts)
   - Cancel during S3 transfer (large file)
   - Cancel after completion attempt
   - Measure actual cancellation time vs file size

2. **Failure Scenarios**
   - File deleted while job is reading
   - Process crash during cancel
   - S3 network timeout during multipart abort
   - Disk full preventing cleanup

3. **Concurrency Tests**
   - Multiple cancels on same upload
   - Cancel during job retry
   - Cancel with WebSocket disconnect
   - Rapid upload/cancel cycles

4. **Orphan Detection**
   - List S3 objects without DB records
   - Verify lifecycle rules work
   - Test cleanup rake task