# File System Documentation

## Overview

This directory contains comprehensive documentation for the Unlisters App file system architecture, including file uploads, storage, thumbnails, and security.

## Documentation Structure

- **[01_architecture.md](./01_architecture.md)** - File name processing, S3 key extensions, and core architecture
- **[02_lambda-integration.md](./02_lambda-integration.md)** - Lambda thumbnail generation system
- **[03_server-side-upload.md](./03_server-side-upload.md)** - Server-side upload with Ready Flag pattern
- **[04_critical-lessons.md](./04_critical-lessons.md)** - Critical implementation patterns and UX lessons

## Primary References

For quick access to complete file system documentation:
- **Master Guide**: [`SECURE_INLINE_FILE_DISPLAY_IMPLEMENTATION_PLAN.md`](../../../SECURE_INLINE_FILE_DISPLAY_IMPLEMENTATION_PLAN.md)
- **Security Documentation**: [`docs/security/THREAT_MODEL.md`](../../security/THREAT_MODEL.md)
- **Testing Guide**: [`SECURE_FILE_TESTING_GUIDE.md`](../../../SECURE_FILE_TESTING_GUIDE.md)

## Quick Commands

```bash
# Test file system functionality
bundle exec rspec spec/requests/*security*

# Check file extensions in S3
ruby test_s3_keys.rb

# Generate thumbnails manually
bin/rails runner "PdfThumbnailGenerationJob.perform_now(Project.find(1))"
```