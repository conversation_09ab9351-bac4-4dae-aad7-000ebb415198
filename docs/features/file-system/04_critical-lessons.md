# Critical Implementation Lessons (June 2025)

## 🚨 DO NOT BREAK THESE WORKING PATTERNS

### 1. UX Pattern - NO Modal/Lightbox
- Users expect: `Thumbnail Click` → `Inline Display Below Files`
- **NEVER** use modal/lightbox - creates friction
- Inline viewer container: `#inline-file-viewer` (already in template)

### 2. JavaScript Event Binding

```javascript
// CORRECT - Triggers InlineFileViewer
data-action="inline"
data-inline-url="<%= safe_inline_url(project, file) %>"
class="file-thumbnail"

// WRONG - Triggers SecureFileViewer (modal)
data-action="preview"
```

### 3. Dual System Architecture
- **Inline Viewing**: Direct file proxy URLs (`/projects/:id/files/:file_id/inline`)
- **Downloads**: Secure token system (`/secure/stream?t=TOKEN`)
- **DO NOT** mix these systems or force tokens for inline viewing

### 4. Thumbnail Generation Timing
- **DO NOT** add delays to jobs (was causing 30-second wait)
- Use resilient job that checks file existence before processing
- pdftoppm creates `-001.png` files, NOT `-1.png` (critical bug)

### 5. Data Attributes Required

```erb
<!-- File items need ALL these attributes -->
data-file-id="<%= file.id %>"
data-file-hash="<%= project.generate_secure_file_hash(file) %>"
data-content-type="<%= file.content_type %>"
data-project-id="<%= project.id %>"
```

## Historical Context

These patterns were established after extensive debugging sessions and represent hard-won stability. Any modifications should be thoroughly tested against the existing test suite before implementation.