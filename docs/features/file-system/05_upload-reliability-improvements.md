# Upload System Reliability Improvements

## Overview

This document outlines critical improvements needed for the server-side file upload system to ensure reliability, especially for large files and slow network connections. These enhancements maintain the security-first approach while dramatically improving user experience.

Direct upload will never be used in this app due to security concerns and until the files will be uploaded to S3 - current system is THE MAIN system we will use and we MUST enhance and tweak it to ensure good user experience.

## Identified Critical Issues

### 1. Race Condition: Background Job vs WebSocket Connection
**Problem**: FileUploadJob often executes and broadcasts status updates before the client's WebSocket subscription is established, causing the "stuck at 100%" issue.

**Impact**: Users see uploads freeze at "Processing..." with no further updates, especially for 10MB+ files.

### 2. Blocking I/O in Upload Controller
**Problem**: `FileUtils.cp` in the controller blocks the web worker thread for several seconds on large files.

**Impact**: Request timeouts, poor concurrent upload performance, potential 502 errors.

### 3. Misleading Progress Indicators
**Problem**: Progress bar shows 100% when only client→server transfer is complete, not the full upload process.

**Impact**: Users confused when upload appears complete but file isn't ready.

### 4. No Recovery Mechanisms
**Problem**: No WebSocket reconnection, no timeout detection, no fallback when broadcasts missed.

**Impact**: Uploads fail silently, users must refresh page to check status.

### 5. Insufficient S3 Client Configuration
**Problem**: Default AWS SDK timeouts too short for large files on slow connections.

**Impact**: Silent failures during S3 upload phase.

## Implementation Plan

### Phase 1: Immediate Fixes (2-4 hours)

#### 1.1 WebSocket Reconnection with Exponential Backoff
```javascript
// app/frontend/js/upload_handler.js
// Add to the subscription callbacks:

disconnected: () => {
  console.log(`Disconnected from upload ${upload.id}, attempting reconnect...`);
  this.handleWebSocketDisconnect(upload);
}

// New method:
handleWebSocketDisconnect(upload) {
  upload.reconnectAttempts = (upload.reconnectAttempts || 0) + 1;
  
  // Exponential backoff: 1s, 2s, 4s, 8s, 16s, max 30s
  const delay = Math.min(30000, Math.pow(2, upload.reconnectAttempts - 1) * 1000);
  
  upload.reconnectTimer = setTimeout(() => {
    console.log(`Reconnect attempt ${upload.reconnectAttempts} for upload ${upload.id}`);
    
    // Clean up old subscription before reconnecting
    if (upload.subscription) {
      upload.subscription.unsubscribe();
    }
    
    // Reconnect
    this.subscribeToUploadProgress(upload);
  }, delay);
}
```

#### 1.2 Client-Side Watchdog Timer
```javascript
// app/frontend/js/upload_handler.js
// Add to trackUpload method:

trackUpload(upload) {
  this.activeUploads.set(upload.id, upload);
  this.createUploadProgressElement(upload);
  this.subscribeToUploadProgress(upload);
  
  // Start watchdog timer
  this.startUploadWatchdog(upload);
}

// New methods:
startUploadWatchdog(upload) {
  upload.lastUpdateTime = Date.now();
  upload.watchdogTimer = setInterval(() => {
    const timeSinceUpdate = Date.now() - upload.lastUpdateTime;
    
    if (timeSinceUpdate > 30000 && !upload.isStalled) {
      this.handleStalledUpload(upload);
    }
  }, 5000);
}

handleStalledUpload(upload) {
  upload.isStalled = true;
  console.warn(`Upload ${upload.id} appears stalled, attempting recovery...`);
  
  // Update UI to show stalled state
  const statusElement = document.querySelector(`#upload-${upload.id} .upload-status`);
  if (statusElement) {
    statusElement.textContent = 'Connection lost - retrying...';
    statusElement.classList.add('upload-status-warning');
  }
  
  // Force a status check via polling
  this.pollUploadStatus(upload);
}

// In updateUploadProgress, reset the watchdog:
updateUploadProgress(data) {
  const upload = this.activeUploads.get(data.id);
  if (upload) {
    upload.lastUpdateTime = Date.now();
    upload.isStalled = false;
  }
  // ... rest of method
}
```

#### 1.3 Delayed Job Execution
```ruby
# app/controllers/uploads_controller.rb
# Give WebSocket 2 seconds to establish connection:

def process_file_upload(file)
  # ... existing upload creation code ...
  
  # Delay job execution to allow WebSocket connection
  FileUploadJob.set(wait: 2.seconds).perform_later(upload.id, persistent_temp_path)
  
  Rails.logger.info "Upload #{upload.id} created, job scheduled with 2s delay"
  upload
end
```

### Phase 2: Performance Optimizations (4-6 hours)

#### 2.1 Replace Blocking File Copy with Move
```ruby
# app/controllers/uploads_controller.rb

def process_file_upload(file)
  upload = Upload.create!(
    user: current_user,
    target: @target,
    original_filename: file.original_filename,
    content_type: file.content_type,
    file_size: file.size,
    status: :pending,
    progress_percentage: 0
  )

  # Generate persistent temp path
  temp_filename = UploadUtils.generate_temp_filename(file.original_filename)
  persistent_temp_path = UploadUtils.temp_file_path(temp_filename)
  
  # Ensure directory exists
  FileUtils.mkdir_p(File.dirname(persistent_temp_path))
  
  # Use move instead of copy for better performance
  # This is instant even for large files
  begin
    FileUtils.mv(file.tempfile.path, persistent_temp_path)
  rescue Errno::EXDEV
    # Cross-device move not supported, fall back to copy
    FileUtils.cp(file.tempfile.path, persistent_temp_path)
  end
  
  # Store temp path on upload record for cleanup
  upload.update_column(:temp_file_path, persistent_temp_path)
  
  # Schedule job with delay
  FileUploadJob.set(wait: 2.seconds).perform_later(upload.id, persistent_temp_path)
  
  upload
end
```

#### 2.2 Configure S3 Client Timeouts
```ruby
# app/jobs/file_upload_job.rb

def s3_bucket
  @s3_bucket ||= begin
    aws_key = Rails.env.development? ? :aws_dev : :aws_prod
    
    # Configure client with appropriate timeouts for large files
    s3_client = Aws::S3::Client.new(
      region: Rails.application.credentials.dig(aws_key, :region) || ENV['AWS_REGION'],
      credentials: Aws::Credentials.new(
        Rails.application.credentials.dig(aws_key, :access_key_id) || ENV['AWS_ACCESS_KEY_ID'],
        Rails.application.credentials.dig(aws_key, :secret_access_key) || ENV['AWS_SECRET_ACCESS_KEY']
      ),
      # Timeout configuration for large file uploads
      http_open_timeout: 15,      # Time to establish connection
      http_read_timeout: 120,     # Time to wait for response
      http_idle_timeout: 10,      # Time to wait between packets
      http_continue_timeout: 2,   # Time to wait for 100-continue
      retry_mode: 'adaptive',     # Use adaptive retry mode
      retry_limit: 3              # Retry failed requests
    )
    
    s3_resource = Aws::S3::Resource.new(client: s3_client)
    
    bucket_name = Rails.env.development? ? 
      'app-sierra-a7b3c9e2-f8d1-4e56-9c2a-dev-uploads' : 
      'app-sierra-b1c5e9a3-d7f2-4a68-9b4e-prod-uploads'
    
    s3_resource.bucket(bucket_name)
  end
end
```

### Phase 3: Enhanced User Experience (6-8 hours)

#### 3.1 Multi-Phase Progress Display
```javascript
// app/frontend/js/upload_handler.js

createUploadProgressElement(upload) {
  const container = this.getUploadContainer();
  
  const uploadElement = document.createElement('div');
  uploadElement.id = `upload-${upload.id}`;
  uploadElement.className = 'upload-item upload-pending';
  
  uploadElement.innerHTML = `
    <div class="upload-info">
      <div class="upload-filename">${upload.original_filename}</div>
      <div class="upload-filesize">${this.formatFileSize(upload.file_size)}</div>
    </div>
    
    <!-- Multi-phase progress indicator -->
    <div class="upload-phases">
      <div class="phase phase-upload ${upload.status === 'pending' ? 'active' : ''}">
        <span class="phase-icon">📤</span>
        <span class="phase-text">Upload</span>
      </div>
      <div class="phase phase-transfer ${upload.status === 'transferred' ? 'active' : ''}">
        <span class="phase-icon">💾</span>
        <span class="phase-text">Save</span>
      </div>
      <div class="phase phase-process ${upload.status === 'processing' ? 'active' : ''}">
        <span class="phase-icon">☁️</span>
        <span class="phase-text">Store</span>
      </div>
      <div class="phase phase-complete ${upload.status === 'completed' ? 'active' : ''}">
        <span class="phase-icon">✅</span>
        <span class="phase-text">Done</span>
      </div>
    </div>
    
    <div class="upload-progress">
      <div class="upload-progress-track">
        <div class="upload-progress-bar" style="width: 0%"></div>
      </div>
      <div class="upload-progress-text">0%</div>
    </div>
    
    <div class="upload-status">${this.getEnhancedStatusText(upload.status || 'preparing')}</div>
    
    <div class="upload-actions">
      <button type="button" class="cancel-upload-btn" data-upload-id="${upload.id}">
        ${this.translations.actions.cancel}
      </button>
    </div>
    
    <div class="upload-error" style="display: none;"></div>
    <div class="upload-network-warning" style="display: none;">
      <span class="warning-icon">⚠️</span>
      <span class="warning-text">Slow network detected - upload may take longer</span>
    </div>
  `;
  
  container.appendChild(uploadElement);
}

getEnhancedStatusText(status) {
  const messages = {
    preparing: 'Preparing upload...',
    pending: 'Uploading to server...',
    transferred: 'Saving file...',
    processing: 'Uploading to secure storage...',
    completed: 'Upload complete!',
    failed: 'Upload failed - please try again',
    cancelled: 'Upload cancelled'
  };
  return messages[status] || status;
}
```

#### 3.2 Network Speed Detection
```javascript
// app/frontend/js/upload_handler.js

uploadWithProgress(formData, placeholderUploads) {
  return new Promise((resolve, reject) => {
    const xhr = new XMLHttpRequest();
    let startTime = Date.now();
    let lastLoaded = 0;
    
    xhr.upload.addEventListener('progress', (e) => {
      if (e.lengthComputable) {
        const uploadProgress = Math.round((e.loaded / e.total) * 100);
        const currentTime = Date.now();
        const elapsedSeconds = (currentTime - startTime) / 1000;
        const bytesPerSecond = (e.loaded - lastLoaded) / elapsedSeconds;
        
        // Detect slow network (less than 100KB/s)
        if (bytesPerSecond < 100000 && uploadProgress > 10) {
          placeholderUploads.forEach(upload => {
            this.showSlowNetworkWarning(upload.id);
          });
        }
        
        // Update progress
        placeholderUploads.forEach(upload => {
          this.updatePlaceholderProgress(upload.id, uploadProgress);
        });
        
        lastLoaded = e.loaded;
      }
    });
    
    // ... rest of method
  });
}

showSlowNetworkWarning(uploadId) {
  const warning = document.querySelector(`#upload-${uploadId} .upload-network-warning`);
  if (warning) {
    warning.style.display = 'flex';
  }
}
```

### Phase 4: Monitoring & Recovery (4-6 hours)

#### 4.1 Enhanced Upload Status Endpoint
```ruby
# app/controllers/uploads_controller.rb

def show
  # Include more diagnostic information
  render json: {
    id: @upload.id,
    status: @upload.status,
    progress_percentage: @upload.progress_percentage,
    original_filename: @upload.original_filename,
    file_size: @upload.file_size,
    error_message: @upload.error_message,
    created_at: @upload.created_at,
    updated_at: @upload.updated_at,
    # New diagnostic fields
    time_in_status: Time.current - @upload.updated_at,
    job_enqueued: job_exists_for_upload?(@upload),
    temp_file_exists: @upload.temp_file_exists?
  }
end

private

def job_exists_for_upload?(upload)
  # Check if a job exists in GoodJob queue
  GoodJob::Job.where(
    "serialized_params->>'job_class' = ? AND serialized_params->'arguments'->0 = ?",
    'FileUploadJob',
    upload.id.to_s
  ).where(finished_at: nil).exists?
end
```

#### 4.2 Stale Upload Cleanup Job
```ruby
# app/jobs/stale_uploads_cleanup_job.rb

class StaleUploadsCleanupJob < ApplicationJob
  queue_as :low
  
  def perform
    stale_uploads = Upload.where(
      status: ['pending', 'transferred', 'processing'],
      created_at: ..24.hours.ago
    )
    
    stale_uploads.find_each do |upload|
      Rails.logger.info "Cleaning up stale upload #{upload.id}"
      
      # Clean up temp file
      upload.cleanup_temp_file!
      
      # Mark as failed
      upload.mark_failed!("Upload timed out after 24 hours")
      
      # Notify user if needed
      # UserMailer.upload_failed(upload).deliver_later
    end
    
    Rails.logger.info "Cleaned up #{stale_uploads.count} stale uploads"
  end
end

# Schedule in config/initializers/good_job.rb or similar:
# StaleUploadsCleanupJob.perform_later if defined?(Rails::Server)
```

## CSS Enhancements

```scss
// app/assets/stylesheets/components/_upload_progress.scss

.upload-phases {
  display: flex;
  justify-content: space-between;
  margin: 10px 0;
  
  .phase {
    display: flex;
    flex-direction: column;
    align-items: center;
    opacity: 0.3;
    transition: opacity 0.3s ease;
    
    &.active {
      opacity: 1;
    }
    
    &.active ~ .phase {
      opacity: 0.3;
    }
    
    .phase-icon {
      font-size: 20px;
      margin-bottom: 4px;
    }
    
    .phase-text {
      font-size: 12px;
      color: #666;
    }
  }
}

.upload-network-warning {
  display: none;
  align-items: center;
  padding: 8px;
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 4px;
  margin-top: 8px;
  
  .warning-icon {
    margin-right: 8px;
  }
  
  .warning-text {
    font-size: 14px;
    color: #856404;
  }
}

.upload-status-warning {
  color: #ff6b6b;
  font-weight: 500;
}
```

## Testing Strategy

### Manual Testing Checklist
1. Upload 10MB file on normal connection - verify completes
2. Upload 10MB file, disconnect network during upload - verify reconnection
3. Upload 5 files concurrently - verify all complete
4. Kill background job worker during upload - verify retry mechanism
5. Upload on 3G throttled connection - verify slow network warning

### Automated Tests
```ruby
# spec/jobs/file_upload_job_spec.rb
it 'retries on S3 timeout' do
  allow_any_instance_of(Aws::S3::Object).to receive(:put).and_raise(Aws::S3::Errors::RequestTimeout)
  
  expect {
    FileUploadJob.perform_now(upload.id, temp_file_path)
  }.to raise_error(Aws::S3::Errors::RequestTimeout)
  
  expect(upload.reload.status).to eq('failed')
end
```

## Deployment Considerations

1. **Rolling Deployment**: Deploy frontend changes first, then backend
2. **Feature Flag**: Consider feature flag for watchdog timer during initial rollout
3. **Monitoring**: Add CloudWatch alarms for upload failure rate
4. **Database Migration**: Add index on `uploads.status` for cleanup job performance

## Success Metrics

- Upload success rate > 99% (from current ~85%)
- Average time to "stuck" detection < 30 seconds
- User-reported upload issues reduced by 80%
- Support tickets for "frozen uploads" eliminated

## Future Enhancements

1. **Chunked Uploads**: Split large files into chunks for better resume capability
2. **Background Tab Handling**: Maintain uploads when browser tab backgrounded
3. **Upload Queue Visualization**: Show position in queue during high load
4. **Bandwidth Throttling**: Allow users to limit upload speed
5. **Detailed Progress Logs**: Client-side upload event log for debugging