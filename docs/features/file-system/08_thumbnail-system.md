# Thumbnail System Documentation

## Overview

The Unlisters App implements a **dual-path thumbnail system** that provides automatic thumbnail generation for all uploaded files through AWS Lambda integration, with graceful fallbacks for immediate user feedback.

## System Architecture

### Two-Bucket Design
- **Primary Bucket** (`amazon_uploads`): Stores original files
- **Thumbnail Bucket** (`amazon_thumbnails`): Stores Lambda-generated thumbnails
- **Separation Benefits**: Clean organization, independent scaling, security isolation

### Processing Flow
1. **File Upload** → Original file stored in `amazon_uploads` bucket
2. **S3 Trigger** → Lambda function automatically invoked
3. **Lambda Processing** → Thumbnail generated (PDF→PNG, Image→optimized)
4. **Webhook Notification** → Rails app notified of completion
5. **Attachment** → Thumbnail attached to project via Active Storage

## File Processing Rules

### Supported File Types
- **PDFs**: First page converted to PNG thumbnail
- **Images** (JPG, PNG, etc.): Optimized and resized thumbnails
- **Processing Time**: 3-4 seconds average (includes Lambda cold start)

### S3 Key Structure

**Legacy (v1) - Extension Replacement:**
```
Original: uploads/YYYY/MM/DD/{type}/{hash}.{ext}
Thumbnail: uploads/YYYY/MM/DD/{type}/{hash}.png
```

**Current (v2) - UUID-Based Security (July 2025):**
```
Original: uploads/YYYY/MM/DD/{type}/{hash}.{ext}
Thumbnail: uploads/YYYY/MM/DD/{type}/thumbnails/{uuid}.png
```

**Benefits of v2 Approach:**
- ✅ **Zero Data Exposure**: Pure UUID keys, no filename leakage
- ✅ **100% Collision Prevention**: UUID guarantees uniqueness across all users
- ✅ **GDPR Compliance**: Complete file isolation between users
- ✅ **Backward Compatible**: Existing thumbnails continue to work

### Lambda Integration Points
- **Trigger**: S3 ObjectCreated events on `amazon_uploads` bucket
- **Output**: Processed thumbnails in `amazon_thumbnails` bucket
- **Notification**: Webhook to `/wh/thumb_ready` endpoint

## Display Logic

### Thumbnail Lookup Strategy
**Location**: `app/models/project.rb:thumbnail_for_attachment`

1. **Direct Match**: `{original_key}.png` in thumbnails bucket
2. **Extension Replace**: `{key_without_ext}.png` in thumbnails bucket  
3. **Graceful Fallback**: Returns `nil` if no thumbnail found

### URL Generation
**Location**: `app/helpers/projects_helper.rb:safe_thumbnail_url`

- **With Thumbnail**: Routes to `FileProxyController#thumbnail`
- **Without Thumbnail**: Returns placeholder icon path
- **Security**: All access goes through authorization checks

## User Experience Flow

### Immediate Feedback (0-1 seconds)
- File upload completes instantly
- Upload progress shows "completed" 
- Placeholder icon displayed in file list

### Thumbnail Ready (3-4 seconds)
- Lambda processing completes
- Webhook triggers thumbnail attachment
- Real thumbnail replaces placeholder automatically
- No page refresh required

### Error Handling
- **Lambda Failure**: Placeholder icon remains (graceful degradation)
- **Webhook Failure**: Background retry mechanisms
- **Missing Files**: Secure 404 responses through `FileProxyController`

## Controller Architecture

### Upload Process
**Location**: `app/controllers/uploads_controller.rb`
- Handles multi-file uploads
- Creates `Upload` records with Ready Flag pattern
- Schedules `FileUploadJob` for S3 transfer

### File Serving
**Location**: `app/controllers/file_proxy_controller.rb`
- **Authorization**: Project access checks before file serving
- **Thumbnail Method**: Secure proxy for thumbnail display
- **Download Method**: Secure proxy for file downloads
- **S3 Integration**: Direct streaming from S3 with security

### Webhook Processing
**Location**: `app/controllers/webhooks/thumbnails_controller.rb`
- Receives Lambda completion notifications
- Attaches thumbnails to projects via Active Storage
- **Idempotent Design (v2)**: Handles duplicate webhook calls gracefully
- **Security Enhancement (v2)**: UUID-based thumbnail blob creation
- **Legacy Preservation**: v1 code maintained for debugging/reference

## Background Job System

### File Upload Job
**Location**: `app/jobs/file_upload_job.rb`
- Transfers files from local disk to S3
- Updates upload status throughout process
- Handles S3 upload failures with retries

### Active Storage Integration
- **Analyze Job**: Automatically triggered for metadata extraction
- **Thumbnail Generation**: Disabled (Lambda handles all thumbnails)
- **Service Routing**: Automatic routing to correct S3 buckets

## Security Features

### Authorization Layers
1. **Project Access**: User must have project view permissions
2. **File Proxy**: All files served through authorized controllers
3. **S3 Security**: Presigned URLs with time limits
4. **Webhook Security**: Validates incoming thumbnail notifications

### File Type Validation
- **Upload Restrictions**: Configurable allowed file types
- **Content Type Checking**: MIME type validation
- **Size Limits**: Configurable maximum file sizes

## Performance Characteristics

### Lambda Performance
- **Cold Start**: ~1-2 seconds for first invocation
- **Warm Processing**: ~1-2 seconds for subsequent files
- **Concurrent Processing**: Multiple files processed in parallel

### Rails Performance
- **Non-blocking Uploads**: Files processed asynchronously
- **Efficient Lookups**: Optimized S3 key generation
- **Caching**: Active Storage blob caching

## Monitoring & Debugging

### Log Locations
- **Upload Process**: Rails logs show upload job progression
- **Lambda Processing**: AWS CloudWatch logs for processing errors
- **Webhook Delivery**: Rails logs show webhook receipt/processing

### Common Issues & Solutions
- **Missing Thumbnails**: Check Lambda CloudWatch logs
- **Slow Processing**: Monitor Lambda cold start frequency
- **Webhook Failures**: Check Rails webhook endpoint accessibility

## Integration Points

### Active Storage Services
**Location**: `config/storage.yml`
- `amazon_uploads`: Primary file storage service
- `amazon_thumbnails`: Thumbnail storage service

### Model Associations
**Location**: `app/models/project.rb`
- `has_many_attached :attachments` (original files)
- `has_many_attached :thumbnails` (Lambda-generated)

### Frontend Integration
- **WebSocket Updates**: Real-time upload progress via ActionCable
- **Dynamic Loading**: Thumbnails appear without page refresh
- **Fallback Icons**: Immediate visual feedback during processing

## Version History & Security Improvements

### Version 2 (July 2025) - UUID Security Implementation
**Security Problem Solved**: 
- Previous extension-replacement naming created collision risks
- Multiple users uploading identical filenames caused `PG::UniqueViolation` errors
- Potential cross-user data leakage through shared thumbnail keys

**Security Solution Implemented**:
- **Lambda**: Pure UUID-based thumbnail key generation (`{uuid}.png`)
- **Rails**: Idempotent webhook handling with find-or-create pattern
- **Result**: 100% collision-proof, GDPR-compliant file isolation

**Implementation Details**:
```python
# Lambda - New UUID generation
def generate_secure_thumbnail_key(original_key):
    directory = os.path.dirname(original_key)
    thumbnail_id = str(uuid.uuid4())
    return f"{directory}/thumbnails/{thumbnail_id}.png"
```

```ruby
# Rails - Idempotent webhook processing
thumbnail_blob = ActiveStorage::Blob.find_by(key: thumbnail_params[:key])
if thumbnail_blob
  Rails.logger.info "Reusing existing thumbnail blob"
else
  thumbnail_blob = ActiveStorage::Blob.create!(thumbnail_params)
end
```

**Backward Compatibility**: All existing thumbnails continue to work without modification.

### Version 1 (Original) - Extension Replacement
- Simple extension replacement: `document.pdf` → `document.png`
- Worked for single-user scenarios but had collision risks
- Code preserved in webhook controller for debugging/reference

## Future Enhancements

### Planned Improvements
- **Manual Retry**: Admin interface for failed thumbnail generation
- **Multiple Sizes**: Generate various thumbnail dimensions
- **Video Support**: Extend Lambda to handle video thumbnails
- **Bulk Operations**: Batch thumbnail regeneration tools

### Scalability Considerations
- **Lambda Concurrency**: Monitor and adjust concurrent execution limits
- **S3 Performance**: Consider S3 Transfer Acceleration for global users
- **Database Optimization**: Index optimization for large file collections