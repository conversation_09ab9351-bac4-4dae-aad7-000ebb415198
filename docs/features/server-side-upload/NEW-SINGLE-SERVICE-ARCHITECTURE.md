Of course. Here is a clear and precise set of instructions for the agent. It is based on the final, correct architecture we have designed together. It respects all of your code and requirements, guiding the agent to make only the necessary changes to align with the simpler, more robust single-service model.

Instructions for AI Agent: Refactor Rails File Upload to a Single-Service Architecture
1. High-Level Goal & Context
The user has a sophisticated file upload system with a state machine and real-time WebSocket updates. The current implementation was designed for a separate Web and Worker service on the Render platform, relying on a shared disk for file handoffs.

A diagnostic test (cat /mnt/disk/uploads/test.txt) has definitively proven that Render does not share disks between separate Web and Worker services. This is a fundamental platform limitation that makes the current architecture non-functional.

The new, mandatory architecture is to run the entire application within a single Render Web Service. This service will use a Procfile to run two processes concurrently in the same container: the puma web server and the good_job background worker. This solves the file visibility problem, as both processes will share the same temporary filesystem.

Your task is to analyze the user-provided code (FileUploadJob and UploadsController) and refactor it to work perfectly within this new, simpler single-service architecture, while preserving the detailed UX and state machine logic.

2. Core Architectural Mandate
All code changes must adhere to the following workflow:

The UploadsController receives the file from the user. It creates the Upload record in the database. Its only other job is to immediately enqueue a background job, passing it the upload_id and the path to the temporary file (params[:file].tempfile.path). The controller must not perform any file copying, fsync operations, or status updates itself.
The FileUploadJob, now running in the same container, receives the upload_id and temp_file_path.
The job is now solely responsible for orchestrating the entire state machine and UX flow: transferred -> processing -> completed, broadcasting each status change via WebSocket.
The job will use the provided temp_file_path to perform the upload to the final S3 destination.
3. Specific Refactoring Instructions by File
A. Refactor app/controllers/uploads_controller.rb

The current process_file_upload helper method is overly complex for the new architecture. It performs file copies, verification, and status updates that must now be moved to the background job.

Primary Task: Simplify the create action and its process_file_upload helper.
Remove Existing Logic: Delete the following logic from the controller:
Copying the file to a new temp path (UploadUtils.temp_file_path).
All file verification logic (File.exist?, File.size, f.fsync).
Updating the temp_file_path on the upload model instance.
Calling upload.mark_transferred!.
New Logic: The process_file_upload method should now only do two things:
Create the Upload record with an initial status of pending.
Enqueue the background job, passing the new upload.id and the path to the original temporary file: FileUploadJob.perform_later(upload.id, file.tempfile.path).
The controller should return a 202 Accepted status immediately after enqueuing the job.
B. Refactor app/jobs/file_upload_job.rb

This file contains most of an excellent implementation, but it was designed around the faulty "shared disk" assumption.

Primary Task: Adapt the job to receive the temp_file_path directly and orchestrate the full UX status flow.
Change perform Signature: Modify the method signature to accept the new argument: def perform(upload_id, temp_file_path).
Remove wait_for_file: The wait_for_file method and any calls to it must be removed. It was designed to solve the network filesystem race condition, which no longer exists in this single-container architecture. The file will always be present immediately.
Implement the UX Flow: At the beginning of the perform method, you must now implement the state transitions that were previously in the controller and model callbacks. The sequence should be:
Find the @upload record.
Immediately call @upload.mark_transferred! and broadcast the status.
Then, immediately call @upload.mark_processing! and broadcast that status.
Proceed with the existing upload_to_s3 and attach_to_target_model logic, using the temp_file_path variable that was passed into the perform method.
Ensure Cleanup: Review the ensure block. The temporary file at temp_file_path will be automatically deleted by Rack/Puma after the controller request finishes. However, it's safer to add a FileUtils.rm_f(temp_file_path) to the ensure block in the job for robustness, in case the file persists for any reason.
C. Review app/models/upload.rb (Not Provided)

The user's report implies that the Upload model may contain after_commit callbacks that enqueue the job automatically when the status changes.

Primary Task: Ensure the job is not enqueued twice.
Action: Instruct the user to find and remove any after_commit or other callbacks in the Upload model that call FileUploadJob.perform_later. Job enqueueing will now be handled explicitly and only by the controller.
4. Infrastructure and Deployment Instructions
Finally, generate the instructions for the user to modify their Render environment.

Create Procfile: Instruct the user to create a Procfile in their project root with the content:
Code snippet

web: bundle exec puma -C config/puma.rb
worker: bundle exec good_job start
Update Start Command: Tell the user to change the Start Command for their unlisters Web Service in the Render Dashboard to bundle exec foreman start -f Procfile.
Add Gem: Remind them to add gem 'foreman' to their Gemfile.
Delete Old Service: Instruct them to delete the now-redundant unlisters-app-worker service to simplify their setup and save costs.
By following these instructions, the agent will successfully refactor the user's application to a simpler, more robust, and fully functional architecture that meets all stated requirements.