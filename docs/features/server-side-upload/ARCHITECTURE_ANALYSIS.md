# Server-Side Upload Architecture Analysis

## Current vs New System Understanding ✅

### Current System (To be replaced)
- **Direct Active Storage uploads** to S3
- Projects have `has_many_attached :private_files, service: :amazon_uploads`
- Files attached directly via form uploads
- **No progress tracking** during upload
- **No state management** for upload process
- **No background job coordination**

### New System Architecture (What we're building)
- **Upload Model**: Tracks upload state/progress for UI and job coordination
- **Background Jobs**: Process uploads asynchronously with S3 integration  
- **Action Cable**: Real-time progress updates to frontend
- **Final Result**: Files still end up as `project.private_files` attachments

## Integration Flow ✅

```
1. User selects files → Upload records created (pending)
2. Files saved to temp storage → Upload marked (ready) 
3. Background job processes → Upload transitions (uploading → completed)
4. <PERSON>U<PERSON>load<PERSON><PERSON> attaches to project.private_files via Active Storage
5. Upload record cleanup → Final state: normal project.private_files
```

## Key Integration Points

### 1. Upload Model → Project Integration
- Upload belongs_to Project polymorphically
- <PERSON><PERSON><PERSON>load<PERSON><PERSON> finds Upload and attaches to project.private_files
- Upload provides state tracking, project.private_files provides final storage

### 2. Active Storage Services
- **amazon_uploads**: Service for actual project files  
- **amazon_thumbnails**: Service for PDF thumbnails
- Upload system will use existing service configuration

### 3. Secure File Access  
- Project includes SecureFileAccess concern
- Provides secure hash-based file access (not IDs)
- New uploads will integrate with existing security model

## Schema Comparison

### Upload Model (NEW - tracking/coordination)
```ruby
# Tracks upload process state
user_id          # Who is uploading
target_type      # "Project" 
target_id        # Project ID
status           # pending → ready → uploading → completed
temp_file_path   # Temporary storage location
s3_key          # Final S3 location
progress_percentage # 0-100 for UI
```

### Project.private_files (EXISTING - final storage)
```ruby
# Active Storage - final attachment storage
# Used by: project.private_files.attach(blob)
# Remains unchanged - Upload system feeds into this
```

## Missing User Association ⚠️

**CRITICAL**: User model needs `has_many :uploads` association for rate limiting in UploadsController line 139:

```ruby
uploads_in_last_hour = current_user.uploads
```

This is required for the new system to work with existing rate limiting logic.

## Next Steps

1. ✅ Add User association for uploads
2. ✅ Test Upload → Project.private_files integration 
3. ✅ Verify Active Storage service compatibility
4. ✅ Test with <NAME_EMAIL>