# Single-Service Architecture Deployment Instructions

## Overview

The file upload system has been refactored from a dual-service architecture (Web + Worker) to a single-service architecture due to <PERSON><PERSON>'s lack of shared disk between separate services.

## Architecture Changes

### Before (Dual-Service)
- **Web Service**: Handled file uploads, saved to shared disk, marked uploads as "transferred"
- **Worker Service**: Waited for files to appear on shared disk, processed to S3
- **Issue**: <PERSON><PERSON> doesn't actually share disks between services

### After (Single-Service)
- **Single Web Service**: Runs both web server and background worker in same container
- **Shared Filesystem**: Both processes have access to the same temporary files
- **Simplified Flow**: No race conditions or file visibility issues

## Code Changes Summary

### 1. UploadsController
- **Simplified**: Removed file copying, verification, and status updates
- **New Flow**: Create Upload record → Enqueue job immediately with temp file path
- **File**: `app/controllers/uploads_controller.rb`

### 2. FileUploadJob
- **New Signature**: `perform(upload_id, temp_file_path)` 
- **Enhanced Flow**: Handles all state transitions (pending → transferred → processing → completed)
- **Removed**: `wait_for_file` method (no longer needed)
- **File**: `app/jobs/file_upload_job.rb`

### 3. Upload Model
- **Removed**: `after_commit :enqueue_processing_job_if_ready` callback
- **Reason**: Jobs are now explicitly enqueued by controller
- **File**: `app/models/upload.rb`

## Deployment Instructions

### Step 1: Update Render Configuration

In your Render Dashboard for the **unlisters Web Service**:

1. **Change Start Command** to:
   ```bash
   bundle exec foreman start -f Procfile
   ```

2. **Update Environment Variables** (if needed):
   - Ensure all existing environment variables remain the same
   - No additional environment variables required

### Step 2: Delete Redundant Services

**Delete the `unlisters-app-worker` service** from Render Dashboard:
- This service is no longer needed
- Will save costs and simplify deployment
- All background processing now happens in the main web service

### Step 3: Deploy the Changes

1. **Commit and push** the refactored code
2. **Render will automatically deploy** with the new start command
3. **Monitor deployment** in Render Dashboard

## Process Management

The new `Procfile` runs two processes in the same container:

```
web: bundle exec puma -C config/puma.rb
worker: bundle exec good_job start
```

### Benefits:
- ✅ **Shared filesystem**: Both processes access the same temp files
- ✅ **No race conditions**: Files are immediately available to background jobs
- ✅ **Simplified deployment**: Single service to manage
- ✅ **Cost savings**: No separate worker service fees
- ✅ **Improved reliability**: No network filesystem dependencies

## Testing Locally

To test the new architecture locally:

```bash
# Install foreman gem (already added to Gemfile)
bundle install

# Check Procfile syntax
bundle exec foreman check

# Start both web and worker processes
bundle exec foreman start

# Alternative: Start processes individually
# Terminal 1:
bundle exec rails server

# Terminal 2:
bundle exec good_job start
```

## Monitoring

### Web Interface
- **Good Job Dashboard**: Visit `/good_job` (admin access required)
- **Upload Progress**: Real-time updates via Action Cable continue to work

### Logs
- **Combined logs**: Both web and worker logs appear in same Render service
- **Job processing**: Look for "FileUploadJob starting" messages
- **State transitions**: Debug messages show upload status changes

## Rollback Plan

If issues occur, you can temporarily rollback by:

1. **Reverting Start Command** to: `bundle exec puma -C config/puma.rb`
2. **Re-enabling Worker Service** (if still available)
3. **Reverting code changes** to previous commit

However, the new architecture is more robust and should not require rollback.

## Performance Considerations

### Expected Performance:
- **Same or better upload performance**: No shared disk bottlenecks
- **Immediate state transitions**: No file polling delays
- **Resource usage**: Single container uses resources more efficiently

### Scaling:
- **Horizontal scaling**: Can still scale web services normally
- **Worker capacity**: Each web instance includes worker capacity
- **Queue management**: Good Job handles job distribution across instances

## Troubleshooting

### Common Issues:

1. **Jobs not processing**:
   - Check logs for Good Job worker startup
   - Verify Procfile start command is correct

2. **File not found errors**:
   - Should be eliminated with new architecture
   - Check temp file paths in logs

3. **Upload timeouts**:
   - Same timeout settings apply
   - Monitor process memory usage

### Support Commands:

```bash
# Check running processes
ps aux | grep -E "(puma|good_job)"

# Monitor job queue
bundle exec rails runner "puts GoodJob::Job.count"

# Check upload status
bundle exec rails runner "puts Upload.group(:status).count"
```

---

## Status: ✅ READY FOR PRODUCTION DEPLOYMENT

The refactored single-service architecture is **production-ready** and addresses the fundamental platform limitation that was preventing proper file upload processing.