import React from 'react';

const RealEstateListingUI = () => {
  return (
    <div style={{ fontFamily: 'Inter, sans-serif', minHeight: '100vh', display: 'flex', backgroundColor: '#f5f5f5' }}>
      {/* Sidebar */}
      <aside style={{ width: '280px', backgroundColor: '#3a3a3a', color: 'white', padding: '0' }}>
        {/* Logo */}
        <div style={{ padding: '20px', borderBottom: '1px solid rgba(255,255,255,0.1)' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <div style={{ width: '40px', height: '40px', backgroundColor: '#5bb5a2', borderRadius: '4px' }}></div>
            <span style={{ fontSize: '24px', fontWeight: '500' }}>unlisters</span>
          </div>
        </div>

        {/* Navigation */}
        <nav style={{ padding: '20px 0' }}>
          {/* Deals Section */}
          <div style={{ marginBottom: '30px' }}>
            <div style={{ padding: '12px 20px', display: 'flex', alignItems: 'center', gap: '12px', fontSize: '18px', fontWeight: '500' }}>
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <rect x="3" y="3" width="7" height="7" />
                <rect x="14" y="3" width="7" height="7" />
                <rect x="3" y="14" width="7" height="7" />
                <rect x="14" y="14" width="7" height="7" />
              </svg>
              Deals
            </div>
            <div style={{ paddingLeft: '56px' }}>
              <div style={{ padding: '8px 0', fontSize: '16px', color: '#ccc', cursor: 'pointer' }}>All deals</div>
              <div style={{ padding: '8px 0', fontSize: '16px', color: '#ccc', cursor: 'pointer' }}>My deals</div>
            </div>
          </div>

          {/* Network Section */}
          <div style={{ marginBottom: '30px' }}>
            <div style={{ padding: '12px 20px', display: 'flex', alignItems: 'center', gap: '12px', fontSize: '18px', fontWeight: '500' }}>
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <circle cx="9" cy="9" r="3" />
                <circle cx="15" cy="15" r="3" />
                <path d="M12 3a3 3 0 100 6 3 3 0 000-6zM3 12a3 3 0 106 0 3 3 0 00-6 0zM12 15a3 3 0 100 6 3 3 0 000-6z" />
              </svg>
              Network
            </div>
            <div style={{ paddingLeft: '56px' }}>
              <div style={{ padding: '8px 0', fontSize: '16px', color: '#ccc', cursor: 'pointer' }}>Unlisters</div>
              <div style={{ padding: '8px 0', fontSize: '16px', color: '#ccc', cursor: 'pointer' }}>My network</div>
              <div style={{ padding: '8px 0', fontSize: '16px', color: '#ccc', cursor: 'pointer' }}>Invite users</div>
            </div>
          </div>

          {/* My Profile Section */}
          <div style={{ marginBottom: '30px' }}>
            <div style={{ padding: '12px 20px', display: 'flex', alignItems: 'center', gap: '12px', fontSize: '18px', fontWeight: '500' }}>
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <circle cx="12" cy="12" r="9" />
                <circle cx="12" cy="10" r="3" />
                <path d="M17.6 20a6 6 0 00-11.2 0" />
              </svg>
              My Profile
            </div>
            <div style={{ paddingLeft: '56px' }}>
              <div style={{ padding: '8px 0', fontSize: '16px', color: '#ccc', cursor: 'pointer' }}>Profile</div>
              <div style={{ padding: '8px 0', fontSize: '16px', color: '#ccc', cursor: 'pointer' }}>Logout</div>
            </div>
          </div>

          {/* Admin Section */}
          <div>
            <div style={{ padding: '12px 20px', fontSize: '18px', fontWeight: '500' }}>ADMIN</div>
          </div>
        </nav>
      </aside>

      {/* Main Content */}
      <main style={{ flex: 1, backgroundColor: '#f0f0f0' }}>
        {/* Header */}
        <header style={{ backgroundColor: '#3a3a3a', padding: '16px 32px', display: 'flex', justifyContent: 'flex-end', alignItems: 'center', gap: '24px' }}>
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="2">
            <path d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
          </svg>
          <span style={{ color: 'white', fontSize: '16px' }}>EN</span>
          <div style={{ width: '40px', height: '40px', backgroundColor: '#5bb5a2', borderRadius: '50%', display: 'flex', alignItems: 'center', justifyContent: 'center', fontSize: '20px', fontWeight: '500', color: '#3a3a3a' }}>
            L
          </div>
        </header>

        {/* Content Container */}
        <div style={{ padding: '32px' }}>
          {/* Title Box */}
          <div style={{ backgroundColor: 'white', border: '1px solid #e0e0e0', borderRadius: '8px', overflow: 'hidden', marginBottom: '24px' }}>
            {/* Green Header Bar */}
            <div style={{ height: '4px', backgroundColor: '#5bb5a2' }}></div>
            
            {/* Property Title */}
            <div style={{ padding: '24px 32px' }}>
              <h1 style={{ fontSize: '32px', fontWeight: '600', margin: '0 0 8px 0' }}>Industrial plot 44,8 ha</h1>
              <p style={{ fontSize: '16px', color: '#666', margin: 0 }}>📍 Zelená stráň, Košická Nová Ves, Slovakia</p>
            </div>
          </div>

          {/* Content Grid Box */}
          <div style={{ backgroundColor: 'white', border: '1px solid #e0e0e0', borderRadius: '8px', padding: '32px', marginBottom: '24px' }}>
            <div style={{ display: 'flex', gap: '32px' }}>
              {/* Left Column - Description */}
              <div style={{ flex: 1 }}>
                <h2 style={{ fontSize: '14px', fontWeight: '600', marginBottom: '16px', textTransform: 'uppercase', letterSpacing: '0.5px' }}>POPIS PONUKY</h2>
                <p style={{ fontSize: '15px', lineHeight: '1.6', color: '#333', marginBottom: '20px' }}>
                  Predaj pozemku na bytové domy, Bardejov, Pod Vinbargom. Predaj pozemkov s celkovou výmerou:
                </p>
                <p style={{ fontSize: '15px', lineHeight: '1.6', color: '#333', marginBottom: '32px' }}>
                  7 800 m2, so všetkými inžinierskymi sieťami, bezproblémovou dopravnou infraštruktúrou a lukratívnou polohou, ktoré sú vhodné na výstavbu bytových domov, odkonzultované s OŽP MÚ v Bardejove pre bezproblémové vydanie.
                </p>

                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '24px' }}>
                  <div>
                    <div style={{ fontSize: '13px', color: '#666', marginBottom: '4px', textTransform: 'uppercase', fontWeight: '600', letterSpacing: '0.5px' }}>TYP PONUKY</div>
                    <div style={{ fontSize: '15px', color: '#333' }}>Reality</div>
                  </div>
                  <div>
                    <div style={{ fontSize: '13px', color: '#666', marginBottom: '4px', textTransform: 'uppercase', fontWeight: '600', letterSpacing: '0.5px' }}>KATEGÓRIA</div>
                    <div style={{ fontSize: '15px', color: '#333' }}>Bytové domy</div>
                  </div>
                  <div>
                    <div style={{ fontSize: '13px', color: '#666', marginBottom: '4px', textTransform: 'uppercase', fontWeight: '600', letterSpacing: '0.5px' }}>VÝMERA</div>
                    <div style={{ fontSize: '15px', color: '#333' }}>2500 m²</div>
                  </div>
                </div>
              </div>

              {/* Right Column - Price and Contact */}
              <div style={{ width: '300px' }}>
                <div style={{ fontSize: '40px', fontWeight: '700', color: '#5bb5a2', marginBottom: '24px' }}>
                  €74 <span style={{ fontSize: '16px', fontWeight: '400', color: '#333' }}>/ m²</span>
                </div>
                
                <div>
                  <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '16px', alignItems: 'center' }}>
                    <span style={{ fontSize: '14px', color: '#666' }}>Provízia</span>
                    <span style={{ fontSize: '14px', color: '#333', fontWeight: '500' }}>1 %</span>
                  </div>
                  <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '16px', alignItems: 'center' }}>
                    <span style={{ fontSize: '14px', color: '#666' }}>Ponúka</span>
                    <span style={{ fontSize: '14px', color: '#333', fontWeight: '500' }}>Lorant Szabo</span>
                  </div>
                  <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '16px', alignItems: 'center' }}>
                    <span style={{ fontSize: '14px', color: '#666' }}>Tel</span>
                    <span style={{ fontSize: '14px', color: '#333', fontWeight: '500' }}>+421 910 584 228</span>
                  </div>
                  <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '16px', alignItems: 'center' }}>
                    <span style={{ fontSize: '14px', color: '#666' }}>Email</span>
                    <span style={{ fontSize: '14px', color: '#333', fontWeight: '500' }}><EMAIL></span>
                  </div>
                  <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '16px', alignItems: 'center' }}>
                    <span style={{ fontSize: '14px', color: '#666' }}>Aktualizácia</span>
                    <span style={{ fontSize: '14px', color: '#333', fontWeight: '500' }}>25. 6. 2025</span>
                  </div>
                  <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '32px', alignItems: 'center' }}>
                    <span style={{ fontSize: '14px', color: '#666' }}>Viditeľnosť</span>
                    <span style={{ fontSize: '14px', color: '#333', fontWeight: '500' }}>Moja sieť / Nadpis</span>
                  </div>

                  <button style={{
                    width: '100%',
                    padding: '14px',
                    backgroundColor: '#4b7bec',
                    color: 'white',
                    border: 'none',
                    borderRadius: '8px',
                    fontSize: '16px',
                    fontWeight: '500',
                    cursor: 'pointer'
                  }}>
                    Upraviť ponuku
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Attachments Box */}
          <div style={{ backgroundColor: 'white', border: '1px solid #e0e0e0', borderRadius: '8px', padding: '32px' }}>
            <h2 style={{ fontSize: '24px', fontWeight: '600', marginBottom: '24px' }}>Prílohy</h2>
            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(3, 1fr)', gap: '24px' }}>
              {[1, 2, 3].map((item) => (
                <div key={item} style={{ textAlign: 'center' }}>
                  <div style={{ 
                    width: '100%', 
                    height: '180px', 
                    backgroundColor: '#e8e8e8', 
                    borderRadius: '8px',
                    marginBottom: '8px',
                    backgroundImage: 'url("data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' width=\'100\' height=\'100\' viewBox=\'0 0 100 100\'%3E%3Crect width=\'100\' height=\'100\' fill=\'%23e8e8e8\'/%3E%3C/svg%3E")',
                    backgroundSize: 'cover',
                    backgroundPosition: 'center'
                  }}></div>
                  <div style={{ fontSize: '14px', fontWeight: '500', marginBottom: '2px' }}>BD BJ 1.jpg</div>
                  <div style={{ fontSize: '12px', color: '#666', marginBottom: '8px' }}>59,09 KB Jun 19</div>
                  <button style={{ 
                    padding: '6px 16px',
                    backgroundColor: 'transparent',
                    color: '#4b7bec',
                    border: '1px solid #4b7bec',
                    borderRadius: '4px',
                    fontSize: '13px',
                    cursor: 'pointer',
                    display: 'inline-flex',
                    alignItems: 'center',
                    gap: '4px'
                  }}>
                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                      <path d="M21 15v4a2 2 0 01-2 2H5a2 2 0 01-2-2v-4M7 10l5 5 5-5M12 15V3" />
                    </svg>
                    Stiahnuť
                  </button>
                </div>
              ))}
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default RealEstateListingUI;