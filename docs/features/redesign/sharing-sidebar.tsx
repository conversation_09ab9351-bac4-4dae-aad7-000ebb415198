import React, { useState } from 'react';
import { Users, UserCheck, Eye, Lock, AlertCircle } from 'lucide-react';

const SharingSidebar = () => {
  const [audience, setAudience] = useState('network'); // 'network' or 'all'
  const [access, setAccess] = useState('summary'); // 'summary' or 'full'

  const getSecurityLevel = () => {
    if (audience === 'network' && access === 'summary') return 'high';
    if (audience === 'network' && access === 'full') return 'medium';
    if (audience === 'all' && access === 'summary') return 'medium';
    return 'low';
  };

  const securityLevel = getSecurityLevel();

  return (
    <div className="w-72 bg-white border border-gray-200 rounded-lg shadow-sm">
      <div className="p-4 border-b border-gray-100">
        <div className="flex items-center gap-2">
          <Lock className="w-4 h-4 text-gray-600" />
          <h3 className="font-semibold text-gray-900">Project Access</h3>
        </div>
      </div>
      
      <div className="p-4">
        {/* Security Level Indicator */}
        <div className={`mb-6 p-3 rounded-lg border-2 ${
          securityLevel === 'high' ? 'bg-green-50 border-green-200' :
          securityLevel === 'medium' ? 'bg-yellow-50 border-yellow-200' :
          'bg-red-50 border-red-200'
        }`}>
          <div className="flex items-center gap-2 mb-1">
            <div className={`w-2 h-2 rounded-full ${
              securityLevel === 'high' ? 'bg-green-500' :
              securityLevel === 'medium' ? 'bg-yellow-500' :
              'bg-red-500'
            }`}></div>
            <span className={`text-sm font-medium ${
              securityLevel === 'high' ? 'text-green-800' :
              securityLevel === 'medium' ? 'text-yellow-800' :
              'text-red-800'
            }`}>
              {securityLevel === 'high' ? 'Secure' : 
               securityLevel === 'medium' ? 'Moderate' : 'Open'}
            </span>
          </div>
          <div className={`text-xs ${
            securityLevel === 'high' ? 'text-green-700' :
            securityLevel === 'medium' ? 'text-yellow-700' :
            'text-red-700'
          }`}>
            {securityLevel === 'high' ? 'Network only, approval required' :
             securityLevel === 'medium' ? 
               (audience === 'network' ? 'Network only, direct access' : 'Public listing, approval required') :
             'Public listing, direct access'}
          </div>
        </div>

        {/* Access Matrix */}
        <div className="space-y-4">
          <div className="text-sm font-medium text-gray-700 mb-3">Who can discover this project?</div>
          
          <div className="grid grid-cols-2 gap-2 mb-6">
            <button
              onClick={() => setAudience('network')}
              className={`p-3 rounded-lg border-2 transition-all ${
                audience === 'network' 
                  ? 'border-blue-500 bg-blue-50' 
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              <UserCheck className={`w-5 h-5 mx-auto mb-2 ${
                audience === 'network' ? 'text-blue-600' : 'text-gray-500'
              }`} />
              <div className="text-xs font-medium">My Network</div>
            </button>
            
            <button
              onClick={() => setAudience('all')}
              className={`p-3 rounded-lg border-2 transition-all ${
                audience === 'all' 
                  ? 'border-blue-500 bg-blue-50' 
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              <Users className={`w-5 h-5 mx-auto mb-2 ${
                audience === 'all' ? 'text-blue-600' : 'text-gray-500'
              }`} />
              <div className="text-xs font-medium">All Users</div>
            </button>
          </div>

          <div className="text-sm font-medium text-gray-700 mb-3">What can they see initially?</div>
          
          <div className="grid grid-cols-2 gap-2">
            <button
              onClick={() => setAccess('summary')}
              className={`p-3 rounded-lg border-2 transition-all ${
                access === 'summary' 
                  ? 'border-blue-500 bg-blue-50' 
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              <Eye className={`w-5 h-5 mx-auto mb-2 ${
                access === 'summary' ? 'text-blue-600' : 'text-gray-500'
              }`} />
              <div className="text-xs font-medium">Summary Only</div>
              <div className="text-xs text-gray-500 mt-1">Must request details</div>
            </button>
            
            <button
              onClick={() => setAccess('full')}
              className={`p-3 rounded-lg border-2 transition-all ${
                access === 'full' 
                  ? 'border-blue-500 bg-blue-50' 
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              <AlertCircle className={`w-5 h-5 mx-auto mb-2 ${
                access === 'full' ? 'text-blue-600' : 'text-gray-500'
              }`} />
              <div className="text-xs font-medium">Full Details</div>
              <div className="text-xs text-gray-500 mt-1">Immediate access</div>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SharingSidebar;