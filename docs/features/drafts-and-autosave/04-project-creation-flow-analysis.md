# Project Creation Flow Analysis

**Status**: Investigation Complete - July 2025  
**Issue**: Projects created even when user makes no form changes  
**Architecture**: "Google Docs Style" immediate draft creation  

## Problem Statement

Users report projects being created even when they don't make any changes to the form. Investigation reveals this is due to the "Google Docs style" immediate project creation architecture combined with dirty tracking complexities.

## Current Architecture Flow

### 1. Immediate Project Creation (Primary Issue)

**Location**: `app/controllers/projects_controller.rb:150-170`

```ruby
def new
  # Create draft project immediately (Google Docs style)
  @project = current_user.projects.build(project_status: false)
  
  if @project.save(validate: false)
    # Redirect to edit the newly created draft
    redirect_to edit_project_path(@project)
  else
    # Fallback to form if save fails
    render :new, status: :unprocessable_entity
  end
end
```

**Flow**:
1. User clicks "New Project" → `GET /projects/new`
2. **Controller immediately creates project** → `@project.save(validate: false)`
3. Redirects to edit form → `redirect_to edit_project_path(@project)`
4. **Project exists before user types anything**

### 2. Autosave Logic (Working Correctly)

**Location**: `app/controllers/projects_controller.rb:223-233`

```ruby
if params[:autosave] == 'true'
  @project.assign_attributes(autosave_params)
  
  # Only save if there are actual changes (Rails dirty tracking)
  if @project.changed?
    @project.save(validate: false)
  end
  
  head :ok
  return
end
```

**Status**: ✅ This logic is correct - only saves when Rails detects changes

### 3. JavaScript Autosave Behavior

**Location**: `app/javascript/autosave.js`

**Analysis**:
- ✅ Properly debounced (2 second delay)
- ✅ Ignores file inputs and system fields
- ✅ Only triggers on actual user input/change events
- ✅ Respects online/offline state

## Database vs Model Defaults Issue

### Pre-Migration State (RESOLVED)
- **Database**: `project_type` INTEGER NOT NULL DEFAULT 0
- **Model**: Forces `nil` for new records
- **Result**: Dirty tracking confusion (`0` → `nil` → `""`)

### Post-Migration State (July 2025)
- **Database**: `project_type` INTEGER NULL DEFAULT NULL
- **Model**: Sets `nil` for new records
- **Result**: Clean dirty tracking (`nil` → `""`)

**Migration Status**: ✅ `20250729210412_allow_null_for_project_enums` - Applied

## Dirty Tracking Analysis

### Before Migration Fix
```ruby
# New project creation
@project = Project.new
@project.project_type # => 0 (database default)
# Model callback forces nil
@project.project_type = nil # Rails sees this as a change: 0 → nil

# Form submission with empty values
params[:project][:project_type] = ""
@project.assign_attributes(params[:project])
# Rails sees change: nil → "" (legitimate change)
```

### After Migration Fix
```ruby
# New project creation
@project = Project.new  
@project.project_type # => nil (database default)
# Model callback: already nil, no change

# Form submission with empty values
params[:project][:project_type] = ""
@project.assign_attributes(params[:project])
# Rails sees change: nil → "" (still legitimate first-time change)
# But subsequent empty submissions: "" → "" (no change)
```

## The "Google Docs Style" Architecture Decision

### Benefits
- ✅ **Instant UX**: No "create" step, immediate editing capability
- ✅ **Draft Persistence**: Automatic saving prevents data loss
- ✅ **Familiar Pattern**: Matches Google Docs, Notion, etc.

### Costs
- ❌ **Storage Overhead**: Creates projects even for immediate abandonment
- ❌ **Database Pollution**: Accumulates unused draft projects
- ❌ **Perceived Bug**: Users see "creation without changes"

## Investigation Findings

### What's NOT Causing Unwanted Creation
- ❌ Model callbacks aren't forcing saves
- ❌ Autosave logic is working correctly  
- ❌ JavaScript isn't triggering spuriously
- ❌ Database defaults (fixed by migration)

### What IS Causing Unwanted Creation
- ✅ **PRIMARY**: Immediate project creation in `new` action
- ✅ **SECONDARY**: First form interaction triggers legitimate `nil → ""` change

## Technical Recommendations

### Option A: Defer Project Creation Until First Change
```ruby
def new
  # Don't create project immediately
  @project = current_user.projects.build(project_status: false)
  # Only render form, don't save
  render :new
end

def create
  # Handle both autosave and initial creation
  @project = current_user.projects.build(project_params)
  # Save logic here
end
```

### Option B: Accept "Google Docs Style" With Cleanup
- Keep current immediate creation
- Implement periodic cleanup of abandoned drafts
- Add clear user messaging about draft creation

### Option C: Hybrid Approach
- Create project on first autosave trigger
- Use local storage for form state until first save
- Seamless transition to server-side persistence

## Business Impact

### Current User Experience
1. User clicks "New Project"
2. **Project created immediately** (invisible to user)
3. User sees empty form
4. User leaves without typing → **"Empty" project remains in database**
5. User returns to "My Projects" → **May see confusing empty draft**

### Proposed User Experience (Option A)
1. User clicks "New Project"
2. User sees empty form (**no project created yet**)
3. User types first character → **Project created with autosave**
4. User leaves → **Only projects with content remain**

## Related Files

- **Controller**: `app/controllers/projects_controller.rb`
- **Model**: `app/models/project.rb` (callbacks and defaults)
- **JavaScript**: `app/javascript/autosave.js`
- **Migration**: `db/migrate/20250729210412_allow_null_for_project_enums.rb`

## Next Steps

1. **Business Decision**: Choose between immediate vs deferred creation
2. **Implementation**: Update controller logic based on decision
3. **Testing**: Verify dirty tracking works correctly post-migration
4. **Cleanup**: Consider migration for existing empty drafts
5. **Documentation**: Update user-facing docs about draft behavior

---

**Investigation Date**: July 30, 2025  
**Status**: Analysis Complete, Awaiting Implementation Decision