# Simplified Draft Implementation Recommendations

## Overview

This document provides simplified implementation recommendations for the draft workflow enhancement, focusing on minimal changes and maximum safety. This approach achieves the core goal (immediate project creation + file uploads) with the least risk.

## Review Results

### ✅ **What's Already Working Well**
1. **Draft infrastructure exists**: The `project_status` field and scopes (`drafts`, `pending_approval`, `published`) are already implemented
2. **File upload system works**: Files can already be uploaded to existing projects
3. **Admin workflow intact**: The approval process is well-established

### 🎯 **Simplest Implementation Strategy**

#### **Phase 1: Minimal Model Changes (HIGHEST PRIORITY)**
Instead of complex conditional validations, start with this simpler approach:

```ruby
# Add these helper methods to Project model (app/models/project.rb)
def published?
  project_status == true
end

def draft?
  project_status == false  
end

# Modify existing validations to be conditional
validates :project_type, presence: true, if: :published?
validates :category, presence: true, if: :published?
validates :subcategory, presence: true, if: :published?
validates :location, presence: true, if: :published?
validate :summary_present, if: :published?
validate :validate_sharing_options, if: :published?

# Skip geocoding for drafts
def should_geocode?
  published? && location.present? && (location_changed? || latitude.blank? || longitude.blank?)
end
```

#### **Phase 2: Controller Simplification (MEDIUM PRIORITY)**
Simplify controller changes in `app/controllers/projects_controller.rb`:

```ruby
def create
  @project = current_user.projects.build(project_params)
  @project.project_status = false  # Always create as draft
  
  if @project.save(validate: false)  # Skip validations for drafts
    redirect_to edit_project_path(@project), notice: 'Draft created successfully.'
  else
    render :new, status: :unprocessable_entity
  end
end

def update
  authorize! @project
  
  # Only validate when trying to publish
  if params[:publish] == 'true'
    @project.project_status = true
    if @project.update(project_params)
      # Existing admin notification logic
      notify_admins_if_needed
      redirect_to edit_project_path(@project), notice: 'Project submitted for approval.'
    else
      @project.project_status = false
      render :edit, status: :unprocessable_entity
    end
  else
    # Draft updates skip validation using update_columns
    safe_params = project_params.except(:project_status)
    @project.update_columns(safe_params.to_h)
    redirect_to edit_project_path(@project), notice: 'Draft saved.'
  end
end

private

def notify_admins_if_needed
  if @project.project_status && @project.saved_change_to_project_status?
    admin_users = User.active.where(role: [:admin, :super_boss])
    admin_users.each do |admin|
      NotificationMailer.admin_project_notification(@project, admin, current_user).deliver_later
    end
  end
end
```

#### **Phase 3: Simple UI Updates (MEDIUM PRIORITY)**
Add basic draft indicators and save functionality:

```erb
<!-- In project form (app/views/projects/_form.html.erb or edit.html.erb) -->
<div class="draft-controls">
  <% if @project.draft? %>
    <div class="draft-status">
      <span class="badge badge-warning">Draft</span>
      <p>This project is saved as a draft. Complete required fields to publish.</p>
    </div>
    
    <div class="form-actions">
      <%= button_to 'Save Draft', project_path(@project), 
                    method: :patch, 
                    params: { project: project_form_params },
                    class: 'btn btn-secondary save-draft-btn' %>
      
      <%= button_to 'Publish Project', project_path(@project), 
                    method: :patch, 
                    params: { publish: 'true', project: project_form_params },
                    class: 'btn btn-primary publish-btn',
                    confirm: 'Submit project for admin approval?' %>
    </div>
  <% else %>
    <div class="published-status">
      <span class="badge badge-success">Published</span>
      <p>This project has been submitted for admin approval.</p>
    </div>
  <% end %>
</div>
```

#### **Phase 4: Skip Autosave Initially (LOWEST PRIORITY)**
**Start without autosave** - it's complex and could introduce bugs. The manual "Save Draft" button provides the core functionality with much less risk.

### 🚨 **Critical Simplifications**

1. **Remove geocoding complexity**: Skip geocoding for empty locations in drafts
2. **Simplify file upload flow**: Files already work with existing projects - no changes needed
3. **Skip autosave initially**: Manual save is much simpler and less error-prone
4. **Use `update_columns` for drafts**: Bypasses all validations and callbacks safely

### 📊 **Implementation Order**
1. **Model validation changes** (1-2 hours)
2. **Controller create/update methods** (2-3 hours) 
3. **Simple UI indicators** for draft status (1 hour)
4. **Test thoroughly** before considering autosave

### 🎯 **Key Benefits of This Approach**
- **Minimal risk**: Uses existing patterns and infrastructure
- **No schema changes**: Leverages current `project_status` field
- **Incremental**: Can add autosave later if needed
- **Testable**: Each phase can be tested independently
- **Reversible**: Easy to rollback if issues arise

### ⚠️ **What to Avoid from the Original Plan**
- Complex autosave JavaScript (add later if needed)
- Conditional validation complexity (start simple)
- `save(validate: false)` everywhere (use `update_columns` for drafts)
- Complex form state management (keep current form structure)
- Premature optimization

### 🔄 **Future Enhancements (Optional)**
Once the basic functionality is working:
1. Add autosave JavaScript with debouncing
2. Add visual indicators for unsaved changes
3. Implement offline support
4. Add draft cleanup for abandoned projects

### 🧪 **Testing Strategy**
1. **Unit tests**: Model validation conditions
2. **Integration tests**: Controller create/update flows
3. **Feature tests**: End-to-end draft creation and publishing
4. **Edge cases**: Network failures, validation errors, file uploads

This simplified approach achieves the core goal with **minimal changes and maximum safety**.
