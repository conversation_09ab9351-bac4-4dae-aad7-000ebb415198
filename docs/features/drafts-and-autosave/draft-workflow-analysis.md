# Draft Workflow Analysis & Implementation Plan

## Overview

This document analyzes the feasibility and implementation approach for enhancing the existing draft workflow to allow users to create projects immediately and upload files without requiring all compulsory fields to be completed first.

## Current State Analysis

### Existing Draft Logic
- **Current System**: `project_status` field where `false` = draft, `true` = published
- **Public Queries**: Already exclude drafts (`project_status: true`) from other users' searches
- **User Dashboard**: Drafts already show in "My Projects" section
- **File Uploads**: Currently require persisted project (already supported)

### Current Validations (Required Fields)
- `project_type`, `category`, `subcategory` - Required
- `location` - Required  
- `summary` - Required via custom validation
- Complex sharing options validation
- Summary length limit (123 characters)

### Current File Upload System
- Server-side upload with `Upload` model tracking
- Upload handler checks for `targetId` (project must exist)
- Real-time progress via ActionCable
- Files can only be uploaded to persisted projects

## Implementation Approach

### ✅ PROS

1. **Seamless UX**: Users can immediately start uploading files without form submission
2. **No Lost Work**: Drafts saved automatically prevent data loss
3. **Better Mobile Experience**: Reduced form complexity on mobile devices
4. **Progressive Enhancement**: Natural workflow - create → populate → publish
5. **Existing Infrastructure**: Upload system and draft logic already supports this pattern
6. **No Schema Changes**: Uses existing `project_status` field

### ⚠️ CONS & CHALLENGES

1. **Validation Complexity**: Conditional validation based on draft/published status
2. **Geocoding**: Location validation conflicts with empty draft creation
3. **Form State Management**: Need to handle incomplete form states gracefully

## 🔄 IMPLEMENTATION PLAN

### Phase 1: Model Changes (Minimal)
```ruby
class Project < ApplicationRecord
  # Conditional validations - only enforce for published projects
  validates :project_type, presence: true, if: :published?
  validates :category, presence: true, if: :published?
  validates :subcategory, presence: true, if: :published?
  validates :location, presence: true, if: :published?
  validate :summary_present, if: :published?
  validate :validate_sharing_options, unless: :draft_or_admin_approval?
  
  # Status-based scopes (already implemented)
  scope :drafts, -> { where(project_status: false) }
  scope :pending_approval, -> { where(project_status: true, approved: false) }
  scope :published, -> { where(project_status: true, approved: true) }
  
  # Helper methods
  def published?
    project_status == true
  end
  
  def draft?
    project_status == false
  end
  
  def status_label
    case status
    when 'published'
      'Published'
    when 'pending'
      'Pending Approval'
    when 'draft'
      'Draft'
    end
  end
  
  private
  
  def draft_or_admin_approval?
    draft? || is_admin_approval_action
  end
end
```

### Phase 2: Controller Changes
```ruby
class ProjectsController < ApplicationController
  def new
    @project = Project.new(project_status: false) # Create as draft
  end
  
  def create
    @project = current_user.projects.build(project_params)
    @project.project_status = false # Force draft status
    
    # Skip validations for drafts but still save
    if @project.save(validate: false)
      redirect_to edit_project_path(@project), notice: 'Draft created successfully.'
    else
      render :new, status: :unprocessable_entity
    end
  end
  
  def update
    authorize! @project
    
    # If trying to publish, validate first
    if project_params[:project_status] == 'true' || project_params[:project_status] == true
      if @project.assign_attributes(project_params) && @project.valid?
        @project.save!
        # Existing publication logic (admin notification, etc.)
        notify_admins_if_needed
        redirect_to edit_project_path(@project), notice: 'Project submitted for approval.'
      else
        # Validation failed, keep as draft
        @project.project_status = false
        @project.save(validate: false)
        render :edit, status: :unprocessable_entity
      end
    else
      # Regular draft update - skip validations
      @project.assign_attributes(project_params)
      @project.save(validate: false)
      redirect_to edit_project_path(@project), notice: 'Draft updated successfully.'
    end
  end
  
  def show_my
    @drafts = current_user.projects.drafts.order(updated_at: :desc)
    @pending = current_user.projects.pending_approval.order(updated_at: :desc)
    @published = current_user.projects.published.includes(:project_auths).order(updated_at: :desc)
  end
  
  private
  
  def project_params
    params.require(:project).permit(
      :summary, :full_description, :location, :project_type, 
      :category, :subcategory, :price_value, :price_currency,
      :price_text, :commission, :commission_type, :land_area,
      :area_unit, :summary_only, :full_access, :network_only,
      :semi_public, :project_status,
      private_files: [], remove_private_files: []
    )
  end
  
  def notify_admins_if_needed
    if @project.project_status && @project.saved_change_to_project_status?
      admin_users = User.active.where(role: [:admin, :super_boss])
      admin_users.each do |admin|
        NotificationMailer.admin_project_notification(@project, admin, current_user).deliver_later
      end
    end
  end
end
```

### Phase 3: Autosave Implementation
```javascript
class AutosaveManager {
  constructor(formId, projectId) {
    this.form = document.getElementById(formId);
    this.projectId = projectId;
    this.saveTimeout = null;
    this.isOnline = navigator.onLine;
    this.pendingChanges = false;
    this.lastSaveTime = null;
    
    this.initializeEventListeners();
    this.setupOnlineOfflineHandlers();
  }
  
  initializeEventListeners() {
    // Debounced autosave on input changes
    this.form.addEventListener('input', (e) => {
      if (this.shouldIgnoreField(e.target)) return;
      
      this.pendingChanges = true;
      this.clearSaveTimeout();
      this.showUnsavedIndicator();
      
      // Debounce save for 2 seconds
      this.saveTimeout = setTimeout(() => {
        this.performAutosave();
      }, 2000);
    });
    
    // Save before page unload
    window.addEventListener('beforeunload', (e) => {
      if (this.pendingChanges) {
        this.performAutosave();
        e.preventDefault();
        e.returnValue = '';
      }
    });
  }
  
  shouldIgnoreField(element) {
    // Don't autosave file inputs, publish button, or system fields
    return element.type === 'file' || 
           element.name === 'authenticity_token' ||
           element.name === 'project[project_status]' ||
           element.classList.contains('no-autosave');
  }
  
  async performAutosave() {
    if (!this.isOnline || !this.pendingChanges) return;
    
    const formData = new FormData(this.form);
    formData.set('project[project_status]', 'false'); // Force draft
    formData.append('autosave', 'true');
    
    this.showSavingIndicator();
    
    try {
      const response = await fetch(`/projects/${this.projectId}`, {
        method: 'PATCH',
        body: formData,
        headers: {
          'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
        }
      });
      
      if (response.ok) {
        this.pendingChanges = false;
        this.lastSaveTime = new Date();
        this.showSavedIndicator();
      } else {
        this.showErrorIndicator();
      }
    } catch (error) {
      console.error('Autosave failed:', error);
      this.showErrorIndicator();
    }
  }
  
  setupOnlineOfflineHandlers() {
    window.addEventListener('online', () => {
      this.isOnline = true;
      if (this.pendingChanges) {
        this.performAutosave();
      }
    });
    
    window.addEventListener('offline', () => {
      this.isOnline = false;
      this.showOfflineIndicator();
    });
  }
  
  clearSaveTimeout() {
    if (this.saveTimeout) {
      clearTimeout(this.saveTimeout);
      this.saveTimeout = null;
    }
  }
  
  showUnsavedIndicator() {
    this.updateSaveIndicator('Unsaved changes', 'unsaved');
  }
  
  showSavingIndicator() {
    this.updateSaveIndicator('Saving...', 'saving');
  }
  
  showSavedIndicator() {
    this.updateSaveIndicator('Saved', 'saved');
    setTimeout(() => {
      this.hideSaveIndicator();
    }, 3000);
  }
  
  showErrorIndicator() {
    this.updateSaveIndicator('Save failed', 'error');
  }
  
  showOfflineIndicator() {
    this.updateSaveIndicator('Offline', 'offline');
  }
  
  updateSaveIndicator(text, status) {
    let indicator = document.getElementById('autosave-indicator');
    if (!indicator) {
      indicator = this.createSaveIndicator();
    }
    
    indicator.textContent = text;
    indicator.className = `autosave-indicator ${status}`;
    indicator.style.display = 'block';
  }
  
  hideSaveIndicator() {
    const indicator = document.getElementById('autosave-indicator');
    if (indicator) {
      indicator.style.display = 'none';
    }
  }
  
  createSaveIndicator() {
    const indicator = document.createElement('div');
    indicator.id = 'autosave-indicator';
    indicator.className = 'autosave-indicator';
    
    // Insert into sidebar or form
    const container = document.querySelector('.sharing-sidebar') || this.form;
    container.appendChild(indicator);
    
    return indicator;
  }
}

// Initialize autosave for project forms
document.addEventListener('DOMContentLoaded', () => {
  const projectForm = document.getElementById('project_form');
  const projectId = projectForm?.dataset.projectId;
  
  if (projectForm && projectId) {
    window.autosaveManager = new AutosaveManager('project_form', projectId);
  }
});
```

## 🚨 EDGE CASES & SOLUTIONS

2. **Concurrent Editing**: Use optimistic locking with `updated_at` timestamps
3. **Network Failures**: Queue autosave requests and retry on reconnection
4. **Form Validation**: Show validation errors without preventing draft saves
5. **File Uploads**: Already works with existing Upload system
6. **Geocoding Conflicts**: Skip geocoding for empty locations in drafts

## 📊 DRAFT MANAGEMENT


## 🎯 PUBLISH WORKFLOW ENHANCEMENT

### Form Updates
```erb
<!-- Enhanced publish section in project form -->
<div class="publish-section">
  <% if @project.draft? %>
    <div class="draft-status">
      <span class="draft-indicator">Draft</span>
      <p>This project is saved as a draft. Complete the required fields and publish when ready.</p>
    </div>
    
    <div class="publish-actions">
      <%= button_to 'Publish Project', project_path(@project), 
                    method: :patch, 
                    params: { project: { project_status: true } },
                    class: 'publish-button',
                    confirm: 'Are you sure you want to submit this project for approval?',
                    id: 'publish-btn' %>
    </div>
  <% else %>
    <div class="published-status">
      <span class="published-indicator">Published</span>
      <p>This project has been submitted for admin approval.</p>
    </div>
  <% end %>
</div>
```

### Validation Feedback
```javascript
// Enhanced form validation for publish button
function validateForPublish() {
  const requiredFields = ['project_type', 'category', 'subcategory', 'location', 'summary'];
  const publishBtn = document.getElementById('publish-btn');
  
  const isValid = requiredFields.every(field => {
    const input = document.querySelector(`[name="project[${field}]"]`);
    return input && input.value.trim() !== '';
  });
  
  if (publishBtn) {
    publishBtn.disabled = !isValid;
    publishBtn.textContent = isValid ? 'Publish Project' : 'Complete Required Fields';
  }
}

// Run validation on form changes
document.addEventListener('input', validateForPublish);
document.addEventListener('DOMContentLoaded', validateForPublish);
```

## 🔧 AUTOSAVE DIFFICULTY: LOW-MEDIUM

### Existing Foundation
- Form handling infrastructure exists
- Upload handler shows real-time updates work
- CSRF token handling already implemented
- No database schema changes needed

### Required Changes
- Add conditional validations to model
- Modify controller to handle drafts
- Add debounced autosave JavaScript
- Enhance form UI for draft/publish states

## 📝 RECOMMENDATIONS

1. **Start with Conditional Validations**: Update model validations first
2. **Enhance Create Flow**: Allow immediate draft creation
3. **Add Autosave Gradually**: Begin with manual save, then add autosave
4. **Visual Feedback**: Clear indicators for draft vs published states
5. **Cleanup Strategy**: Regular cleanup of abandoned drafts

## CSS Styling
```scss
.autosave-indicator {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  z-index: 1000;
  transition: all 0.3s ease;
  
  &.unsaved {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
  }
  
  &.saving {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
  }
  
  &.saved {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
  }
  
  &.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
  }
  
  &.offline {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
  }
}

.draft-indicator {
  display: inline-block;
  padding: 2px 8px;
  background: #fff3cd;
  color: #856404;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  margin-left: 8px;
}

.pending-indicator {
  display: inline-block;
  padding: 2px 8px;
  background: #ffeaa7;
  color: #b7791f;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  margin-left: 8px;
}

.published-indicator {
  display: inline-block;
  padding: 2px 8px;
  background: #d4edda;
  color: #155724;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  margin-left: 8px;
}

.publish-section {
  margin-top: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #dee2e6;
  
  .publish-button {
    background: #28a745;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    
    &:hover:not(:disabled) {
      background: #218838;
      transform: translateY(-1px);
    }
    
    &:disabled {
      background: #6c757d;
      cursor: not-allowed;
      transform: none;
    }
  }
}

.project-card {
  padding: 16px;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  margin-bottom: 12px;
  
  &.draft {
    background: #fefefe;
    border-left: 4px solid #ffc107;
  }
  
  &.pending {
    background: #fffdf5;
    border-left: 4px solid #ffc107;
  }
  
  &.published {
    background: #f8f9fa;
    border-left: 4px solid #28a745;
  }
}
```

This streamlined implementation leverages the existing `project_status` field and infrastructure while adding the requested immediate draft creation and autosave functionality. The approach is minimal but effective, requiring no database schema changes while providing the seamless file upload experience you want.