# Troubleshooting Guide

## Memory Issues

### Memory Leak in Geocoder Gem (RESOLVED)

**Issue**: The geocoder gem was causing severe memory leaks of ~7MB per 10 location queries, causing production apps to hit memory limits and restart.

**Symptoms**:
- Memory usage climbing from ~200MB to 512MB+ with light usage
- Memory not being freed by garbage collection
- Production server restarts due to memory limits

**Root Cause**: Geocoder gem retains objects internally without proper cache cleanup.

**Solution Implemented**:
1. **Enabled geocoder cache** in `config/initializers/geocoder.rb`:
   ```ruby
   Geocoder.configure(
     cache: {},  # Simple hash cache with manual cleanup
     # ... other configuration
   )
   ```

2. **Added cache cleanup in ProjectsController**:
   ```ruby
   after_action :clear_geocoder_cache
   
   private
   
   def clear_geocoder_cache_safe
     # Use official geocoder cache expiration API
     lookup = Geocoder::Lookup.get(Geocoder.config[:lookup])
     lookup.cache.expire(:all) if lookup&.cache
     
     # Clear configuration cache and force GC
     Geocoder.configuration.cache.clear if Geocoder.configuration.cache.is_a?(Hash)
     GC.start
   end
   ```

**Results**: 86% memory leak reduction (7.125MB → 1.0MB per test cycle)

**Files Modified**:
- `app/controllers/projects_controller.rb` - Added cache cleanup
- `config/initializers/geocoder.rb` - Enabled cache configuration
- Added memory debugging tools for future monitoring

**Monitoring**: Use `/memory_debug/test_projects_leak` endpoint to verify the fix remains effective.

### Memory Debugging Tools

**Real-time Monitoring**:
- `/memory_debug/stats` - Real-time memory statistics and object counts
- `/memory_debug/heap_dump` - Create heap dumps for detailed analysis
- `/memory_debug/test_projects_leak` - Test geocoder-specific memory leaks
- `/memory_debug/force_gc` - Force garbage collection
- `./test_memory_leak.rb` - Automated memory leak testing script

**Common Memory Troubleshooting**:
- **High memory usage**: Use `/memory_debug/stats` to check current memory and object counts
- **Suspected memory leak**: Run `./test_memory_leak.rb` to test systematically
- **Production memory limits**: Check geocoder cache clearing is working in logs
- **Emergency memory relief**: Use `/memory_debug/force_gc` endpoint

## Geocoding Issues

**Common Problems**:
- **Location search not working**: Check geocoder configuration in `config/initializers/geocoder.rb`
- **Geocoder errors**: Review logs for API limit issues or network problems
- **Performance issues**: Verify cache is enabled and being cleared properly

## Development Debugging

```bash
# Start server with memory profiling
MEMORY_PROFILING=1 bin/rails server

# Run memory leak tests
ruby test_memory_leak.rb

# Check memory in development
curl http://localhost:3000/memory_debug/stats
```

## Database Issues

### Clean Test Database
```bash
RAILS_ENV=test bundle exec rails db:reset
RAILS_ENV=test bundle exec rails db:migrate
```

### Migration Issues
```bash
bin/rails db:migrate:status
bin/rails db:rollback STEP=1  # Rollback one migration
```

## File Upload Issues

**Upload Status Check**:
```bash
# Check for stuck uploads
bin/rails runner "puts Upload.where(status: ['transferred', 'processing']).where('updated_at < ?', 30.minutes.ago).count"

# Cleanup stuck uploads
bin/rails runner "Upload.where(status: ['transferred', 'processing']).where('updated_at < ?', 30.minutes.ago).each(&:cleanup_stuck_upload!)"
```

**S3 Key Verification**:
```bash
ruby test_s3_keys.rb
```

**Thumbnail Generation**:
```bash
bin/rails runner "PdfThumbnailGenerationJob.perform_now(Project.find(1))"
```

## Background Jobs Issues

**Job Monitoring**:
- Visit `/good_job` in browser (admin access required)
- Check job status and retry counts

**Job Worker**:
```bash
# Start job worker (production)
bundle exec good_job start

# Check job queue
bin/rails runner "puts GoodJob::Job.count"
```

## Security Testing

**Run Security Tests**:
```bash
bundle exec rspec spec/requests/secure_file_access_spec.rb  # Main secure file tests
bundle exec rspec spec/requests/*security*                  # All security tests
```

## Performance Issues

**APM Monitoring**:
- Check Scout APM dashboard for slow queries
- Use Rack Mini Profiler in development

**Memory Profiling**:
```bash
# Enable memory profiling
MEMORY_PROFILING=1 bin/rails server

# Check object allocations
bundle exec rake memory:check
```

## Common Error Patterns

### Authentication Issues
- Check `ENV['INVITE_ONLY']` setting
- Verify Devise configuration
- Check invitation token validity

### Authorization Issues
- Review ActionPolicy definitions
- Check project approval status
- Verify network connections exist

### File Storage Issues
- Check AWS S3 credentials
- Verify Active Storage configuration
- Check file upload permissions

## Emergency Procedures

### Memory Crisis
1. Use `/memory_debug/force_gc` endpoint
2. Restart Rails server
3. Check geocoder cache clearing
4. Review memory leak test results

### Database Connection Issues
1. Check PostgreSQL connection
2. Verify database.yml configuration
3. Check connection pool settings
4. Restart database if necessary

### File Upload Crisis
1. Check S3 connectivity
2. Review upload queue status
3. Clear stuck uploads
4. Verify thumbnail generation

## Recent Development History

See [`docs/archive/development-sessions/`](../archive/development-sessions/) for detailed session summaries and technical discoveries.