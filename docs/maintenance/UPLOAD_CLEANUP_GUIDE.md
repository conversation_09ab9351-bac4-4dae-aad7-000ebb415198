# Upload Cleanup Guide

**Purpose**: Manual cleanup of stuck uploads, orphaned files, and temp files to prevent storage bloat and maintain system health.

## 📋 Available Rake Tasks

### 1. **Upload Statistics** (Safe - Read Only)
```bash
bundle exec rake cleanup:stats
```
**Purpose**: Get detailed overview of upload system health
- Upload status breakdown
- Time-based analysis
- Stuck upload detection
- Temp file usage
- ActiveStorage statistics

**When to run**: Anytime to assess system state

---

### 2. **Stuck Uploads Only** (Safe - Targeted Cleanup)
```bash
bundle exec rake cleanup:stuck_only
```
**Purpose**: Clean only uploads stuck in `transferred` or `processing` state for 2+ hours
- Updates status to `aborted`
- Cleans temp files
- Does not touch S3 or ActiveStorage

**When to run**: 
- Daily or when users report stuck uploads
- Before major deployments
- When temp disk space is high

---

### 3. **S3 Orphan Analysis** (Safe - Read Only)
```bash
bundle exec rake cleanup:analyze_s3_orphans
```
**Purpose**: Identify S3 files not referenced by ActiveStorage
- Lists potentially orphaned S3 objects
- Shows sizes and dates
- Does NOT delete anything

**When to run**: Monthly to assess S3 storage efficiency

---

### 4. **Full Cleanup** (Comprehensive)
```bash
bundle exec rake cleanup:uploads
```
**Purpose**: Complete cleanup including:
- Stuck uploads (2+ hours old)
- Failed uploads (7+ days old) 
- Orphaned temp files (24+ hours old)
- S3 orphan analysis (read-only)

**When to run**: Weekly/monthly maintenance

## 🛡️ Safety Features

### Built-in Safeguards
- **Time-based filters**: Only processes old files
- **Status validation**: Respects upload state machine
- **Error handling**: Continues on individual failures
- **Detailed logging**: Shows exactly what's being cleaned
- **S3 read-only**: Never automatically deletes S3 files

### Before Running
1. **Check current stats**: `rake cleanup:stats`
2. **Backup consideration**: Database backups should be current
3. **Off-peak timing**: Run during low-usage periods
4. **Monitor logs**: Watch for errors during execution

## 📅 Recommended Schedule

### Daily (Automated Safe)
```bash
# Add to crontab when ready for automation
0 2 * * * cd /path/to/app && bundle exec rake cleanup:stuck_only
```

### Weekly (Manual Review)
```bash
# Run these manually with review
bundle exec rake cleanup:stats
bundle exec rake cleanup:uploads  # Review output before S3 cleanup
```

### Monthly (Deep Analysis)
```bash
bundle exec rake cleanup:analyze_s3_orphans
# Review orphaned S3 files
# Manual S3 cleanup if needed
```

## 🔍 Sample Output Analysis

### Normal Healthy System
```
📊 UPLOAD STATISTICS
Completed      : 150
Failed         : 5
Cancelled      : 2
Processing     : 0

Currently stuck: 0
Temp files: 10
Temp space used: 2.1 MB
```

### System Needing Cleanup
```
📊 UPLOAD STATISTICS  
Completed      : 66
Failed         : 16      ← High failure rate
Processing     : 1       ← Potentially stuck

Currently stuck: 3       ← Needs cleanup
Temp files: 175         ← High temp file count
Temp space used: 10.1 MB ← Significant temp usage
```

## 🚨 When to Investigate Further

### High Failure Rates
- **Failed > 10%** of total uploads
- **Action**: Check logs, S3 connectivity, worker processes

### Persistent Stuck Uploads  
- **Same uploads stuck** after cleanup
- **Action**: Check GoodJob worker status, investigate specific errors

### Large Temp File Accumulation
- **Temp usage > 50MB**
- **Action**: Run full cleanup, check for process failures

### S3 Orphan Growth
- **Orphaned size > 100MB**
- **Action**: Manual S3 review and cleanup

## 🔧 Manual S3 Cleanup (Advanced)

**Only after thorough analysis of orphaned files:**

```bash
# 1. Analyze orphans first
bundle exec rake cleanup:analyze_s3_orphans

# 2. Manual review of specific files
bundle exec rails console
# Check if specific S3 key should exist:
ActiveStorage::Blob.find_by(key: "uploads/2025/06/25/pdf/abc123.pdf")

# 3. AWS CLI cleanup (if confirmed orphaned)
aws s3 rm s3://bucket-name/uploads/2025/06/25/pdf/abc123.pdf
```

## ⚡ Emergency Procedures

### Disk Space Critical
```bash
# Quick temp file cleanup
find tmp/uploads -type f -mtime +1 -delete

# Emergency stuck upload cleanup  
bundle exec rake cleanup:stuck_only
```

### All Uploads Failing
```bash
# Check system health
bundle exec rake cleanup:stats

# Check GoodJob status
bundle exec rails console
GoodJob::Job.where(finished_at: nil).count

# Restart upload processing
sudo systemctl restart app-worker  # or equivalent
```

## 📝 Monitoring Integration

### Success Metrics
- Stuck uploads: 0
- Failed upload rate: < 5%
- Temp file usage: < 25MB
- Processing time: < 30 seconds average

### Alert Thresholds
- Stuck uploads > 5
- Temp files > 100MB
- Failed uploads > 20% in 24h
- S3 orphans growing > 50MB/week

---

## 🎯 Summary

**Start with**: `rake cleanup:stats` to understand current state
**Regular use**: `rake cleanup:stuck_only` for safe daily cleanup  
**Deep clean**: `rake cleanup:uploads` for weekly maintenance
**Advanced**: S3 orphan analysis and manual cleanup monthly

The tasks are designed to be safe and informative, with manual review required for destructive S3 operations.