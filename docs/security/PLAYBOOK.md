# Security Incident Response Playbook

**⚡ FOR EMERGENCIES**: Skip to relevant playbook section below

## Emergency Contacts

- **On-Call Lead**: Check PagerDuty
- **Engineering Manager**: [Contact via Slack]
- **Security Point of Contact**: <EMAIL>

## Triage Steps (First Responder)

1. [ ] **Acknowledge** incident in monitoring system
2. [ ] **Assess** severity (Critical/High/Medium/Low)
3. [ ] **Alert** appropriate team members
4. [ ] **Act** following relevant playbook below
5. [ ] **Document** actions taken in incident log

---

## 🚨 Playbook: JWT Secret Compromised

**Severity**: CRITICAL  
**Impact**: All user sessions compromised

### Immediate Actions
1. [ ] **STOP** - Don't panic, follow steps
2. [ ] **ROTATE** secret in Rails credentials:
   ```bash
   EDITOR=vim rails credentials:edit
   # Change secret_key_base value
   ```
3. [ ] **DEPLOY** immediately to production
4. [ ] **INVALIDATE** all sessions:
   ```ruby
   # Rails console
   User.update_all(updated_at: Time.current)
   ```
5. [ ] **NOTIFY** users of required re-login
6. [ ] **INVESTIGATE** how secret was exposed

### Post-Incident
- [ ] Schedule security review
- [ ] Update secret rotation procedures
- [ ] Document lessons learned

---

## 🔐 Playbook: Authentication Bypass Detected

**Severity**: CRITICAL  
**Impact**: Unauthorized access to user data

### Immediate Actions
1. [ ] **IDENTIFY** affected endpoints
2. [ ] **PATCH** vulnerability:
   ```ruby
   # Add to affected controller
   before_action :authenticate_user!
   ```
3. [ ] **DEPLOY** fix immediately
4. [ ] **AUDIT** access logs for exploitation:
   ```bash
   grep "403\|401" log/production.log | tail -1000
   ```
5. [ ] **CHECK** for data exfiltration
6. [ ] **NOTIFY** affected users if data accessed

### Investigation Checklist
- [ ] Which endpoints were vulnerable?
- [ ] How long was vulnerability exposed?
- [ ] Was it actively exploited?
- [ ] What data could be accessed?

---

## 📧 Playbook: Security Vulnerability Report

**Severity**: Variable  
**Response Time**: 48 hours

### Initial Response
1. [ ] **ACKNOWLEDGE** receipt within 48 hours:
   ```
   Subject: Re: Security Report - [TICKET-ID]
   
   Thank you for your security report. We take security seriously 
   and are investigating your findings. We'll update you within 
   [X] business days.
   ```
2. [ ] **CREATE** confidential issue in tracker
3. [ ] **ASSIGN** to security lead
4. [ ] **VALIDATE** the reported issue

### If Valid
1. [ ] **ASSESS** severity and impact
2. [ ] **DEVELOP** fix in private branch
3. [ ] **TEST** fix thoroughly
4. [ ] **DEPLOY** when ready
5. [ ] **NOTIFY** reporter of resolution
6. [ ] **CREDIT** reporter (if desired)

### If Invalid
1. [ ] **EXPLAIN** why issue isn't a vulnerability
2. [ ] **THANK** reporter for diligence
3. [ ] **CLOSE** ticket

---

## 🔥 Playbook: Active Attack (XSS/SQLi)

**Severity**: CRITICAL  
**Impact**: Active exploitation

### Immediate Mitigation
1. [ ] **BLOCK** attacking IPs:
   ```ruby
   # config/initializers/rack_attack.rb
   Rack::Attack.blocklist('block-attacker') do |req|
     ['*******', '*******'].include?(req.ip)
   end
   ```
2. [ ] **DEPLOY** IP blocks immediately
3. [ ] **ENABLE** WAF rules (if available)
4. [ ] **INCREASE** rate limiting:
   ```ruby
   throttle('strict/ip', limit: 10, period: 1.minute)
   ```

### Investigation
1. [ ] **COLLECT** attack patterns from logs
2. [ ] **IDENTIFY** vulnerable endpoints
3. [ ] **PATCH** vulnerabilities
4. [ ] **SCAN** for backdoors/persistence

---

## 📊 Playbook: Rate Limit Bypass

**Severity**: HIGH  
**Impact**: DoS potential

### Quick Fix
1. [ ] **VERIFY** rate limits working:
   ```bash
   curl -I http://app.domain/api/endpoint
   # Check X-RateLimit headers
   ```
2. [ ] **TIGHTEN** limits if needed:
   ```ruby
   # Reduce from 60 to 30 requests
   throttle('api/ip', limit: 30, period: 1.minute)
   ```
3. [ ] **MONITOR** for continued abuse
4. [ ] **IMPLEMENT** additional limiting by user_id

---

## 📁 Playbook: File Access Vulnerability

**Severity**: HIGH  
**Impact**: Unauthorized file access

### Immediate Actions
1. [ ] **DISABLE** file serving if critical
2. [ ] **AUDIT** recent file access:
   ```sql
   SELECT * FROM logs 
   WHERE action = 'file_access' 
   AND created_at > NOW() - INTERVAL '24 hours';
   ```
3. [ ] **PATCH** authorization checks
4. [ ] **VERIFY** fix with security tests:
   ```bash
   bundle exec rspec spec/requests/authorization_security_spec.rb
   ```

---

## 🔄 Post-Incident Procedures

### Within 24 Hours
1. [ ] Create incident report
2. [ ] Update relevant documentation
3. [ ] Notify stakeholders

### Within 1 Week
1. [ ] Conduct post-mortem
2. [ ] Update security tests
3. [ ] Review and update playbooks
4. [ ] Share lessons learned

### Documentation Template
```markdown
## Incident Report - [DATE]

**Severity**: Critical/High/Medium/Low
**Duration**: [Start] - [End]
**Impact**: [Users/Data affected]

### Timeline
- HH:MM - Issue detected
- HH:MM - First response
- HH:MM - Mitigation applied
- HH:MM - Resolution confirmed

### Root Cause
[Technical explanation]

### Lessons Learned
[What went well, what didn't]

### Action Items
- [ ] [Preventive measure 1]
- [ ] [Preventive measure 2]
```

---

**Remember**: Speed is important, but accuracy is critical. When in doubt, escalate.