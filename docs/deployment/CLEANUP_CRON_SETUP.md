# Automated Upload Cleanup Setup

## 🕒 Cron Job Configuration

### For Production Server

Add to crontab (`crontab -e`):

```bash
# Upload cleanup - runs daily at 2 AM
0 2 * * * cd /home/<USER>/unlisters_app && /home/<USER>/.rbenv/shims/bundle exec rake cleanup:stuck_only RAILS_ENV=production >> /var/log/upload_cleanup.log 2>&1

# Weekly comprehensive cleanup - runs Sundays at 3 AM  
0 3 * * 0 cd /home/<USER>/unlisters_app && /home/<USER>/.rbenv/shims/bundle exec rake cleanup:uploads RAILS_ENV=production >> /var/log/upload_cleanup_weekly.log 2>&1

# Monthly S3 analysis - runs first day of month at 4 AM
0 4 1 * * cd /home/<USER>/unlisters_app && /home/<USER>/.rbenv/shims/bundle exec rake cleanup:analyze_s3_orphans RAILS_ENV=production >> /var/log/s3_analysis.log 2>&1
```

### For Development/Staging

```bash
# Less frequent cleanup for development
0 6 * * 1 cd /home/<USER>/unlisters_app && bundle exec rake cleanup:stuck_only RAILS_ENV=development >> tmp/cleanup.log 2>&1
```

## 📧 Email Notifications (Optional)

### Setup Email Alerts for Issues

Create wrapper script at `bin/cleanup_with_alerts`:

```bash
#!/bin/bash
cd /home/<USER>/unlisters_app

# Run cleanup and capture output
OUTPUT=$(bundle exec rake cleanup:stuck_only RAILS_ENV=production 2>&1)
EXIT_CODE=$?

# Log output
echo "$OUTPUT" >> /var/log/upload_cleanup.log

# Check for issues and send email if problems found
if [ $EXIT_CODE -ne 0 ] || echo "$OUTPUT" | grep -q "❌"; then
    echo "$OUTPUT" | mail -s "Upload Cleanup Issues - $(date)" <EMAIL>
fi
```

Update crontab to use wrapper:
```bash
0 2 * * * /home/<USER>/unlisters_app/bin/cleanup_with_alerts
```

## 🔧 Systemd Service (Alternative to Cron)

### Create service file: `/etc/systemd/system/upload-cleanup.service`

```ini
[Unit]
Description=Upload Cleanup Service
After=network.target

[Service]
Type=oneshot
User=deploy
WorkingDirectory=/home/<USER>/unlisters_app
Environment=RAILS_ENV=production
ExecStart=/home/<USER>/.rbenv/shims/bundle exec rake cleanup:stuck_only
StandardOutput=append:/var/log/upload_cleanup.log
StandardError=append:/var/log/upload_cleanup.log
```

### Create timer file: `/etc/systemd/system/upload-cleanup.timer`

```ini
[Unit]
Description=Run upload cleanup daily
Requires=upload-cleanup.service

[Timer]
OnCalendar=daily
Persistent=true

[Install]
WantedBy=timers.target
```

### Enable and start:
```bash
sudo systemctl enable upload-cleanup.timer
sudo systemctl start upload-cleanup.timer
sudo systemctl status upload-cleanup.timer
```

## 📊 Monitoring Integration

### Prometheus/Grafana Metrics

Add to rake task output for metrics collection:

```ruby
# In cleanup rake task
File.write('/tmp/upload_cleanup_metrics.prom', <<~METRICS)
  # HELP upload_cleanup_stuck_count Number of stuck uploads cleaned
  # TYPE upload_cleanup_stuck_count gauge
  upload_cleanup_stuck_count #{stuck_uploads_cleaned}
  
  # HELP upload_cleanup_temp_files Number of temp files removed  
  # TYPE upload_cleanup_temp_files gauge
  upload_cleanup_temp_files #{orphaned_temp_files}
  
  # HELP upload_cleanup_space_freed Bytes of space freed
  # TYPE upload_cleanup_space_freed gauge  
  upload_cleanup_space_freed #{total_space_freed}
METRICS
```

### Health Check Integration

```bash
# Add to existing health check endpoint
curl -f http://localhost:3000/health || echo "CRITICAL: App health check failed after cleanup"
```

## 🚨 Alerting Rules

### Recommended Alert Conditions

```yaml
alerts:
  - alert: HighStuckUploads
    expr: upload_cleanup_stuck_count > 10
    for: 0m
    labels:
      severity: warning
    annotations:
      summary: "High number of stuck uploads detected"
      
  - alert: ExcessiveTempFiles
    expr: upload_cleanup_temp_files > 100
    for: 0m
    labels:
      severity: warning
    annotations:
      summary: "Excessive temp file accumulation"
      
  - alert: CleanupFailure
    expr: time() - upload_cleanup_last_success > 172800  # 48 hours
    for: 0m
    labels:
      severity: critical
    annotations:
      summary: "Upload cleanup hasn't run successfully in 48 hours"
```

## 🔍 Log Rotation

### Setup logrotate for cleanup logs

Create `/etc/logrotate.d/upload-cleanup`:

```
/var/log/upload_cleanup*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 deploy deploy
    postrotate
        # Send signal to restart logging if needed
    endscript
}
```

## 🧪 Testing Automation

### Before Enabling Cron Jobs

1. **Test manual execution**:
   ```bash
   cd /home/<USER>/unlisters_app
   RAILS_ENV=production bundle exec rake cleanup:stuck_only
   ```

2. **Test with cron environment**:
   ```bash
   # Run with minimal environment like cron
   env -i HOME=/home/<USER>/usr/bin:/bin bash -c "cd /home/<USER>/unlisters_app && bundle exec rake cleanup:stuck_only RAILS_ENV=production"
   ```

3. **Test logging**:
   ```bash
   # Ensure log files are writable
   touch /var/log/upload_cleanup.log
   chown deploy:deploy /var/log/upload_cleanup.log
   ```

4. **Run test cron job**:
   ```bash
   # Add temporary 5-minute test job
   */5 * * * * cd /home/<USER>/unlisters_app && bundle exec rake cleanup:stats RAILS_ENV=production >> /tmp/test_cleanup.log 2>&1
   ```

## 📋 Deployment Checklist

- [ ] Test all rake tasks manually in production environment
- [ ] Verify log file permissions and rotation
- [ ] Test cron environment and paths
- [ ] Setup monitoring/alerting for cleanup failures
- [ ] Document recovery procedures
- [ ] Schedule regular review of S3 orphan analysis
- [ ] Plan manual S3 cleanup procedures

## 🚀 Gradual Rollout Plan

### Week 1: Manual Only
- Run `cleanup:stats` daily to establish baseline
- Run `cleanup:stuck_only` manually when needed

### Week 2: Automated Stuck Cleanup
- Enable daily stuck upload cleanup via cron
- Monitor logs and effectiveness

### Week 3: Weekly Full Cleanup
- Add weekly comprehensive cleanup
- Review and tune time thresholds

### Week 4: S3 Analysis
- Add monthly S3 orphan analysis
- Plan first manual S3 cleanup based on findings

This gradual approach ensures each automation step is working correctly before adding the next layer.