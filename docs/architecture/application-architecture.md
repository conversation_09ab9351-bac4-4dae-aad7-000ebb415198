# Application Architecture

## Overview

Unlisters App is built on Ruby on Rails with sophisticated authentication, authorization, and content sharing systems.

## Authentication & Authorization Flow

- **Invitation-only registration** system (controlled by ENV['INVITE_ONLY'])
- **Three-tier user roles**: regular, admin, super_boss
- **Network connections** are bidirectional and created automatically upon invitation acceptance
- **Project access** controlled via ProjectAuth with different access levels

## Project Visibility & Access Control

Projects have sophisticated privacy controls:
- **Visibility**: `network_only` (connected users) vs `semi_public` (all registered users)
- **Access Level**: `summary_only` (title/location) vs `full_access` (all details + files)
- **Approval Workflow**: New/modified projects require admin approval before publication
- **Geographic Search**: Projects are geocoded and searchable by location

## Internationalization

- **Default locale**: Slovak (:sk)
- **Fallback locale**: English (:en)
- **Timezone**: CET
- **Translations**: Organized in config/locales/ with nested directories

## Email System

- **Service**: Resend for transactional emails
- **Notifications**: Connection requests, project approvals, admin alerts
- **Testing**: Mailer previews available in test/mailers/previews/

## Background Jobs System

- **Framework**: GoodJob with PostgreSQL for reliable, persistent job queuing
- **Admin Dashboard**: `/good_job` for job monitoring (admin access only)
- **Production**: Render Worker Service processes jobs asynchronously
- **Primary Use Case**: Email jobs for background processing
- **Persistence**: Jobs survive server restarts and deployments
- **Retry Strategy**: Exponential backoff for handling S3 upload race conditions
  - Professional pattern: `retry_on CustomError, wait: :exponentially_longer`
  - No hardcoded delays - adapts to actual conditions

## Performance & Monitoring

- **APM**: Scout APM for application monitoring
- **Development**: Rack Mini Profiler for performance analysis
- **Memory**: Profiling tools (memory_profiler, heapy)
- **Rate Limiting**: Rack::Attack for protection

## Memory Leak Debugging Tools

- `/memory_debug/stats` - Real-time memory statistics and object counts
- `/memory_debug/heap_dump` - Create heap dumps for detailed analysis
- `/memory_debug/test_projects_leak` - Test geocoder-specific memory leaks
- `/memory_debug/force_gc` - Force garbage collection
- `./test_memory_leak.rb` - Automated memory leak testing script

## Testing Architecture

- **Framework**: RSpec as primary testing framework
- **Database**: Test database uses transactional fixtures
- **Files**: Test fixtures stored in spec/fixtures/files/
- **Email**: Mailer tests with preview capabilities
- **Coverage**: Model, controller, and integration test coverage

## Non-Obvious Architectural Decisions

### Bidirectional Network Connections

Unlike typical follower/following patterns, this app implements **bidirectional connections** where inviting someone automatically creates a mutual connection. This is handled through complex queries in `User#network_connections`:

```ruby
def network_connections
  NetworkConnection.where('inviter_id = ? OR invitee_id = ?', id, id)
end
```

### Dual Project Visibility System

Projects have a **two-dimensional access control** that's not immediately obvious:
- **Audience dimension**: `network_only` vs `semi_public` (who can see it exists)
- **Detail dimension**: `summary_only` vs `full_access` (what they can see)

This creates 4 possible combinations, managed through complex scoping in `Project.full_list_for_user`.

### Automatic Connection Creation on Invitation

The app automatically creates network connections when users accept invitations via `after_invitation_accepted :create_network_connection`. This tight coupling between authentication and social networking isn't typical.

### Admin Approval Workflow with State Management

Projects automatically become **unapproved** when summary changes, requiring admin re-approval. This is handled through `set_approval_status_based_on_changes` and virtual attributes like `admin_approver` and `is_admin_approval_action`.

### Invitation-Only Registration Override

The app can toggle between open and invitation-only registration via `ENV['INVITE_ONLY']`, validated in the User model rather than at the controller level.

### Complex Project Type Hierarchy

The nested `PROJECT_TYPES` and `CATEGORIES` constants create a three-level taxonomy (project_type → category → subcategory) with validation dependencies that aren't obvious from the schema alone.

## Common Development Patterns

### Policy-Based Authorization

Use ActionPolicy for authorization checks:
```ruby
authorize! user_profile, to: :show?
```

### Geocoding Integration

Projects are automatically geocoded when location changes. Search by coordinates supported.

### Enum Usage

Project model uses extensive enums for project_type, category, and subcategory with translation support.

### Invitation System

Users can only be created through invitations. Network connections are automatically established upon invitation acceptance.