# Claude CLI MCP Configuration for Unlisters

## Current MCP Server Status
- ✅ **gmail**: Connected and working
- ✅ **puppeteer**: Connected and working  
- ❌ **playwright**: Failed to connect
- ✅ **filesystem**: Installed and configured
- ✅ **postgres**: Installed and configured

## Quick Start

### Use the Helper Script
```bash
# Run Claude with all MCP servers configured
./scripts/claude-with-mcp.sh
```

### Manual Command
```bash
claude \
  --mcp gmail:"npx -y @modelcontextprotocol/server-gmail" \
  --mcp puppeteer:"npx -y @modelcontextprotocol/server-puppeteer" \
  --mcp filesystem:"npx -y @modelcontextprotocol/server-filesystem /home/<USER>/Projects/unlisters_app" \
  --mcp postgres:"npx -y @modelcontextprotocol/server-postgres postgresql://localhost/unlisters_development"
```

## MCP Server Capabilities

### 1. Gmail MCP Server
- Send emails
- Read emails
- Draft emails
- Manage labels
- Already configured to <NAME_EMAIL>

### 2. Puppeteer MCP Server
- Browser automation
- Take screenshots
- Click elements
- Fill forms
- Navigate pages
- Execute JavaScript

### 3. Filesystem MCP Server
- Browse project directories
- Read files
- Write files
- Search for files
- Monitor file changes

### 4. PostgreSQL MCP Server
- Execute SQL queries
- Inspect database schema
- View table structures
- Analyze relationships
- Test queries

### 5. Playwright MCP Server (Currently Failing)
- Similar to Puppeteer but with more features
- Cross-browser testing
- Better selector engine
- Network interception

## Troubleshooting

### Check MCP Status
```bash
# In Claude CLI
mcp

# With debug mode
claude --mcp-debug
```

### View MCP Logs
```bash
# Location mentioned in mcp status output
ls ~/.cache/claude-cli-nodejs/-home-mm-Projects-unlisters_app
```

## Creating Aliases

Add to your `~/.bashrc` or `~/.zshrc`:

```bash
# Claude with all MCP servers for Unlisters
alias claude-app='/home/<USER>/Projects/unlisters_app/scripts/claude-with-mcp.sh'

# Claude with MCP debug mode
alias claude-debug='claude --mcp-debug'

# Claude with specific MCP server
alias claude-fs='claude --mcp filesystem:"npx -y @modelcontextprotocol/server-filesystem /home/<USER>/Projects/unlisters_app"'
alias claude-db='claude --mcp postgres:"npx -y @modelcontextprotocol/server-postgres postgresql://localhost/unlisters_development"'
```

## Usage Examples

### 1. Analyze Code
```
# Using filesystem MCP
mcp filesystem list_directory /home/<USER>/Projects/unlisters_app/app/controllers
mcp filesystem read_file /home/<USER>/Projects/unlisters_app/app/controllers/application_controller.rb
```

### 2. Database Schema Inspection
```
# Using postgres MCP
mcp postgres query "SELECT * FROM information_schema.tables WHERE table_schema = 'public'"
mcp postgres query "\\d users"
```

### 3. Test App Endpoints
```
# Using puppeteer MCP
mcp puppeteer navigate http://localhost:3000/login
mcp puppeteer screenshot login-page
mcp puppeteer fill "#email" "<EMAIL>"
```

### 4. Send Status Updates
```
# Using gmail MCP
mcp gmail send_email {
  "to": ["<EMAIL>"],
  "subject": "Development Progress Update",
  "body": "Completed feature implementation"
}
```

## Helper Scripts Location
All helper scripts are in `/home/<USER>/Projects/unlisters_app/scripts/`:
- `claude-with-mcp.sh` - Start Claude with all MCP servers
- `setup_claude_cli_mcp.sh` - Setup MCP servers

## Next Steps
1. Use `./scripts/claude-with-mcp.sh` to start Claude with MCP
2. Test each MCP server individually
3. Begin development with enhanced tooling