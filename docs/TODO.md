# TODO

## Bugs to Fix

### File Upload Form Reset Bug
- **Issue**: When uploading files during project editing, all previously entered form data is lost
- **Reported by**: <PERSON><PERSON>
- **Date**: July 17, 2025
- **Steps to reproduce**:
  1. Edit existing project
  2. Fill out various form fields
  3. Upload attachments
  4. All previous form changes disappear
- **Status**: New
- **Priority**: High (affects UX)
- **GitHub Issue**: https://github.com/codecraftmario/unlisters/issues/34

## Security Improvements

### AWS S3 Malware Scanning Implementation  
- **Issue**: Current file upload validation only checks MIME type headers (easily spoofed)
- **Current Risk**: Malicious files can be uploaded by changing request headers
- **Proposed Solution**: Implement AWS S3 built-in malware scanning
- **Benefits**: 
  - Scans actual file content, not just headers
  - Detects malware and suspicious files automatically
  - Enterprise-level security solution
  - Integrates with existing S3 infrastructure
- **Implementation**: 
  - Enable S3 malware scanning on amazon_uploads and amazon_thumbnails buckets
  - Update `validate_private_files` method to handle scan results
  - Add proper error handling for infected files
  - Update upload processing workflow
- **Files to modify**: `app/controllers/projects_controller.rb`, storage configs
- **Date**: July 17, 2025
- **Status**: Identified
- **Priority**: Medium (security improvement)