#!/usr/bin/env ruby
# Vite Configuration Preflight Check & Health Monitor
# Comprehensive diagnostic tool for Vite + Rails integration
# Prevents and diagnoses the 0.0.0.0 connection loop issue

require 'json'
require 'net/http'
require 'uri'
require 'timeout'
require 'open3'

class ViteHealthCheck
  RED = "\e[31m"
  GREEN = "\e[32m"
  YELLOW = "\e[33m"
  BLUE = "\e[34m"
  RESET = "\e[0m"

  def initialize
    @rails_env = ENV['RAILS_ENV'] || 'development'
    @errors = []
    @warnings = []
    @successes = []
    @checks_passed = true
    
    # Load vite.json with proper error handling
    @vite_json = begin
      JSON.parse(File.read('config/vite.json'))
    rescue Errno::ENOENT
      @errors << "config/vite.json not found"
      {}
    rescue JSON::ParserError => e
      @errors << "Invalid JSON in config/vite.json: #{e.message}"
      {}
    end
  end

  def run
    puts "\n#{BLUE}🚀 Vite Configuration Preflight Check - #{@rails_env.capitalize} Environment#{RESET}\n"
    puts "#{BLUE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━#{RESET}\n\n"
    
    case @rails_env
    when 'development'
      puts "#{YELLOW}Vite provides:#{RESET}"
      puts "  • Hot Module Replacement (instant updates without page reload)"
      puts "  • Native ES Modules (fast on-demand loading)"
      puts "  • Sub-second build times with esbuild\n\n"
      check_development_setup
    when 'production'
      check_production_setup
    when 'test'
      check_test_setup
    end
    
    # Check for CI-specific issues
    check_ci_environment if ENV['CI']
    
    print_results
    exit(@checks_passed ? 0 : 1)
  end

  private

  def check_development_setup
    puts "#{YELLOW}Development Environment Checks:#{RESET}\n\n"
    
    # Core checks from test_vite_configuration.rb
    test_configuration_files
    test_environment_variables
    test_vite_json_validity
    test_vite_config_ts_validity
    test_server_accessibility
    test_client_connection_urls
    test_hmr_configuration
    test_version_compatibility
  end

  def test_configuration_files
    puts "1️⃣  Checking configuration files..."
    
    if File.exist?('config/vite.json')
      @successes << "✓ config/vite.json exists"
    else
      @errors << "✗ config/vite.json is missing"
    end
    
    if File.exist?('vite.config.ts')
      @successes << "✓ vite.config.ts exists"
    else
      @errors << "✗ vite.config.ts is missing"
    end
    
    if File.exist?('package.json')
      @successes << "✓ package.json exists"
    else
      @errors << "✗ package.json is missing"
    end
  end

  def test_environment_variables
    puts "\n2️⃣  Checking environment variables..."
    
    # Comprehensive list of variables that can cause issues
    suspicious_vars = %w[
      VITE_RUBY_HOST VITE_RUBY_PORT VITE_RUBY_MODE
      VITE_HOST VITE_PORT HOST PORT
    ]
    found_vars = []
    
    suspicious_vars.each do |var|
      if ENV[var]
        value = ENV[var]
        if var == 'VITE_RUBY_MODE' && value == 'production' && @rails_env == 'development'
          @errors << "✗ #{var}=#{value} will force production mode in development!"
        elsif value == '0.0.0.0'
          @errors << "✗ #{var}=#{value} will cause client connection issues!"
        else
          found_vars << "#{var}=#{value}"
        end
      end
    end
    
    if found_vars.empty? && @errors.empty?
      @successes << "✓ No conflicting environment variables found"
    elsif !found_vars.empty?
      @warnings << "⚠ Found environment variables that might override config: #{found_vars.join(', ')}"
    end
  end

  def test_vite_json_validity
    puts "\n3️⃣  Analyzing config/vite.json..."
    
    return if @vite_json.empty? # Already reported error in initialize
    
    dev_config = @vite_json['development']
    
    if dev_config
      host = dev_config['host'] || 'localhost'
      port = dev_config['port'] || 3036
      
      # Check host configuration
      if host == '0.0.0.0'
        @successes << "✓ Host is set to 0.0.0.0 (allows network access)"
        
        # When host is 0.0.0.0, hmr_host should be set or plugin should handle it
        if dev_config['hmr_host']
          if dev_config['hmr_host'] == 'localhost' || dev_config['hmr_host'] == '127.0.0.1'
            @successes << "✓ hmr_host is properly set to #{dev_config['hmr_host']}"
          else
            @errors << "✗ hmr_host is set to #{dev_config['hmr_host']} - should be localhost or 127.0.0.1"
          end
        else
          @warnings << "⚠ hmr_host not set - vite-plugin-ruby should handle this automatically"
        end
      elsif host == 'localhost' || host == '127.0.0.1'
        @successes << "✓ Host is set to #{host} (local access only)"
      else
        @warnings << "⚠ Unusual host setting: #{host}"
      end
      
      @successes << "✓ Port is set to #{port}"
    else
      @errors << "✗ No development configuration found in vite.json"
    end
  end

  def test_vite_config_ts_validity
    puts "\n4️⃣  Analyzing vite.config.ts..."
    
    begin
      content = File.read('vite.config.ts')
      
      # Check for server block - this is the main issue!
      if content.match(/server\s*:\s*{/)
        server_match = content.match(/server\s*:\s*{[^}]*}/m)
        
        if server_match
          server_config = server_match[0]
          
          # Check for problematic host configurations
          if server_config.match(/host\s*:\s*['"]0\.0\.0\.0['"]/)
            @errors << "✗ Found 'server' block with host: '0.0.0.0' - this overrides vite-plugin-ruby!"
          end
          
          # Check for HMR host issues
          if server_config.match(/hmr\s*:.*?host\s*:\s*['"]0\.0\.0\.0['"]/m)
            @errors << "✗ HMR is configured to use 0.0.0.0 - WebSocket will fail!"
          end
          
          # Warning for any server block
          if @errors.empty?
            @warnings << "⚠ Server block found - may override vite-plugin-ruby settings"
          end
        end
      else
        @successes << "✓ No server block found (good - letting plugin manage)"
      end
      
      # Check for RubyPlugin
      if content.match(/RubyPlugin\(\)/)
        @successes << "✓ RubyPlugin is configured"
      else
        @errors << "✗ RubyPlugin not found in vite.config.ts"
      end
    rescue => e
      @errors << "✗ Error reading vite.config.ts: #{e.message}"
    end
  end

  def test_server_accessibility
    puts "\n5️⃣  Testing server accessibility..."
    
    port = @vite_json.dig('development', 'port') || 3036
    
    begin
      # Use /@vite/client as it's a reliable endpoint
      response = Timeout.timeout(2) do
        Net::HTTP.get_response(URI("http://localhost:#{port}/@vite/client"))
      end
      
      if response.is_a?(Net::HTTPSuccess)
        @successes << "✓ Vite server is running and accessible on port #{port}"
      else
        @warnings << "⚠ Vite server responded with #{response.code} #{response.message}"
      end
    rescue Timeout::Error
      @warnings << "⚠ Vite server connection timeout"
    rescue Errno::ECONNREFUSED
      @warnings << "⚠ Vite server not running (run 'bin/vite dev' to start)"
    rescue => e
      @warnings << "⚠ Could not test server: #{e.message}"
    end
  end

  def test_client_connection_urls
    puts "\n6️⃣  Testing client connection URLs..."
    
    host = @vite_json.dig('development', 'host') || 'localhost'
    port = @vite_json.dig('development', 'port') || 3036
    
    # Check if vite.config.ts has problematic server block
    vite_config = File.read('vite.config.ts') rescue ''
    has_problematic_server = vite_config.match(/server\s*:.*?host\s*:\s*['"]0\.0\.0\.0['"]/m)
    
    if host == '0.0.0.0' && has_problematic_server
      @errors << "✗ CRITICAL: Client will try to connect to http://0.0.0.0:#{port} (INVALID!)"
      @errors << "  → This causes the infinite connection loop"
    elsif host == '0.0.0.0' && !has_problematic_server
      @successes << "✓ vite-plugin-ruby will translate 0.0.0.0 to localhost for client"
    else
      @successes << "✓ Client will connect to http://#{host}:#{port}"
    end
  end

  def test_hmr_configuration
    puts "\n7️⃣  Testing HMR (Hot Module Replacement) configuration..."
    
    vite_config = File.read('vite.config.ts') rescue ''
    
    if vite_config.match(/hmr\s*:.*?host\s*:\s*['"]0\.0\.0\.0['"]/m)
      @errors << "✗ HMR host is set to 0.0.0.0 - WebSocket connections will fail!"
    elsif vite_config.match(/hmr\s*:/)
      @warnings << "⚠ Custom HMR configuration detected - verify it's correct"
    else
      @successes << "✓ HMR will use default configuration (recommended)"
    end
  end

  def test_version_compatibility
    puts "\n8️⃣  Testing version compatibility..."
    
    # Check vite-plugin-ruby version
    begin
      package_json = JSON.parse(File.read('package.json'))
      vite_plugin_version = package_json.dig('devDependencies', 'vite-plugin-ruby') || 
                           package_json.dig('dependencies', 'vite-plugin-ruby')
      
      if vite_plugin_version
        @successes << "✓ vite-plugin-ruby version: #{vite_plugin_version}"
        
        # Check for known problematic versions
        if vite_plugin_version.match(/[45]\.\d+\.\d+/)
          @successes << "✓ Using supported vite-plugin-ruby version"
        else
          @warnings << "⚠ Untested vite-plugin-ruby version"
        end
      else
        @errors << "✗ vite-plugin-ruby not found in package.json"
      end
    rescue => e
      @errors << "✗ Error checking versions: #{e.message}"
    end
  end

  def check_production_setup
    puts "#{YELLOW}Production Environment Checks:#{RESET}\n\n"
    
    manifest_path = 'public/vite/.vite/manifest.json'
    
    # Check manifest exists and is valid
    check("Manifest file") do
      if File.exist?(manifest_path)
        manifest = JSON.parse(File.read(manifest_path))
        @successes << "✓ Found #{manifest.keys.size} entries in manifest"
        
        # Check for key entrypoints
        key_entrypoints = ['application.js', 'application.css'].select { |e| manifest[e] }
        if key_entrypoints.any?
          @successes << "✓ Key entrypoints found: #{key_entrypoints.join(', ')}"
          
          # Verify actual files exist
          missing_files = []
          key_entrypoints.each do |entry|
            file_path = File.join('public/vite', manifest[entry]['file'])
            unless File.exist?(file_path)
              missing_files << file_path
            end
          end
          
          if missing_files.empty?
            @successes << "✓ All manifest files exist on disk"
          else
            @errors << "✗ Missing files referenced in manifest: #{missing_files.join(', ')}"
          end
        else
          @warnings << "⚠ No standard entrypoints found in manifest"
        end
        
        true
      else
        @errors << "✗ Manifest missing! Run: bin/vite build"
        false
      end
    rescue JSON::ParserError => e
      @errors << "✗ Invalid manifest JSON: #{e.message}"
      false
    end
    
    # Verify no dev server needed
    @successes << "✓ Rails serves static files from public/vite"
    @successes << "✓ No Vite process needed in production"
  end

  def check_test_setup
    puts "#{YELLOW}Test Environment Checks:#{RESET}\n\n"
    
    test_config = @vite_json['test']
    if test_config
      @successes << "✓ Test config found (port #{test_config['port']})"
    else
      @warnings << "⚠ No specific test config (will use development config)"
    end
  end

  def check_ci_environment
    puts "\n#{YELLOW}CI Environment Checks:#{RESET}\n\n"
    
    # Check for headless operation
    @successes << "✓ Running in CI mode"
    
    # Check for common CI environment issues
    if ENV['DISPLAY'].nil?
      @successes << "✓ Headless environment detected (no DISPLAY)"
    end
    
    # Check for port conflicts
    port = @vite_json.dig('development', 'port') || 3036
    begin
      server = TCPServer.new('localhost', port)
      server.close
      @successes << "✓ Port #{port} is available"
    rescue Errno::EADDRINUSE
      @errors << "✗ Port #{port} is already in use!"
    end
  end

  def check(description)
    puts "\n🔍 #{description}..."
    result = yield
    @checks_passed = false unless result
    result
  rescue => e
    @errors << "✗ Error in #{description}: #{e.message}"
    @checks_passed = false
    false
  end

  def print_results
    puts "\n#{BLUE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━#{RESET}"
    puts "#{BLUE}📊 TEST RESULTS#{RESET}"
    puts "#{BLUE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━#{RESET}"
    
    if @successes.any?
      puts "\n#{GREEN}✅ PASSED (#{@successes.length})#{RESET}"
      @successes.each { |s| puts "   #{s}" }
    end
    
    if @warnings.any?
      puts "\n#{YELLOW}⚠️  WARNINGS (#{@warnings.length})#{RESET}"
      @warnings.each { |w| puts "   #{w}" }
    end
    
    if @errors.any?
      puts "\n#{RED}❌ ERRORS (#{@errors.length})#{RESET}"
      @errors.each { |e| puts "   #{e}" }
      
      puts "\n#{RED}🚨 CRITICAL CONFIGURATION ISSUE DETECTED!#{RESET}"
      puts "\nThe Vite configuration is causing client-side code to connect to 'http://0.0.0.0:PORT'."
      puts "This is invalid and causes infinite connection loops in the browser."
      puts "\n#{YELLOW}Common fixes:#{RESET}"
      puts "1. Remove the 'server: { ... }' block from your 'vite.config.ts'"
      puts "2. Ensure 'hmr.host' in 'vite.config.ts' is not set to '0.0.0.0'"
      puts "3. Unset environment variables like VITE_RUBY_HOST if set to '0.0.0.0'"
      puts "4. Use 'config/vite.json' for configuration, let vite-plugin-ruby handle the rest"
    else
      puts "\n#{GREEN}✨ All critical checks passed!#{RESET}"
    end
    
    puts "\n#{BLUE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━#{RESET}\n"
  end
end

# Run the health check
ViteHealthCheck.new.run