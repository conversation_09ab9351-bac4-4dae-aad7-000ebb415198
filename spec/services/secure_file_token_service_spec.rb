# spec/services/secure_file_token_service_spec.rb
require 'rails_helper'

RSpec.describe SecureFileTokenService, type: :service do
  let(:user) { create(:user) }
  let(:project) { create(:project, :minimal, :with_files, user: user) }
  let(:file_attachment) { project.private_files.first }

  describe '.generate_token' do
    it 'returns a non-empty JWT string' do
      token = described_class.generate_token(file_attachment, user)
      expect(token).to be_present
      expect(token).to be_a(String)
      expect(token.split('.').length).to eq(3)  # JWT format check
    end

    context 'when decoding the generated token' do
      let(:token) { described_class.generate_token(file_attachment, user) }
      let(:payload) { described_class.decode_token(token) }

      it 'contains the correct file_id' do
        expect(payload['file_id']).to eq(file_attachment.id)
      end

      it 'contains the correct user_id' do
        expect(payload['user_id']).to eq(user.id)
      end

      it 'contains the correct project_id' do
        expect(payload['project_id']).to eq(project.id)
      end

      it 'sets the expiration to approximately 5 minutes in the future' do
        expected_exp = 5.minutes.from_now.to_i
        expect(payload['exp']).to be_within(5).of(expected_exp)
      end

      it 'includes a unique nonce' do
        expect(payload['nonce']).to be_present
        expect(payload['nonce']).to match(/\A[a-f0-9]{32}\z/)  # 32-char hex
      end
    end
  end

  describe '.decode_token' do
    it 'returns the payload for a valid and unexpired token' do
      token = described_class.generate_token(file_attachment, user)
      payload = described_class.decode_token(token)
      
      expect(payload).to be_present
      expect(payload['file_id']).to eq(file_attachment.id)
    end

    it 'returns nil for a token with an invalid signature' do
      token = described_class.generate_token(file_attachment, user)
      tampered_token = token[0..-2] + 'X'  # Change last character
      
      expect(described_class.decode_token(tampered_token)).to be_nil
    end

    it 'returns nil for a malformed token' do
      expect(described_class.decode_token('not.a.token')).to be_nil
      expect(described_class.decode_token('')).to be_nil
      expect(described_class.decode_token(nil)).to be_nil
    end

    it 'returns nil for an expired token' do
      # Create token with past expiration
      expired_payload = {
        file_id: file_attachment.id,
        user_id: user.id,
        project_id: project.id,
        exp: 1.hour.ago.to_i,
        iat: 2.hours.ago.to_i,
        nonce: SecureRandom.hex(16)
      }
      expired_token = JWT.encode(expired_payload, JWT_SECRET, JWT_ALGORITHM)
      
      expect(described_class.decode_token(expired_token)).to be_nil
    end
  end

  describe '.token_valid?' do
    it 'returns true for a valid and unexpired token' do
      token = described_class.generate_token(file_attachment, user)
      expect(described_class.token_valid?(token)).to be true
    end

    it 'returns false for an expired token' do
      expired_payload = {
        file_id: file_attachment.id,
        user_id: user.id,
        project_id: project.id,
        exp: 1.hour.ago.to_i,
        iat: 2.hours.ago.to_i,
        nonce: SecureRandom.hex(16)
      }
      expired_token = JWT.encode(expired_payload, JWT_SECRET, JWT_ALGORITHM)
      
      expect(described_class.token_valid?(expired_token)).to be false
    end

    it 'returns false for an invalid token' do
      expect(described_class.token_valid?('invalid')).to be false
      expect(described_class.token_valid?(nil)).to be false
    end
  end

  describe 'token uniqueness and security' do
    it 'generates different tokens for the same file and user on multiple calls' do
      token1 = described_class.generate_token(file_attachment, user)
      token2 = described_class.generate_token(file_attachment, user)
      
      expect(token1).not_to eq(token2)
      
      payload1 = described_class.decode_token(token1)
      payload2 = described_class.decode_token(token2)
      expect(payload1['nonce']).not_to eq(payload2['nonce'])
    end

    it 'includes issued at timestamp (iat)' do
      token = described_class.generate_token(file_attachment, user)
      payload = described_class.decode_token(token)
      
      expect(payload['iat']).to be_within(5).of(Time.current.to_i)
    end
  end
end