# ABOUTME: Comprehensive tests for WantPolicy that verify authorization patterns for all CRUD operations
# ABOUTME: Tests owner-based permissions and authenticated user access controls

require 'rails_helper'

RSpec.describe WantPolicy, type: :policy do
  let(:owner) { create(:user) }
  let(:other_user) { create(:user) }
  let(:want) { create(:want, user: owner) }
  
  before do
    # Ensure all users have complete profiles to avoid redirection issues
    [owner, other_user].each do |u|
      u.user_profile.update!(
        first_name: 'Test',
        last_name: 'User',
        city: 'Bratislava', 
        country: 'Slovakia'
      ) if u.user_profile
    end
  end

  describe '#index?' do
    context 'when user is authenticated' do
      it 'allows access' do
        policy = described_class.new(want, user: owner)
        expect(policy.index?).to be true
      end
    end
    
    context 'when user is another authenticated user' do
      it 'allows access' do
        policy = described_class.new(want, user: other_user)
        expect(policy.index?).to be true
      end
    end
    
    # Note: user: nil cases are handled by authentication layer, not policy layer
  end

  describe '#show_my?' do
    context 'when user is authenticated' do
      it 'allows access' do
        policy = described_class.new(want, user: owner)
        expect(policy.show_my?).to be true
      end
    end
    
    context 'when user is another authenticated user' do
      it 'allows access' do
        policy = described_class.new(want, user: other_user)
        expect(policy.show_my?).to be true
      end
    end
    
    # Note: user: nil cases are handled by authentication layer, not policy layer
  end

  describe '#show?' do
    context 'when user is the want owner' do
      it 'allows access' do
        policy = described_class.new(want, user: owner)
        expect(policy.show?).to be true
      end
    end
    
    context 'when user is another authenticated user' do
      it 'allows access' do
        policy = described_class.new(want, user: other_user)
        expect(policy.show?).to be true
      end
    end
    
    # Note: user: nil cases are handled by authentication layer, not policy layer
  end

  describe '#create?' do
    context 'when user is authenticated' do
      it 'allows access' do
        policy = described_class.new(want, user: owner)
        expect(policy.create?).to be true
      end
    end
    
    context 'when user is another authenticated user' do
      it 'allows access' do
        policy = described_class.new(want, user: other_user)
        expect(policy.create?).to be true
      end
    end
    
    # Note: user: nil cases are handled by authentication layer, not policy layer
  end

  describe '#new?' do
    context 'when user is authenticated' do
      it 'allows access (delegates to create?)' do
        policy = described_class.new(want, user: owner)
        expect(policy.new?).to be true
      end
    end
    
    context 'when user is another authenticated user' do
      it 'allows access (delegates to create?)' do
        policy = described_class.new(want, user: other_user)
        expect(policy.new?).to be true
      end
    end
    
    # Note: user: nil cases are handled by authentication layer, not policy layer
  end

  describe '#edit?' do
    context 'when user is the want owner' do
      it 'allows access' do
        policy = described_class.new(want, user: owner)
        expect(policy.edit?).to be true
      end
    end
    
    context 'when user is not the want owner' do
      it 'denies access' do
        policy = described_class.new(want, user: other_user)
        expect(policy.edit?).to be false
      end
    end
    
    # Note: user: nil cases are handled by authentication layer, not policy layer
  end

  describe '#update?' do
    context 'when user is the want owner' do
      it 'allows access' do
        policy = described_class.new(want, user: owner)
        expect(policy.update?).to be true
      end
    end
    
    context 'when user is not the want owner' do
      it 'denies access' do
        policy = described_class.new(want, user: other_user)
        expect(policy.update?).to be false
      end
    end
    
    # Note: user: nil cases are handled by authentication layer, not policy layer
  end

  describe '#destroy?' do
    context 'when user is the want owner' do
      it 'allows access' do
        policy = described_class.new(want, user: owner)
        expect(policy.destroy?).to be true
      end
    end
    
    context 'when user is not the want owner' do
      it 'denies access' do
        policy = described_class.new(want, user: other_user)
        expect(policy.destroy?).to be false
      end
    end
    
    # Note: user: nil cases are handled by authentication layer, not policy layer
  end

  describe 'private #owner?' do
    context 'when user is the want owner' do
      it 'returns true' do
        policy = described_class.new(want, user: owner)
        expect(policy.send(:owner?)).to be true
      end
    end
    
    context 'when user is not the want owner' do
      it 'returns false' do
        policy = described_class.new(want, user: other_user)
        expect(policy.send(:owner?)).to be false
      end
    end
    
    # Note: user: nil cases are handled by authentication layer, not policy layer
  end

  describe 'integration with other wants' do
    let(:other_want) { create(:want, user: other_user) }
    
    it 'correctly identifies ownership for different wants' do
      owner_policy = described_class.new(want, user: owner)
      other_policy = described_class.new(other_want, user: owner)
      
      # User should be owner of their own want
      expect(owner_policy.edit?).to be true
      expect(owner_policy.update?).to be true
      expect(owner_policy.destroy?).to be true
      
      # User should not be owner of other user's want
      expect(other_policy.edit?).to be false
      expect(other_policy.update?).to be false
      expect(other_policy.destroy?).to be false
      
      # But both should allow viewing
      expect(owner_policy.show?).to be true
      expect(other_policy.show?).to be true
    end
  end
end