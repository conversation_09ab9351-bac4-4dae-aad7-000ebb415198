# spec/features/secure_file_display_spec.rb
require 'rails_helper'

RSpec.describe 'Secure File Display', type: :feature, js: true do
  let(:user) { create(:user) }
  let(:project) { create(:project, :minimal, :with_files, user: user) }
  
  before do
    sign_in user
  end

  describe 'Secure File Preview' do
    context 'happy path - image preview' do
      it 'opens lightbox when user clicks on image file' do
        visit project_path(project)
        
        # Find and click on image file
        image_file = find('[data-content-type="image/png"]')
        expect(image_file).to be_present
        
        # Click to open preview
        image_file.find('[data-action="preview"]').click
        
        # Wait for lightbox to appear
        expect(page).to have_css('#secureLightbox:not(.hidden)', wait: 5)
        
        # Verify image loads in lightbox
        within('#secureLightbox') do
          expect(page).to have_css('img', wait: 10)
          expect(page).to have_css('.lightbox-download[style*="inline-flex"]')
        end
      end
    end

    context 'happy path - PDF preview' do
      it 'opens lightbox when user clicks on PDF file' do
        visit project_path(project)
        
        # Find and click on PDF file
        pdf_file = find('[data-content-type="application/pdf"]')
        expect(pdf_file).to be_present
        
        # Click to open preview
        pdf_file.find('[data-action="preview"]').click
        
        # Wait for lightbox to appear
        expect(page).to have_css('#secureLightbox:not(.hidden)', wait: 5)
        
        # Verify PDF iframe loads in lightbox
        within('#secureLightbox') do
          expect(page).to have_css('iframe', wait: 10)
          expect(page).to have_css('.lightbox-download[style*="inline-flex"]')
        end
      end
    end

    context 'lightbox controls' do
      before do
        visit project_path(project)
        find('[data-content-type="image/png"] [data-action="preview"]').click
        expect(page).to have_css('#secureLightbox:not(.hidden)', wait: 5)
      end

      it 'closes lightbox when close button is clicked' do
        within('#secureLightbox') do
          find('.lightbox-close').click
        end
        
        expect(page).to have_css('#secureLightbox.hidden', wait: 2)
      end

      it 'closes lightbox when backdrop is clicked' do
        find('.lightbox-backdrop').click
        expect(page).to have_css('#secureLightbox.hidden', wait: 2)
      end

      it 'closes lightbox when Escape key is pressed' do
        page.send_keys(:escape)
        expect(page).to have_css('#secureLightbox.hidden', wait: 2)
      end
    end

    context 'download functionality' do
      it 'downloads file when download button is clicked in lightbox' do
        visit project_path(project)
        
        # Open lightbox
        find('[data-content-type="image/png"] [data-action="preview"]').click
        expect(page).to have_css('#secureLightbox:not(.hidden)', wait: 5)
        
        # Click download in lightbox
        within('#secureLightbox') do
          find('.lightbox-download').click
        end
        
        # Verify download initiated (hard to test actual download in headless browser)
        # We mainly want to ensure no JavaScript errors occur
        expect(page).not_to have_css('.error')
      end

      it 'downloads file when download button is clicked on project page' do
        visit project_path(project)
        
        # Click download button on file
        find('.downloadButton').click
        
        # Verify download overlay appears briefly
        expect(page).to have_css('#downloadOverlay[style*="flex"]', wait: 2)
        
        # Should disappear when download completes
        expect(page).to have_css('#downloadOverlay[style*="none"]', wait: 10)
      end
    end
  end

  describe 'Error handling and edge cases' do
    context 'when server returns error' do
      it 'shows error message when token request fails' do
        # Simulate server error by using invalid project ID
        visit project_path(99999)
        
        # Should show 404 or redirect, not JavaScript errors
        # This is mainly to ensure the error handling is graceful
      end
    end

    context 'DOM security and cleanup' do
      it 'does not accumulate DOM elements after multiple preview cycles' do
        visit project_path(project)
        
        # Open and close lightbox multiple times
        3.times do
          find('[data-content-type="image/png"] [data-action="preview"]').click
          expect(page).to have_css('#secureLightbox:not(.hidden)', wait: 5)
          
          find('.lightbox-close').click
          expect(page).to have_css('#secureLightbox.hidden', wait: 2)
        end
        
        # Verify only one lightbox exists (no accumulation)
        expect(page).to have_css('#secureLightbox', count: 1)
        
        # Verify no extra images or iframes accumulated
        within('#secureLightbox') do
          expect(page).to have_css('img, iframe', count: 0)
        end
      end

      it 'clears previous content when opening different file types' do
        visit project_path(project)
        
        # First open image
        find('[data-content-type="image/png"] [data-action="preview"]').click
        expect(page).to have_css('#secureLightbox:not(.hidden)', wait: 5)
        expect(page).to have_css('#secureLightbox img', wait: 10)
        
        # Close and open PDF
        find('.lightbox-close').click
        expect(page).to have_css('#secureLightbox.hidden', wait: 2)
        
        find('[data-content-type="application/pdf"] [data-action="preview"]').click
        expect(page).to have_css('#secureLightbox:not(.hidden)', wait: 5)
        
        # Should have iframe, not image
        within('#secureLightbox') do
          expect(page).to have_css('iframe', wait: 10)
          expect(page).not_to have_css('img')
        end
      end
    end
  end

  describe 'Security validation' do
    it 'does not expose file URLs in page source or network requests' do
      visit project_path(project)
      
      # Check that no file IDs or S3 URLs are in the page source
      expect(page.html).not_to match(/\/rails\/active_storage/)
      expect(page.html).not_to match(/amazonaws\.com/)
      expect(page.html).not_to match(/attachment_\d+/)
      
      # File access should only use secure hashes
      expect(page).to have_css('[data-file-hash]')
      
      # Hash should be cryptographic (32 hex chars)
      hash = find('[data-file-hash]')['data-file-hash']
      expect(hash).to match(/\A[a-f0-9]{32}\z/)
    end

    it 'generates unique secure tokens for each request' do
      # This test verifies that tokens are unique by making multiple requests
      # The actual uniqueness is tested in the service spec, but this ensures
      # the frontend integration maintains this property
      
      visit project_path(project)
      
      # Open lightbox twice (should generate 2 different tokens)
      find('[data-content-type="image/png"] [data-action="preview"]').click
      expect(page).to have_css('#secureLightbox:not(.hidden)', wait: 5)
      
      find('.lightbox-close').click
      expect(page).to have_css('#secureLightbox.hidden', wait: 2)
      
      find('[data-content-type="image/png"] [data-action="preview"]').click
      expect(page).to have_css('#secureLightbox:not(.hidden)', wait: 5)
      
      # If we get here without errors, token generation is working
      # Actual uniqueness validation is in the service spec
    end
  end
end