require 'rails_helper'

RSpec.describe "Full Access Integration", type: :feature do
  let(:owner) { create(:user, email: '<EMAIL>', password: 'password123') }
  let(:connected_user) { create(:user, email: '<EMAIL>', password: 'password123') }
  let(:unconnected_user) { create(:user, email: '<EMAIL>', password: 'password123') }
  
  before do
    # Create network connection between owner and connected_user
    create(:network_connection, inviter: owner, invitee: connected_user)
  end

  describe "Project visibility and access in index page" do
    context "with full_access + network_only project" do
      let!(:project) do
        create(:project, 
          user: owner,
          summary: "Full Access Network Only Project",
          full_access: true, 
          network_only: true,
          summary_only: false,
          semi_public: false,
          approved: true
        )
      end

      scenario "connected user sees 'View' button instead of 'Request Access'" do
        sign_in connected_user
        visit projects_path
        
        expect(page).to have_content(project.summary)
        expect(page).to have_link('View', href: project_path(project))
        expect(page).not_to have_link('Request Access')
        expect(page).to have_content('Full Details')
      end

      scenario "unconnected user sees 'Request Access' button" do
        sign_in unconnected_user
        visit projects_path
        
        expect(page).to have_content(project.summary)
        expect(page).to have_link('Request Access')
        expect(page).not_to have_link('View')
      end

      scenario "guest user is redirected to sign in page" do
        visit project_path(project)
        expect(current_path).to eq(new_user_session_path)
      end

      scenario "connected user can actually view project details" do
        sign_in connected_user
        visit projects_path
        click_link 'View'
        
        expect(page).to have_content(project.summary)
        expect(page).to have_content(project.full_description || '')
        expect(current_path).to eq(project_path(project))
      end
    end

    context "with full_access + semi_public project" do
      let!(:project) do
        create(:project, 
          user: owner,
          summary: "Full Access Everyone Project",
          full_access: true, 
          semi_public: true,
          summary_only: false,
          network_only: false,
          approved: true
        )
      end

      scenario "connected user sees 'View' button" do
        sign_in connected_user
        visit projects_path
        
        expect(page).to have_content(project.summary)
        expect(page).to have_link('View', href: project_path(project))
        expect(page).not_to have_link('Request Access')
      end

      scenario "unconnected user also sees 'View' button (public access)" do
        sign_in unconnected_user
        visit projects_path
        
        expect(page).to have_content(project.summary)
        expect(page).to have_link('View', href: project_path(project))
        expect(page).not_to have_link('Request Access')
      end
    end

    context "with summary_only project (traditional behavior)" do
      let!(:project) do
        create(:project, 
          user: owner,
          summary: "Summary Only Project",
          summary_only: true, 
          network_only: true,
          full_access: false,
          semi_public: false,
          approved: true
        )
      end

      scenario "connected user still sees 'Request Access' button" do
        sign_in connected_user
        visit projects_path
        
        expect(page).to have_content(project.summary)
        expect(page).to have_link('Request Access')
        expect(page).not_to have_link('View')
      end

      scenario "unconnected user sees 'Request Access' button" do
        sign_in unconnected_user
        visit projects_path
        
        expect(page).to have_content(project.summary)
        expect(page).to have_link('Request Access')
        expect(page).not_to have_link('View')
      end
    end
  end

  describe "Project detail page access control" do
    context "with full_access + network_only project" do
      let!(:project) do
        create(:project, 
          user: owner,
          summary: "Full Access Network Project",
          full_description: "This is the full description that should be visible",
          full_access: true, 
          network_only: true,
          summary_only: false,
          semi_public: false,
          approved: true
        )
      end

      scenario "connected user can access project page directly" do
        sign_in connected_user
        visit project_path(project)
        
        expect(page).to have_content(project.summary)
        expect(page).to have_content("This is the full description")
        expect(page).not_to have_content("Access Denied")
      end

      scenario "unconnected user is denied access to project page" do
        sign_in unconnected_user
        
        expect {
          visit project_path(project)
        }.to raise_error(ActionPolicy::Unauthorized)
      end
    end

    context "with full_access + semi_public project" do
      let!(:project) do
        create(:project, 
          user: owner,
          summary: "Public Full Access Project",
          full_description: "This should be visible to everyone",
          full_access: true, 
          semi_public: true,
          summary_only: false,
          network_only: false,
          approved: true
        )
      end

      scenario "any authenticated user can access project page" do
        sign_in unconnected_user
        visit project_path(project)
        
        expect(page).to have_content(project.summary)
        expect(page).to have_content("This should be visible to everyone")
        expect(page).not_to have_content("Access Denied")
      end
    end
  end

  describe "Mixed scenarios and edge cases" do
    scenario "user with both connection AND explicit auth gets access" do
      project = create(:project, 
        user: owner,
        summary: "Mixed Auth Project",
        full_access: false,  # full_access disabled
        summary_only: true,  # but user has explicit auth
        network_only: true,
        approved: true
      )
      
      # Create explicit ProjectAuth
      create(:project_auth, project: project, user: connected_user, access_level: 'full_details')
      
      sign_in connected_user
      visit projects_path
      
      expect(page).to have_content(project.summary)
      expect(page).to have_link('View', href: project_path(project))
      expect(page).not_to have_link('Request Access')
    end

    scenario "owner always has access regardless of settings" do
      project = create(:project, 
        user: owner,
        summary: "Owner's Project",
        summary_only: true,  # Even with restrictive settings
        network_only: true,
        full_access: false,
        approved: true
      )
      
      sign_in owner
      visit projects_path
      
      expect(page).to have_content(project.summary)
      expect(page).to have_link('Edit', href: edit_project_path(project))
    end
  end

  private

  def sign_in(user)
    visit new_user_session_path
    fill_in 'Email', with: user.email
    fill_in 'Password', with: user.password
    click_button 'Log in'
  end
end