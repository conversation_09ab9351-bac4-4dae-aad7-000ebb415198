# ABOUTME: Feature tests for UNL-42 deferred project creation user workflow
# ABOUTME: Tests the complete user experience from clicking "New Project" through form interaction and saving

require 'rails_helper'

RSpec.feature "Deferred Project Creation Flow (UNL-42)", type: :feature, js: true do
  let(:user) { create(:user) }
  
  before do
    login_as(user, scope: :user)
  end

  scenario "User visits new project page without creating database records" do
    # Track initial project count
    initial_count = Project.count
    
    # User clicks "New Project"
    visit new_project_path
    
    # Page loads successfully
    expect(page).to have_content("New Project") # Adjust based on actual page content
    expect(page).to have_css("form#project_form")
    
    # No project created in database yet
    expect(Project.count).to eq(initial_count)
    
    # Form fields are present and empty
    expect(page).to have_field("Summary", with: "")
    expect(page).to have_field("Location", with: "")
  end

  scenario "User leaves empty form without creating project" do
    initial_count = Project.count
    
    visit new_project_path
    
    # User navigates away without entering anything
    visit projects_path  # Navigate away
    
    # No project created
    expect(Project.count).to eq(initial_count)
  end

  scenario "User enters content and triggers autosave" do
    visit new_project_path
    initial_count = Project.count
    
    # User starts typing in summary field
    fill_in "Summary", with: "My Real Estate Investment"
    
    # Wait for autosave to trigger (2 second debounce + processing time)
    sleep 3
    
    # Project should now be created
    expect(Project.count).to eq(initial_count + 1)
    
    project = Project.last
    expect(project.summary).to eq("My Real Estate Investment")
    expect(project.user).to eq(user)
    expect(project.project_status).to be_falsey # Draft
  end

  scenario "User adds more content after initial creation" do
    visit new_project_path
    
    # First input triggers creation
    fill_in "Summary", with: "Investment Opportunity"
    sleep 3 # Wait for autosave
    
    project = Project.last
    original_updated_at = project.updated_at
    
    # Add more content
    fill_in "Location", with: "Bratislava, Slovakia"
    sleep 3 # Wait for autosave
    
    # Project should be updated, not duplicated
    expect(Project.count).to eq(1) # Still only one project
    
    project.reload
    expect(project.summary).to eq("Investment Opportunity")
    expect(project.location).to eq("Bratislava, Slovakia")
    expect(project.updated_at).to be > original_updated_at
  end

  scenario "User submits form normally after autosave creation" do
    visit new_project_path
    
    # Trigger autosave first
    fill_in "Summary", with: "Test Project"
    sleep 3
    
    project = Project.last
    expect(project).to be_present
    
    # Now fill out complete form and submit
    fill_in "Location", with: "Test Location"
    select "Real Estate", from: "Project Type" # Adjust selector based on actual form
    
    # Submit form normally
    click_button "Create Project" # Adjust button text based on actual form
    
    # Should redirect to edit page of the same project (not create new one)
    expect(Project.count).to eq(1) # Still only one project
    expect(current_path).to eq(edit_project_path(project))
    
    project.reload
    expect(project.location).to eq("Test Location")
  end

  scenario "Multiple users don't interfere with each other" do
    other_user = create(:user)
    
    # First user starts creating project
    visit new_project_path
    fill_in "Summary", with: "User 1 Project"
    sleep 3
    
    user1_project = Project.last
    expect(user1_project.user).to eq(user)
    
    # Simulate second user in different session
    logout
    login_as(other_user, scope: :user)
    
    visit new_project_path
    fill_in "Summary", with: "User 2 Project"
    sleep 3
    
    user2_project = Project.last
    expect(user2_project.user).to eq(other_user)
    expect(user2_project).not_to eq(user1_project)
    
    # Both projects exist independently
    expect(Project.count).to eq(2)
  end

  scenario "Autosave indicator shows feedback to user", skip: "Requires autosave indicator implementation" do
    visit new_project_path
    
    # Initially no indicator
    expect(page).not_to have_css("#autosave-indicator")
    
    # Start typing
    fill_in "Summary", with: "Test"
    
    # Should show "saving" indicator
    expect(page).to have_css("#autosave-indicator", text: /saving/i)
    
    # Wait for save to complete
    sleep 3
    
    # Should show "saved" indicator or hide
    expect(page).not_to have_css("#autosave-indicator", text: /saving/i)
  end

  scenario "Form behavior with validation errors" do
    visit new_project_path
    
    # Create project with autosave
    fill_in "Summary", with: "Test Project"
    sleep 3
    
    project = Project.last
    
    # Try to publish with incomplete data
    check "Publish" # Adjust based on actual form structure
    click_button "Update Project"
    
    # Should show validation errors but keep project as draft
    expect(page).to have_content("error") # Adjust based on actual error display
    
    project.reload
    expect(project.project_status).to be_falsey # Still draft
  end

  scenario "Browser back button after autosave creation" do
    visit projects_path # Start from projects index
    click_link "New Project" # Navigate to new project
    
    # Create project via autosave
    fill_in "Summary", with: "Back Button Test"
    sleep 3
    
    project = Project.last
    expect(project).to be_present
    
    # Use browser back button
    page.go_back
    
    # Should be back at projects index
    expect(current_path).to eq(projects_path)
    
    # Project should still exist in database
    expect(Project.find(project.id)).to be_present
  end

  # Test edge cases
  scenario "Very slow typing doesn't create multiple projects" do
    visit new_project_path
    
    # Type very slowly, triggering multiple autosave attempts
    fill_in "Summary", with: "S"
    sleep 1
    fill_in "Summary", with: "Sl"
    sleep 1
    fill_in "Summary", with: "Slow"
    sleep 1
    fill_in "Summary", with: "Slow Type"
    sleep 3 # Final autosave
    
    # Should only create one project
    expect(Project.count).to eq(1)
    expect(Project.last.summary).to eq("Slow Type")
  end

  scenario "Rapid typing with debouncing" do
    visit new_project_path
    
    # Type rapidly - should be debounced
    fill_in "Summary", with: "Quick typing test without pausing"
    
    # Wait for debounced save
    sleep 3
    
    # Should create exactly one project
    expect(Project.count).to eq(1)
    expect(Project.last.summary).to eq("Quick typing test without pausing")
  end
end