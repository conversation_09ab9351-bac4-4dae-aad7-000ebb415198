require 'rails_helper'

RSpec.describe Project, type: :model do
  let(:user) { create(:user) }
  let(:project) { create(:project, user: user) }
  let(:other_project) { create(:project, user: user) }
  let(:test_file) { fixture_file_upload('spec/fixtures/files/test.pdf', 'application/pdf') }
  let(:other_file) { fixture_file_upload('spec/fixtures/files/test_image.png', 'image/png') }

  describe "SecureFileAccess concern" do
    describe "#generate_secure_file_hash" do
      context "with valid inputs" do
        before do
          project.files.attach(test_file)
        end

        it "generates a 32-character hexadecimal string minimum" do
          file_attachment = project.files.first
          hash = project.generate_secure_file_hash(file_attachment)
          
          expect(hash).to be_a(String)
          expect(hash.length).to be >= 32
          expect(hash).to match(/\A[a-f0-9]+\z/i)
        end

        it "always produces the same hash for the same file on the same project" do
          file_attachment = project.files.first
          
          hash1 = project.generate_secure_file_hash(file_attachment)
          hash2 = project.generate_secure_file_hash(file_attachment)
          
          expect(hash1).to eq(hash2)
        end

        it "produces different hashes for different files on the same project" do
          project.files.attach(other_file)
          
          file1 = project.files.first
          file2 = project.files.last
          
          hash1 = project.generate_secure_file_hash(file1)
          hash2 = project.generate_secure_file_hash(file2)
          
          expect(hash1).not_to eq(hash2)
        end

        it "produces different hashes for the same file on different projects (project-scoped)" do
          other_project.files.attach(test_file)
          
          file1 = project.files.first
          file2 = other_project.files.first
          
          hash1 = project.generate_secure_file_hash(file1)
          hash2 = other_project.generate_secure_file_hash(file2)
          
          expect(hash1).not_to eq(hash2)
        end

        it "does not use simple concatenation of public IDs (cryptographic hashing)" do
          file_attachment = project.files.first
          hash = project.generate_secure_file_hash(file_attachment)
          
          # Hash should not contain obvious patterns like project.id or file.id
          expect(hash).not_to include(project.id.to_s)
          expect(hash).not_to include(file_attachment.id.to_s)
        end

        it "generates the same hash for re-attached files (based on stable identifiers)" do
          file_attachment = project.files.first
          original_hash = project.generate_secure_file_hash(file_attachment)
          
          # Detach and reattach the same file
          project.files.detach
          project.files.attach(test_file)
          
          new_file_attachment = project.files.first
          new_hash = project.generate_secure_file_hash(new_file_attachment)
          
          expect(new_hash).to eq(original_hash)
        end
      end

      context "with invalid inputs" do
        it "raises ArgumentError when project is not saved (no ID)" do
          unsaved_project = build(:project)
          file_attachment = double('attachment')
          
          expect {
            unsaved_project.generate_secure_file_hash(file_attachment)
          }.to raise_error(ArgumentError, /project must be saved/)
        end

        it "raises ArgumentError when file is not attached" do
          unattached_file = double('attachment', attached?: false)
          
          expect {
            project.generate_secure_file_hash(unattached_file)
          }.to raise_error(ArgumentError, /file must be attached/)
        end

        it "raises ArgumentError when file parameter is nil" do
          expect {
            project.generate_secure_file_hash(nil)
          }.to raise_error(ArgumentError, /file cannot be nil/)
        end
      end

      context "DoS protection and resource limits" do
        it "generates hash without loading entire large file into memory" do
          # This is more of an implementation test, but we can verify
          # that the method completes quickly even for large files
          large_file = fixture_file_upload('spec/fixtures/files/large_test.pdf', 'application/pdf')
          project.files.attach(large_file)
          
          file_attachment = project.files.first
          
          start_time = Time.current
          hash = project.generate_secure_file_hash(file_attachment)
          end_time = Time.current
          
          expect(hash).to be_present
          expect(end_time - start_time).to be < 1.second
        end

        it "handles files with missing metadata gracefully" do
          project.files.attach(test_file)
          file_attachment = project.files.first
          
          # Simulate missing content_type
          allow(file_attachment.blob).to receive(:content_type).and_return(nil)
          
          expect {
            hash = project.generate_secure_file_hash(file_attachment)
            expect(hash).to be_present
          }.not_to raise_error
        end
      end
    end

    describe "#find_file_by_secure_hash" do
      before do
        project.files.attach(test_file)
        project.files.attach(other_file)
      end

      context "with valid hash" do
        it "returns the correct ActiveStorage::Attachment instance" do
          file_attachment = project.files.first
          hash = project.generate_secure_file_hash(file_attachment)
          
          found_file = project.find_file_by_secure_hash(hash)
          
          expect(found_file).to eq(file_attachment)
          expect(found_file).to be_an(ActiveStorage::Attachment)
        end
      end

      context "with invalid hash" do
        it "returns nil for hash that doesn't correspond to any file" do
          invalid_hash = "nonexistent" + "a" * 32
          
          result = project.find_file_by_secure_hash(invalid_hash)
          
          expect(result).to be_nil
        end

        it "returns nil for hash that corresponds to file on different project (project isolation)" do
          other_project.files.attach(test_file)
          other_file_attachment = other_project.files.first
          other_hash = other_project.generate_secure_file_hash(other_file_attachment)
          
          result = project.find_file_by_secure_hash(other_hash)
          
          expect(result).to be_nil
        end

        it "returns nil for empty string hash" do
          result = project.find_file_by_secure_hash("")
          
          expect(result).to be_nil
        end

        it "returns nil for nil hash" do
          result = project.find_file_by_secure_hash(nil)
          
          expect(result).to be_nil
        end

        it "returns nil for malformed hash (wrong length/format)" do
          malformed_hashes = [
            "short",
            "toolongofhashstringthatexceedsnormallength" * 3,
            "contains-invalid-chars!@#$%^&*()",
            " " * 32  # whitespace
          ]
          
          malformed_hashes.each do |bad_hash|
            result = project.find_file_by_secure_hash(bad_hash)
            expect(result).to be_nil, "Expected nil for hash: #{bad_hash}"
          end
        end
      end
    end

    describe "hash consistency and security" do
      it "generates different hashes for the same file on two different projects" do
        other_project.files.attach(test_file)
        
        file1 = project.files.first
        file2 = other_project.files.first
        
        hash1 = project.generate_secure_file_hash(file1)
        hash2 = other_project.generate_secure_file_hash(file2)
        
        expect(hash1).not_to eq(hash2)
      end

      it "always returns the same hash for multiple calls on the same file (deterministic)" do
        file_attachment = project.files.first
        
        hashes = 5.times.map { project.generate_secure_file_hash(file_attachment) }
        
        expect(hashes.uniq.length).to eq(1)
      end
    end
  end

  describe "Full Access functionality" do
    let(:owner) { create(:user) }
    let(:connected_user) { create(:user) }
    let(:unconnected_user) { create(:user) }
    
    before do
      # Create network connection between owner and connected_user
      create(:network_connection, inviter: owner, invitee: connected_user)
    end

    describe "#user_has_access?" do
      context "when user is project owner" do
        let(:project) { create(:project, user: owner) }

        it "returns true regardless of sharing settings" do
          expect(project.user_has_access?(owner)).to be true
        end
      end

      context "when user has explicit ProjectAuth" do
        let(:project) { create(:project, user: owner) }
        
        before do
          create(:project_auth, project: project, user: connected_user, access_level: 'full_details')
        end

        it "returns true" do
          expect(project.user_has_access?(connected_user)).to be true
        end
      end

      context "with full_access + network_only settings" do
        let(:project) do
          create(:project, 
            user: owner, 
            full_access: true, 
            network_only: true,
            summary_only: false,
            semi_public: false
          )
        end

        it "allows access for connected user" do
          expect(project.user_has_access?(connected_user)).to be true
        end

        it "denies access for unconnected user" do
          expect(project.user_has_access?(unconnected_user)).to be false
        end
      end

      context "with full_access + semi_public settings" do
        let(:project) do
          create(:project, 
            user: owner, 
            full_access: true, 
            semi_public: true,
            summary_only: false,
            network_only: false
          )
        end

        it "allows access for connected user" do
          expect(project.user_has_access?(connected_user)).to be true
        end

        it "allows access for unconnected user" do
          expect(project.user_has_access?(unconnected_user)).to be true
        end
      end

      context "with summary_only settings" do
        let(:project) do
          create(:project, 
            user: owner, 
            summary_only: true, 
            network_only: true,
            full_access: false,
            semi_public: false
          )
        end

        it "denies access for connected user" do
          expect(project.user_has_access?(connected_user)).to be false
        end

        it "denies access for unconnected user" do
          expect(project.user_has_access?(unconnected_user)).to be false
        end
      end
    end

    describe "#user_connected_to_owner?" do
      let(:project) { create(:project, user: owner) }

      it "returns true when user is connected to owner" do
        expect(project.send(:user_connected_to_owner?, connected_user)).to be true
      end

      it "returns false when user is not connected to owner" do
        expect(project.send(:user_connected_to_owner?, unconnected_user)).to be false
      end

      it "handles bidirectional connections" do
        # Create reverse connection
        user = create(:user)
        create(:network_connection, inviter: user, invitee: owner)
        
        expect(project.send(:user_connected_to_owner?, user)).to be true
      end
    end

    describe "sharing validation" do
      describe "#validate_sharing_consistency" do
        it "allows exactly one visibility option" do
          project = build(:project, network_only: true, semi_public: false)
          expect(project).to be_valid
          
          project = build(:project, network_only: false, semi_public: true)
          expect(project).to be_valid
        end

        it "rejects both visibility options being true" do
          project = build(:project, network_only: true, semi_public: true)
          expect(project).not_to be_valid
          expect(project.errors[:base]).to include("Choose either 'My Network' or 'Everyone' for visibility")
        end

        it "rejects both visibility options being false" do
          project = build(:project, network_only: false, semi_public: false)
          expect(project).not_to be_valid
          expect(project.errors[:base]).to include("Choose either 'My Network' or 'Everyone' for visibility")
        end

        it "allows exactly one detail level option" do
          project = build(:project, summary_only: true, full_access: false)
          expect(project).to be_valid
          
          project = build(:project, summary_only: false, full_access: true)
          expect(project).to be_valid
        end

        it "rejects both detail level options being true" do
          project = build(:project, summary_only: true, full_access: true)
          expect(project).not_to be_valid
          expect(project.errors[:base]).to include("Choose either 'Title Only' or 'Everything' for sharing level")
        end

        it "rejects both detail level options being false" do
          project = build(:project, summary_only: false, full_access: false)
          expect(project).not_to be_valid
          expect(project.errors[:base]).to include("Choose either 'Title Only' or 'Everything' for sharing level")
        end
      end
    end

    describe "unapproved projects (approved: false)" do
      let(:project) do
        create(:project, 
          user: owner,
          full_access: true, 
          semi_public: true, # Most permissive settings
          summary_only: false,
          network_only: false,
          approved: false    # But not approved
        )
      end

      it "allows access for owner" do
        expect(project.user_has_access?(owner)).to be true
      end

      it "denies access for connected user" do
        expect(project.user_has_access?(connected_user)).to be false
      end

      it "denies access for unconnected user" do
        expect(project.user_has_access?(unconnected_user)).to be false
      end

      it "denies access even with explicit ProjectAuth" do
        create(:project_auth, project: project, user: connected_user, access_level: 'full_details')
        expect(project.user_has_access?(connected_user)).to be false
      end
    end

    describe "edge cases" do
      let(:project) { create(:project, user: owner) }

      it "handles nil user gracefully by returning false" do
        expect(project.user_has_access?(nil)).to be false
      end

      it "prioritizes explicit auth over automatic access" do
        project.update!(full_access: false, summary_only: true, network_only: true)
        create(:project_auth, project: project, user: connected_user, access_level: 'full_details')

        expect(project.user_has_access?(connected_user)).to be true
      end
    end
  end

  describe "Status functionality" do
    let(:user) { create(:user) }

    describe "#status" do
      it "returns 'draft' when project_status is false" do
        project = create(:project, user: user, project_status: false, approved: false)
        expect(project.status).to eq('draft')
      end

      it "returns 'pending' when project_status is true but not approved" do
        project = create(:project, user: user, project_status: true, approved: false)
        expect(project.status).to eq('pending')
      end

      it "returns 'published' when project_status is true and approved" do
        project = create(:project, user: user, project_status: true, approved: true)
        expect(project.status).to eq('published')
      end
    end

    describe "#status_label" do
      it "returns human-readable labels for each status" do
        draft_project = create(:project, user: user, project_status: false, approved: false)
        pending_project = create(:project, user: user, project_status: true, approved: false)
        published_project = create(:project, user: user, project_status: true, approved: true)

        expect(draft_project.status_label).to eq('Draft')
        expect(pending_project.status_label).to eq('Pending')
        expect(published_project.status_label).to eq('Published')
      end
    end

    describe "status scopes" do
      let!(:draft_project) { create(:project, user: user, project_status: false, approved: false) }
      let!(:pending_project) { create(:project, user: user, project_status: true, approved: false) }
      let!(:published_project) { create(:project, user: user, project_status: true, approved: true) }

      describe ".drafts" do
        it "returns only draft projects" do
          expect(Project.drafts).to contain_exactly(draft_project)
        end
      end

      describe ".pending_approval" do
        it "returns only pending projects" do
          expect(Project.pending_approval).to contain_exactly(pending_project)
        end
      end

      describe ".published" do
        it "returns only published projects" do
          expect(Project.published).to contain_exactly(published_project)
        end
      end
    end
  end
end
