require 'rails_helper'

RSpec.describe User, type: :model do
  describe "subscription tiers" do
    it "defaults to free tier" do
      user = User.new
      expect(user.tier_free?).to be true
    end
    
    it "can be assigned to premium tier" do
      user = create(:user, subscription_tier: :premium)
      expect(user.tier_premium?).to be true
    end
    
    it "can be assigned to beta tier" do
      user = create(:user, subscription_tier: :beta)
      expect(user.tier_beta?).to be true
    end
    
    it "correctly identifies active subscription for premium users" do
      user = create(:user, subscription_tier: :premium, subscription_expires_at: 1.month.from_now)
      expect(user.active_subscription?).to be true
    end
    
    it "correctly identifies inactive subscription for expired premium users" do
      user = create(:user, subscription_tier: :premium, subscription_expires_at: 1.day.ago)
      expect(user.active_subscription?).to be false
    end
    
    it "beta users never expire" do
      user = create(:user, subscription_tier: :beta)
      expect(user.active_subscription?).to be true
    end
    
    it "beta users have active subscription without expiration date" do
      user = create(:user, subscription_tier: :beta)
      expect(user.active_subscription?).to be true
    end
    
    it "free users do not have active subscription" do
      user = create(:user, subscription_tier: :free)
      expect(user.active_subscription?).to be false
    end
    
    it "correctly identifies subscription expiration for premium users" do
      expired_user = create(:user, subscription_tier: :premium, subscription_expires_at: 1.day.ago)
      active_user = create(:user, subscription_tier: :premium, subscription_expires_at: 1.month.from_now)
      
      expect(expired_user.subscription_expired?).to be true
      expect(active_user.subscription_expired?).to be false
    end
    
    it "can generate unique referral codes" do
      user = create(:user)
      user.generate_referral_code!
      
      expect(user.referral_code).to be_present
      expect(user.referral_code.length).to eq(8)
      expect(user.referral_code).to match(/^[A-Z0-9]+$/)
    end
    
    it "generates unique referral codes for different users" do
      user1 = create(:user)
      user2 = create(:user)
      
      user1.generate_referral_code!
      user2.generate_referral_code!
      
      expect(user1.referral_code).not_to eq(user2.referral_code)
    end
  end
end
