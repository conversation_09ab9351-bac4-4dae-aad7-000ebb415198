require 'rails_helper'

RSpec.describe Upload, type: :model do
  let(:user) { create(:user) }
  
  describe 'validations' do
    let(:upload) { build(:upload, user: user) }
    
    it 'is valid with valid attributes' do
      expect(upload).to be_valid
    end
    
    it 'requires user presence' do
      upload.user = nil
      expect(upload).not_to be_valid
      expect(upload.errors[:user]).to be_present
    end
    
    it 'requires original_filename presence' do
      upload.original_filename = nil
      expect(upload).not_to be_valid
      expect(upload.errors[:original_filename]).to be_present
    end
    
    it 'requires content_type presence' do
      upload.content_type = nil
      expect(upload).not_to be_valid
      expect(upload.errors[:content_type]).to be_present
    end
    
    it 'requires file_size presence and positive value' do
      upload.file_size = nil
      expect(upload).not_to be_valid
      expect(upload.errors[:file_size]).to be_present
      
      upload.file_size = 0
      expect(upload).not_to be_valid
      expect(upload.errors[:file_size]).to be_present
    end
    
    it 'validates progress_percentage range' do
      upload.progress_percentage = -1
      expect(upload).not_to be_valid
      expect(upload.errors[:progress_percentage]).to be_present
      
      upload.progress_percentage = 101
      expect(upload).not_to be_valid
      expect(upload.errors[:progress_percentage]).to be_present
    end
    
    it 'validates signed_id_token uniqueness' do
      existing_upload = create(:upload, user: user)
      upload.signed_id_token = existing_upload.signed_id_token
      expect(upload).not_to be_valid
      expect(upload.errors[:signed_id_token]).to be_present
    end
  end
  
  describe 'associations' do
    let(:upload) { create(:upload, user: user) }
    
    it 'belongs to user' do
      expect(upload.user).to eq(user)
    end
    
    it 'can belong to a target polymorphically' do
      project = create(:project, user: user)
      upload.target = project
      upload.save!
      expect(upload.target).to eq(project)
      expect(upload.target_type).to eq('Project')
      expect(upload.target_id).to eq(project.id)
    end
  end
  
  describe 'enums' do
    it 'defines status enum with enhanced state machine states' do
      expect(Upload.statuses.keys).to match_array(%w[pending transferred processing completed failed cancelled])
    end
  end
  
  describe 'Enhanced state machine transitions' do
    let(:upload) { create(:upload, user: user, status: :pending) }
    
    describe '#mark_transferred!' do
      it 'transitions from pending to transferred' do
        expect { upload.mark_transferred! }.to change { upload.status }.from('pending').to('transferred')
      end
      
      it 'raises error if not in pending state' do
        upload.update!(status: :processing)
        expect { upload.mark_transferred! }.to raise_error(Upload::InvalidStateTransition)
      end
    end
    
    describe '#can_process?' do
      it 'returns true when status is transferred' do
        upload.update!(status: :transferred)
        expect(upload.can_process?).to be true
      end
      
      it 'returns false when status is not transferred' do
        expect(upload.can_process?).to be false
      end
    end
    
    describe '#mark_processing!' do
      it 'transitions from transferred to processing' do
        upload.update!(status: :transferred)
        expect { upload.mark_processing! }.to change { upload.status }.from('transferred').to('processing')
      end
      
      it 'resets progress to 0%' do
        upload.update!(status: :transferred, progress_percentage: 50)
        upload.mark_processing!
        expect(upload.progress_percentage).to eq(0)
      end
      
      it 'raises error if not in transferred state' do
        expect { upload.mark_processing! }.to raise_error(Upload::InvalidStateTransition)
      end
    end
    
    describe '#mark_completed!' do
      it 'transitions from processing to completed' do
        upload.update!(status: :processing)
        expect { upload.mark_completed! }.to change { upload.status }.from('processing').to('completed')
      end
      
      it 'sets progress to 100%' do
        upload.update!(status: :processing, progress_percentage: 50)
        upload.mark_completed!
        expect(upload.progress_percentage).to eq(100)
      end
      
      it 'raises error if not in processing state' do
        upload.update!(status: :transferred)
        expect { upload.mark_completed! }.to raise_error(Upload::InvalidStateTransition)
      end
    end
    
    describe '#mark_failed!' do
      it 'can transition from any active state to failed' do
        %w[pending transferred processing].each do |status|
          upload.update!(status: status)
          expect { upload.mark_failed!('Test error') }.to change { upload.status }.to('failed')
          expect(upload.error_message).to eq('Test error')
        end
      end
    end
  end
  
  describe 'signed ID generation' do
    let(:upload) { create(:upload, user: user) }
    
    it 'generates unique signed_id_token on creation' do
      expect(upload.signed_id_token).to be_present
      expect(upload.signed_id_token.length).to be > 20
    end
    
    it 'generates different tokens for different uploads' do
      upload2 = create(:upload, user: user)
      expect(upload.signed_id_token).not_to eq(upload2.signed_id_token)
    end
    
    describe '#generate_secure_signed_id' do
      it 'returns a Rails signed_id for Action Cable' do
        signed_id = upload.generate_secure_signed_id
        expect(signed_id).to be_present
        expect(Upload.find_signed(signed_id, purpose: :upload_progress)).to eq(upload)
      end
    end
  end
  
  describe 'progress tracking' do
    let(:upload) { create(:upload, user: user, progress_percentage: 45) }
    
    describe '#update_progress!' do
      it 'updates progress percentage and broadcasts' do
        expect(upload).to receive(:broadcast_status)
        upload.update_progress!(75)
        expect(upload.progress_percentage).to eq(75)
      end
      
      it 'validates progress range' do
        expect { upload.update_progress!(150) }.to raise_error(ActiveRecord::RecordInvalid)
        expect { upload.update_progress!(-5) }.to raise_error(ActiveRecord::RecordInvalid)
      end
    end
    
    describe '#calculate_progress' do
      it 'returns current progress percentage' do
        expect(upload.calculate_progress).to eq(45)
      end
    end
  end
  
  describe 'file path management' do
    let(:upload) { create(:upload, user: user) }
    
    describe '#temp_file_exists?' do
      it 'returns false when temp_file_path is nil' do
        expect(upload.temp_file_exists?).to be false
      end
      
      it 'returns true when temp file exists on disk' do
        # Create a temporary file for testing
        temp_file = Tempfile.new('test_upload')
        upload.update!(temp_file_path: temp_file.path)
        
        expect(upload.temp_file_exists?).to be true
        
        temp_file.close
        temp_file.unlink
      end
    end
    
    describe '#cleanup_temp_file!' do
      it 'removes temp file from disk if it exists' do
        temp_file = Tempfile.new('test_upload')
        temp_path = temp_file.path
        upload.update!(temp_file_path: temp_path)
        temp_file.close
        
        expect(File.exist?(temp_path)).to be true
        upload.cleanup_temp_file!
        expect(File.exist?(temp_path)).to be false
        expect(upload.reload.temp_file_path).to be_nil
      end
      
      it 'handles missing temp files gracefully' do
        upload.update!(temp_file_path: '/nonexistent/path')
        expect { upload.cleanup_temp_file! }.not_to raise_error
      end
    end
  end
  
  describe 'Action Cable broadcasting' do
    let(:upload) { create(:upload, user: user) }
    
    describe '#broadcast_status' do
      it 'broadcasts upload status via Action Cable' do
        expect(UploadChannel).to receive(:broadcast_to).with(
          upload,
          hash_including(
            status: upload.status,
            progress: upload.progress_percentage,
            id: upload.id
          )
        )
        
        upload.broadcast_status
      end
      
      it 'includes error message when failed' do
        upload.update!(status: :failed, error_message: 'Upload failed')
        
        expect(UploadChannel).to receive(:broadcast_to).with(
          upload,
          hash_including(
            status: 'failed',
            error_message: 'Upload failed'
          )
        )
        
        upload.broadcast_status
      end
    end
  end
  
  describe 'polymorphic target association' do
    let(:project) { create(:project, user: user) }
    let(:upload) { create(:upload, user: user, target: project) }
    
    it 'can associate with any model via polymorphic association' do
      expect(upload.target).to eq(project)
      expect(upload.target_type).to eq('Project')
      expect(upload.target_id).to eq(project.id)
    end
    
    it 'can exist without a target for standalone uploads' do
      standalone_upload = create(:upload, user: user, target: nil)
      expect(standalone_upload.target).to be_nil
      expect(standalone_upload).to be_valid
    end
  end
  
  describe 'scopes' do
    let!(:pending_upload) { create(:upload, user: user, status: :pending) }
    let!(:transferred_upload) { create(:upload, user: user, status: :transferred) }
    let!(:processing_upload) { create(:upload, user: user, status: :processing) }
    let!(:completed_upload) { create(:upload, user: user, status: :completed) }
    
    it 'defines scope for processable uploads' do
      expect(Upload.processable).to contain_exactly(transferred_upload)
    end
    
    it 'defines scope for active uploads' do
      expect(Upload.active).to contain_exactly(pending_upload, transferred_upload, processing_upload)
    end
    
    it 'defines scope for finished uploads' do
      expect(Upload.finished).to contain_exactly(completed_upload)
    end
  end

  describe '#can_cancel?' do
    let(:upload) { create(:upload, user: user) }

    context 'when upload is pending' do
      it 'allows cancellation' do
        upload.update!(status: :pending)
        expect(upload.can_cancel?).to be true
      end
    end

    context 'when upload is transferred' do
      it 'allows cancellation' do
        upload.update!(status: :transferred)
        expect(upload.can_cancel?).to be true
      end
    end

    context 'when upload is processing (S3 upload in progress)' do
      it 'does not allow cancellation' do
        upload.update!(status: :processing)
        expect(upload.can_cancel?).to be false
      end
    end

    context 'when upload is completed' do
      it 'does not allow cancellation' do
        upload.update!(status: :completed)
        expect(upload.can_cancel?).to be false
      end
    end

    context 'when upload has failed' do
      it 'does not allow cancellation' do
        upload.update!(status: :failed)
        expect(upload.can_cancel?).to be false
      end
    end

    context 'when upload is already cancelled' do
      it 'does not allow cancellation' do
        upload.update!(status: :cancelled)
        expect(upload.can_cancel?).to be false
      end
    end
  end

  describe '#mark_cancelled!' do
    let(:upload) { create(:upload, user: user, status: :pending) }

    it 'transitions to cancelled status' do
      expect { upload.mark_cancelled! }.to change { upload.status }.to('cancelled')
    end

    it 'does not cleanup temp file (job handles cleanup)' do
      # Create a temp file and set the path
      temp_file = Tempfile.new('test_upload')
      temp_path = temp_file.path
      upload.update!(temp_file_path: temp_path)
      temp_file.close

      # Verify file exists before cancellation
      expect(File.exist?(temp_path)).to be true

      # Mark as cancelled
      upload.mark_cancelled!

      # File should still exist (not cleaned up by controller)
      expect(File.exist?(temp_path)).to be true
      expect(upload.temp_file_path).to eq(temp_path)

      # Clean up test file
      File.delete(temp_path)
    end

    it 'raises error when trying to cancel completed upload' do
      upload.update!(status: :completed)
      expect { upload.mark_cancelled! }.to raise_error(Upload::InvalidStateTransition, "Cannot cancel from completed state")
    end

    context 'when upload is in processing state' do
      it 'raises error (consistent with can_cancel? logic)' do
        upload.update!(status: :processing)
        expect { upload.mark_cancelled! }.to raise_error(Upload::InvalidStateTransition, "Cannot cancel from processing state")
      end
    end
  end
end