# ABOUTME: Tests for Want model that follows exact same patterns as Project model
# ABOUTME: Tests enums, validations, associations, scopes and translation methods

require 'rails_helper'

RSpec.describe Want, type: :model do
  let(:user) { create(:user) }
  
  describe 'associations' do
    it 'belongs to a user' do
      want = build(:want, user: nil)
      expect(want).not_to be_valid
      expect(want.errors[:user]).to include("musí existovať")
    end
  end
  
  describe 'enums' do
    it 'defines want_type enum using Project.project_types' do
      expect(Want.want_types).to eq(Project.project_types)
    end
    
    it 'defines category enum using Project.categories' do
      expect(Want.categories).to eq(Project.categories)  
    end
    
    it 'defines subcategory enum using Project.subcategories' do
      expect(Want.subcategories).to eq(Project.subcategories)
    end
  end
  
  describe 'validations' do
    context 'basic validations' do
      it 'validates presence of summary' do
        want = build(:want, summary: nil)
        expect(want).not_to be_valid
        expect(want.errors[:summary]).to include("<PERSON><PERSON> poptávky nemôže byť prázdny")
      end
      
      it 'allows description to be present' do
        want = build(:want, description: "Detailed description of what I'm looking for")
        expect(want).to be_valid
      end
      
      it 'allows description to be blank' do
        want = build(:want, description: nil)
        expect(want).to be_valid
        
        want = build(:want, description: "")
        expect(want).to be_valid
      end
      
      it 'validates summary length between 10 and 500 characters' do
        want = build(:want, summary: 'too short')
        expect(want).not_to be_valid
        expect(want.errors[:summary]).to include("je príliš krátky/a (min. 10 znakov)")
        
        want = build(:want, summary: 'a' * 501)
        expect(want).not_to be_valid
        expect(want.errors[:summary]).to include("je príliš dlhá/ý (max. 500 znakov)")
      end
      
      it 'validates presence of want_type' do
        want = build(:want, want_type: nil)
        expect(want).not_to be_valid
        expect(want.errors[:want_type]).to include("Typ poptávky musí byť vybraný")
      end
      
      it 'validates presence of category' do
        want = build(:want, category: nil)
        expect(want).not_to be_valid
        expect(want.errors[:category]).to include("Kategória musí byť vybraná")
      end
      
      it 'validates presence of subcategory' do
        want = build(:want, subcategory: nil)
        expect(want).not_to be_valid
        expect(want.errors[:subcategory]).to include("je povinná položka")
      end
    end
    
    context 'price validations' do
      it 'validates price_min is greater than 0 when present' do
        want = build(:want, price_min: -100)
        expect(want).not_to be_valid
        expect(want.errors[:price_min]).to include("musí byť väčšie ako 0")
        
        want = build(:want, price_min: nil)
        expect(want).to be_valid
      end
      
      it 'validates price_max is greater than 0 when present' do
        want = build(:want, price_max: -100)
        expect(want).not_to be_valid
        expect(want.errors[:price_max]).to include("musí byť väčšie ako 0")
        
        want = build(:want, price_max: nil)
        expect(want).to be_valid
      end
      
      it 'validates price_currency is EUR or USD when present' do
        want = build(:want, price_currency: 'EUR')
        expect(want).to be_valid
        
        want = build(:want, price_currency: 'USD')
        expect(want).to be_valid
        
        want = build(:want, price_currency: '')
        expect(want).to be_valid
        
        want = build(:want, price_currency: 'GBP')
        expect(want).not_to be_valid
        expect(want.errors[:price_currency]).to be_present
      end
    end
  end
  
  describe 'scopes' do
    let!(:old_want) { create(:want, created_at: 45.days.ago) }
    let!(:recent_want) { create(:want, created_at: 15.days.ago) }
    let!(:real_estate_want) { create(:want, want_type: :real_estate) }
    let!(:business_want) { create(:want, want_type: :business) }
    
    describe '.active' do
      it 'returns wants created within last 30 days' do
        expect(Want.active).to include(recent_want)
        expect(Want.active).not_to include(old_want)
      end
    end
    
    describe '.by_type' do
      it 'filters wants by type when type is present' do
        expect(Want.by_type('real_estate')).to include(real_estate_want)
        expect(Want.by_type('real_estate')).not_to include(business_want)
      end
      
      it 'returns all wants when type is blank' do
        expect(Want.by_type('')).to include(real_estate_want, business_want)
      end
    end
  end
  
  describe 'translation methods' do
    let(:want) { build(:want, want_type: :real_estate, category: :commercial_property, subcategory: :warehouse) }
    
    describe '#translated_want_type' do
      it 'returns translated want type using project translations' do
        expect(want.translated_want_type).to eq(I18n.t("models.project.project_types.real_estate"))
      end
    end
    
    describe '#translated_category' do
      it 'returns translated category using project translations' do
        expect(want.translated_category).to eq(I18n.t("models.project.categories.commercial_property"))
      end
    end
    
    describe '#translated_subcategory' do  
      it 'returns translated subcategory using project translations' do
        expect(want.translated_subcategory).to eq(I18n.t("models.project.subcategories.warehouse"))
      end
    end
  end
  
  describe 'class methods' do
    describe '.translated_want_types' do
      it 'returns array of translated want types' do
        result = Want.translated_want_types
        expect(result).to be_an(Array)
        expect(result.first).to be_an(Array) # [translation, key] format
        expect(result.first.first).to be_a(String) # translation
        expect(result.first.last).to be_a(String) # key
      end
    end
    
    describe '.translated_categories_for' do
      it 'returns translated categories for given want type' do
        result = Want.translated_categories_for('real_estate')
        expect(result).to be_an(Array)
        expect(result).not_to be_empty
      end
      
      it 'returns empty array for invalid want type' do
        expect(Want.translated_categories_for('invalid')).to eq([])
      end
      
      it 'returns empty array for blank want type' do
        expect(Want.translated_categories_for('')).to eq([])
      end
    end
  end
end