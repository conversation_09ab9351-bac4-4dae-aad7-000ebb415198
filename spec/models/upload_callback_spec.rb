require 'rails_helper'

RSpec.describe Upload, type: :model do
  describe 'automatic job enqueuing callback' do
    let(:user) { create(:user) }
    let(:project) { create(:project, user: user) }
    
    before do
      # Clear any existing jobs
      GoodJob::Job.delete_all
    end

    context 'when upload status changes to ready' do
      it 'enqueues FileUploadJob automatically' do
        # Create upload in pending status
        upload = Upload.create!(
          user: user,
          target: project,
          original_filename: 'test.png',
          content_type: 'image/png',
          file_size: 1000,
          status: :pending,
          progress_percentage: 0
        )

        expect(GoodJob::Job.count).to eq(0)

        # Update status to ready - this should trigger the callback
        upload.update!(
          status: :ready,
          temp_file_path: '/tmp/test.png'
        )

        # Check that job was enqueued
        expect(GoodJob::Job.count).to eq(1)
        job = GoodJob::Job.last
        expect(job.job_class).to eq('FileUploadJob')
        expect(job.serialized_params['arguments']).to eq([upload.id])
      end
    end

    context 'when upload is created directly as ready' do
      it 'enqueues job because record was created with ready status' do
        # Create upload directly as ready
        upload = Upload.create!(
          user: user,
          target: project,
          original_filename: 'test.png',
          content_type: 'image/png',
          file_size: 1000,
          status: :ready,  # Created directly as ready
          temp_file_path: '/tmp/test.png',
          progress_percentage: 0
        )

        # Job should be enqueued because record was created with ready status
        expect(GoodJob::Job.count).to eq(1)
        job = GoodJob::Job.last
        expect(job.job_class).to eq('FileUploadJob')
        expect(job.serialized_params['arguments']).to eq([upload.id])
      end
    end

    context 'when upload status changes to something other than ready' do
      it 'does not enqueue job' do
        upload = Upload.create!(
          user: user,
          target: project,
          original_filename: 'test.png',
          content_type: 'image/png',
          file_size: 1000,
          status: :pending,
          progress_percentage: 0
        )

        # Update to failed status
        upload.update!(status: :failed)

        # No job should be enqueued
        expect(GoodJob::Job.count).to eq(0)
      end
    end
  end
end