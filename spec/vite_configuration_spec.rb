require 'rails_helper'
require 'json'
require 'net/http'

RSpec.describe 'Vite Configuration', type: :integration do
  let(:vite_json_path) { Rails.root.join('config', 'vite.json') }
  let(:vite_config_ts_path) { Rails.root.join('vite.config.ts') }
  
  # Ensure files exist before running tests
  before do
    unless File.exist?(vite_json_path)
      fail "Configuration file not found: #{vite_json_path}"
    end
    unless File.exist?(vite_config_ts_path)
      fail "Configuration file not found: #{vite_config_ts_path}"
    end
  end
  
  describe 'Expected Development Behavior' do
    context 'when Vite is properly configured' do
      it 'should have a configuration that prevents 0.0.0.0 client connections' do
        config = JSON.parse(File.read(vite_json_path))
        dev_config = config['development']
        
        if dev_config['host'] == '0.0.0.0'
          # The real test is that the generated URL is not 0.0.0.0
          # This check is an approximation
          has_server_block = File.read(vite_config_ts_path).match?(/server\s*:\s*{/)
          expect(dev_config['hmr_host'] || !has_server_block).to be_truthy,
            "When vite.json host is 0.0.0.0, a server block in vite.config.ts is dangerous. Use hmr_host in vite.json instead."
        end
      end
      
      # PRIMARY TEST - Tests actual output, not implementation
      it 'should generate correct client URLs in development' do
        # Skip if not in development environment (test environment has different config)
        skip "Only runs in development environment" unless Rails.env.development?
        
        # Test actual helper output
        generated_tags = []
        
        # vite_client_tag generates the HMR client script
        client_tag = helper.vite_client_tag
        generated_tags << client_tag if client_tag
        
        # vite_javascript_tag generates the app entry point
        js_tag = helper.vite_javascript_tag('application')
        generated_tags << js_tag if js_tag
        
        # vite_asset_path generates asset URLs
        asset_path = helper.vite_asset_path('images/logo.svg')
        generated_tags << asset_path if asset_path
        
        # Verify none contain 0.0.0.0
        generated_tags.each do |tag|
          expect(tag).not_to include('0.0.0.0'),
            "Generated output should not contain 0.0.0.0: #{tag}"
          
          # When host is 0.0.0.0, output should use localhost
          if JSON.parse(File.read(vite_json_path)).dig('development', 'host') == '0.0.0.0'
            expect(tag).to include('localhost'),
              "When host is 0.0.0.0, output should use localhost: #{tag}"
          end
        end
      end
    end
    
    context 'HMR WebSocket connections' do
      it 'should not attempt to connect to ws://0.0.0.0' do
        vite_config = File.read(vite_config_ts_path)
        
        if vite_config.match(/hmr\s*:.*?host\s*:\s*['"]0\.0\.0\.0['"]/m)
          fail "HMR is explicitly configured to use 0.0.0.0 - this will cause WebSocket connection failures"
        end
      end
    end
  end
  
  describe 'Production Behavior' do
    it 'should not require Vite server in production' do
      # In test environment, dev server should not be running
      expect(ViteRuby.instance.dev_server_running?).to be(false)
      
      # In test environment, assets are served from vite-test directory
      if File.exist?(Rails.root.join('public/vite-test/.vite/manifest.json'))
        tag = helper.vite_javascript_tag('application')
        # Test environment uses vite-test directory
        expect(tag).to include('/vite-test/assets/')
        expect(tag).not_to include(':3037') # No port number
        expect(tag).to match(/application-[a-zA-Z0-9]+\.js/) # Fingerprinted
      else
        skip "No manifest file in test environment"
      end
    end
  end
  
  describe 'Environment Variable Interference' do
    it 'should not have VITE_RUBY_HOST set to 0.0.0.0' do
      expect(ENV['VITE_RUBY_HOST']).not_to eq('0.0.0.0'),
        "VITE_RUBY_HOST environment variable should not be set to 0.0.0.0"
    end
    
    it 'should not have VITE_RUBY_MODE set to production in development' do
      if Rails.env.development?
        expect(ENV['VITE_RUBY_MODE']).not_to eq('production'),
          "VITE_RUBY_MODE should not be 'production' during development tests"
      end
    end
    
    it 'should not have conflicting HOST or PORT variables' do
      %w[VITE_HOST VITE_PORT HOST PORT].each do |var|
        if ENV[var]
          expect(ENV[var]).not_to match(/^0\.0\.0\.0/),
            "#{var} environment variable might interfere with Vite configuration"
        end
      end
    end
  end
  
  describe 'Version Compatibility' do
    it 'should use a supported vite-plugin-ruby version' do
      package_json = JSON.parse(File.read('package.json'))
      version = package_json.dig('devDependencies', 'vite-plugin-ruby') ||
                package_json.dig('dependencies', 'vite-plugin-ruby')
      
      expect(version).not_to be_nil, "vite-plugin-ruby not found in package.json"
      
      # Extract version number (handle ^, ~, etc.)
      version_number = version.match(/\d+\.\d+\.\d+/)
      expect(version_number).not_to be_nil, "Invalid version format: #{version}"
      
      # Check major version is 4 or 5 (known good versions)
      major_version = version_number[0].split('.').first.to_i
      expect([4, 5]).to include(major_version),
        "Using untested vite-plugin-ruby major version: #{major_version}"
    end
  end
  
  describe 'CI Environment Compatibility' do
    context 'when running in CI', skip: !ENV['CI'] do
      it 'should work without display' do
        expect(ENV['DISPLAY']).to be_nil,
          "CI environment should be headless"
      end
      
      it 'should have required ports available' do
        port = JSON.parse(File.read(vite_json_path)).dig('development', 'port') || 3036
        
        # Try to bind to the port
        begin
          server = TCPServer.new('localhost', port)
          server.close
          expect(true).to be(true) # Port is available
        rescue Errno::EADDRINUSE
          fail "Port #{port} is already in use in CI environment"
        end
      end
    end
  end
  
  describe 'Fix Verification' do
    it 'verifies that server block was removed' do
      # Simply verify the fix is in place
      content = File.read(vite_config_ts_path)
      
      # Check that server block is not present
      expect(content).not_to match(/server\s*:\s*{/),
        "vite.config.ts should not have a server block"
      
      # Check that warning comment is present
      expect(content).to include('IMPORTANT: Do NOT add a `server` configuration block'),
        "vite.config.ts should have warning comment about server block"
      
      # Check that vite-plugin-ruby is configured
      expect(content).to match(/RubyPlugin\(\)/),
        "vite.config.ts should have RubyPlugin configured"
    end
    
    it 'ensures vite-plugin-ruby handles host translation' do
      # When host is 0.0.0.0 in vite.json and no server block exists,
      # vite-plugin-ruby should handle the translation
      config = JSON.parse(File.read(vite_json_path))
      vite_config = File.read(vite_config_ts_path)
      
      if config.dig('development', 'host') == '0.0.0.0'
        expect(vite_config).not_to match(/server\s*:\s*{/),
          "With host 0.0.0.0, server block must not exist for plugin to work"
      end
    end
  end
  
  private
  
  # Helper method to access view helpers in specs
  def helper
    @helper ||= Class.new do
      include ActionView::Helpers
      include ViteRails::TagHelpers
    end.new
  end
end