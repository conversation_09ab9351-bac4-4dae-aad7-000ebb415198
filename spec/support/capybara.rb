# spec/support/capybara.rb
require 'capybara/rspec'
require 'capybara/cuprite'

# Configure Cuprite driver for modern headless testing
Capybara.javascript_driver = :cuprite
Capybara.default_driver = :rack_test # Fast for non-JS tests

# Configure Cuprite options
Capybara.register_driver :cuprite do |app|
  Capybara::Cuprite::Driver.new(
    app,
    window_size: [1200, 800],
    browser_options: {},
    process_timeout: 10,
    inspector: true,
    headless: !ENV['HEADLESS'].in?([nil, '', '0', 'false'])
  )
end

# Use Puma server (same as development)
Capybara.server = :puma, { Silent: true }

# Configure timeouts
Capybara.default_max_wait_time = 5
Capybara.default_normalize_ws = true

# Asset host for tests
Capybara.asset_host = 'http://localhost:3000'

# Configure for system specs
RSpec.configure do |config|
  config.include Capybara::DSL, type: :feature
  config.include Capybara::DSL, type: :system
  
  # Set driver for system specs
  config.before(:each, type: :system) do
    driven_by :cuprite
  end
  
  # Set JS driver for feature specs that need JavaScript
  config.before(:each, type: :feature, js: true) do
    Capybara.current_driver = :cuprite
  end
  
  # Reset driver after each test
  config.after(:each, type: :feature) do
    Capybara.reset_sessions!
    Capybara.use_default_driver
  end
end