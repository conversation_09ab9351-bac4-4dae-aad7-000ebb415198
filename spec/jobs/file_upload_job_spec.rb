require 'rails_helper'

RSpec.describe FileUploadJob, type: :job do
  include ActiveJob::TestHelper
  
  let(:user) { create(:user) }
  let(:project) { create(:project, user: user) }
  let(:upload) { create(:upload, :transferred, user: user, target: project) }
  
  before do
    # Create a test file in temp storage
    FileUtils.mkdir_p(File.dirname(upload.temp_file_path))
    File.write(upload.temp_file_path, 'test file content')
    
    # Mock S3 interactions
    allow_any_instance_of(FileUploadJob).to receive(:s3_bucket).and_return(double('s3_bucket'))
    allow_any_instance_of(FileUploadJob).to receive(:upload_to_s3).and_return('test/s3/key.pdf')
    # Remove mock for wait_for_file as it doesn't exist
    # Mock target model attachment to avoid S3 blob creation
    allow_any_instance_of(FileUploadJob).to receive(:attach_to_target_model)
  end
  
  after do
    # Clean up test files
    if upload.temp_file_path && File.exist?(upload.temp_file_path)
      File.delete(upload.temp_file_path)
    end
  end

  describe '#perform' do
    context 'with transferred upload' do
      it 'processes upload successfully' do
        expect {
          perform_enqueued_jobs do
            FileUploadJob.perform_later(upload.id, upload.temp_file_path)
          end
        }.to change { upload.reload.status }.from('transferred').to('completed')
      end
      
      it 'marks upload as processing during processing' do
        allow_any_instance_of(FileUploadJob).to receive(:upload_to_s3) do
          expect(upload.reload.status).to eq('processing')
          'test/s3/key.pdf'
        end
        
        perform_enqueued_jobs do
          FileUploadJob.perform_later(upload.id, upload.temp_file_path)
        end
      end
      
      it 'updates progress during upload' do
        expect(upload).to receive(:update_progress!).at_least(:once)
        
        perform_enqueued_jobs do
          FileUploadJob.perform_later(upload.id, upload.temp_file_path)
        end
      end
      
      it 'sets s3_key on completion' do
        perform_enqueued_jobs do
          FileUploadJob.perform_later(upload.id, upload.temp_file_path)
        end
        
        expect(upload.reload.s3_key).to eq('test/s3/key.pdf')
      end
      
      it 'cleans up temp file after processing' do
        expect(File.exist?(upload.temp_file_path)).to be true
        
        perform_enqueued_jobs do
          FileUploadJob.perform_later(upload.id, upload.temp_file_path)
        end
        
        expect(File.exist?(upload.temp_file_path)).to be false
      end
      
      it 'attaches file to target project' do
        expect {
          perform_enqueued_jobs do
            FileUploadJob.perform_later(upload.id, upload.temp_file_path)
          end
        }.to change { project.private_files.count }.by(1)
      end
      
      it 'broadcasts status updates' do
        expect(UploadChannel).to receive(:broadcast_to).with(upload, hash_including(
          status: 'processing'
        ))
        expect(UploadChannel).to receive(:broadcast_to).with(upload, hash_including(
          status: 'completed'
        ))
        
        perform_enqueued_jobs do
          FileUploadJob.perform_later(upload.id, upload.temp_file_path)
        end
      end
    end

    context 'with upload in pending state' do
      let(:pending_upload) { create(:upload, :pending, user: user, target: project) }
      
      before do
        # Set up temp file path for pending upload
        temp_filename = "test_pending_#{SecureRandom.hex(8)}.pdf"
        temp_path = Rails.root.join('tmp', 'uploads', temp_filename)
        pending_upload.update_column(:temp_file_path, temp_path.to_s)
        
        # Ensure the pending upload has a temp file to work with
        FileUtils.mkdir_p(File.dirname(pending_upload.temp_file_path))
        File.write(pending_upload.temp_file_path, 'test file content')
      end
      
      after do
        # Clean up test files
        if pending_upload.temp_file_path && File.exist?(pending_upload.temp_file_path)
          File.delete(pending_upload.temp_file_path)
        end
      end

      it 'processes the upload successfully' do
        expect {
          perform_enqueued_jobs do
            FileUploadJob.perform_later(pending_upload.id, pending_upload.temp_file_path)
          end
        }.to change { pending_upload.reload.status }.from('pending').to('completed')
      end
      
      it 'transitions through transferred and processing states' do
        # Mock the S3 upload to inspect state during processing
        allow_any_instance_of(FileUploadJob).to receive(:upload_to_s3) do |job_instance|
          upload = job_instance.instance_variable_get(:@upload)
          # At this point, it should have been marked as processing
          expect(upload.status).to eq('processing')
          'test/s3/key.pdf'
        end

        perform_enqueued_jobs do
          FileUploadJob.perform_later(pending_upload.id, pending_upload.temp_file_path)
        end
      end
    end

    context 'with missing temp file' do
      before do
        File.delete(upload.temp_file_path) if File.exist?(upload.temp_file_path)
      end
      
      it 'raises StandardError for missing file' do
        expect {
          perform_enqueued_jobs do
            FileUploadJob.perform_later(upload.id, upload.temp_file_path)
          end
        }.to raise_error(StandardError, /Temp file not found/)
      end
      
      it 'marks upload as failed' do
        begin
          perform_enqueued_jobs do
            FileUploadJob.perform_later(upload.id, upload.temp_file_path)
          end
        rescue StandardError
          # Expected error
        end
        
        expect(upload.reload.status).to eq('failed')
        expect(upload.error_message).to include('Temp file not found')
      end
    end

    context 'with S3 upload failure' do
      before do
        allow_any_instance_of(FileUploadJob).to receive(:upload_to_s3)
          .and_raise(Aws::S3::Errors::ServiceError.new('test', 'S3 error'))
      end
      
      it 'marks upload as failed' do
        begin
          perform_enqueued_jobs do
            FileUploadJob.perform_later(upload.id, upload.temp_file_path)
          end
        rescue Aws::S3::Errors::ServiceError
          # Expected error
        end
        
        expect(upload.reload.status).to eq('failed')
        expect(upload.error_message).to include('S3 error')
      end
      
      it 'still cleans up temp file' do
        expect(File.exist?(upload.temp_file_path)).to be true
        
        begin
          perform_enqueued_jobs do
            FileUploadJob.perform_later(upload.id, upload.temp_file_path)
          end
        rescue Aws::S3::Errors::ServiceError
          # Expected error
        end
        
        expect(File.exist?(upload.temp_file_path)).to be false
      end
    end

    context 'with non-existent upload' do
      it 'raises RecordNotFound error' do
        expect {
          perform_enqueued_jobs do
            FileUploadJob.perform_later(99999, '/tmp/nonexistent')
          end
        }.to raise_error(ActiveRecord::RecordNotFound)
      end
    end
  end

  describe 'retry behavior' do
    it 'retries on StandardError' do
      call_count = 0
      allow_any_instance_of(FileUploadJob).to receive(:upload_to_s3) do
        call_count += 1
        raise StandardError, 'Temporary error' if call_count < 3
        'test/s3/key.pdf'
      end
      
      perform_enqueued_jobs do
        FileUploadJob.perform_later(upload.id, upload.temp_file_path)
      end
      
      expect(call_count).to eq(3)
      expect(upload.reload.status).to eq('completed')
    end
    
    it 'discards job after max retries' do
      allow_any_instance_of(FileUploadJob).to receive(:upload_to_s3)
        .and_raise(StandardError, 'Persistent error')
      
      # Simulate job execution at the final attempt
      job = FileUploadJob.new
      allow(job).to receive(:executions).and_return(5)
      
      # The job should handle the error without re-raising on final attempt
      expect { job.perform(upload.id) }.not_to raise_error
      
      expect(upload.reload.status).to eq('failed')
      expect(upload.error_message).to include('Job failed after all retries')
    end
    
    context 'GoodJob compatibility - rescue_from behavior' do
      it 'tracks execution attempts correctly' do
        execution_counts = []
        
        allow_any_instance_of(FileUploadJob).to receive(:upload_to_s3) do |job|
          execution_counts << job.executions
          raise StandardError, 'Test error'
        end
        
        begin
          perform_enqueued_jobs do
            FileUploadJob.perform_later(upload.id, upload.temp_file_path)
          end
        rescue StandardError
          # Expected error on final attempt
        end
        
        # Should have attempted 5 times (1 initial + 4 retries)
        expect(execution_counts).to eq([1, 2, 3, 4, 5])
      end
      
      it 'marks upload as failed on final attempt' do
        allow_any_instance_of(FileUploadJob).to receive(:upload_to_s3)
          .and_raise(StandardError, 'Persistent error')
        
        # Mock executions to simulate final attempt
        allow_any_instance_of(FileUploadJob).to receive(:executions).and_return(5)
        
        begin
          FileUploadJob.new.perform(upload.id, upload.temp_file_path)
        rescue StandardError
          # Expected - rescue_from should have handled it
        end
        
        expect(upload.reload.status).to eq('failed')
        expect(upload.error_message).to include('Persistent error')
      end
      
      it 'cleans up temp file on final failure' do
        allow_any_instance_of(FileUploadJob).to receive(:upload_to_s3)
          .and_raise(StandardError, 'Persistent error')
        
        # Mock executions to simulate final attempt
        allow_any_instance_of(FileUploadJob).to receive(:executions).and_return(5)
        
        expect(File.exist?(upload.temp_file_path)).to be true
        
        begin
          FileUploadJob.new.perform(upload.id, upload.temp_file_path)
        rescue StandardError
          # Expected
        end
        
        expect(File.exist?(upload.temp_file_path)).to be false
      end
      
      it 're-raises exception when not final attempt' do
        allow_any_instance_of(FileUploadJob).to receive(:upload_to_s3)
          .and_raise(StandardError, 'Temporary error')
        
        # Mock executions to simulate non-final attempt
        allow_any_instance_of(FileUploadJob).to receive(:executions).and_return(2)
        
        expect {
          FileUploadJob.new.perform(upload.id, upload.temp_file_path)
        }.to raise_error(StandardError, 'Temporary error')
        
        # Should not mark as failed on non-final attempt
        expect(upload.reload.status).to eq('processing')
      end
    end
  end

  describe 'S3 integration' do
    let(:mock_s3_bucket) { double('s3_bucket') }
    let(:mock_s3_object) { double('s3_object') }
    
    before do
      allow_any_instance_of(FileUploadJob).to receive(:s3_bucket).and_return(mock_s3_bucket)
      allow(mock_s3_bucket).to receive(:object).and_return(mock_s3_object)
    end
    
    it 'generates unique S3 keys' do
      job = FileUploadJob.new
      job.instance_variable_set(:@upload, upload)
      
      key1 = job.send(:generate_s3_key)
      key2 = job.send(:generate_s3_key)
      
      expect(key1).to match(%r{uploads/\d{4}/\d{2}/\d{2}/\w+/[a-f0-9]{32}\.pdf})
      expect(key1).not_to eq(key2)
    end
    
    it 'includes upload metadata in S3 object' do
      expect(mock_s3_object).to receive(:put).with(hash_including(
        content_type: upload.content_type,
        metadata: hash_including(
          'upload-id' => upload.id.to_s,
          'user-id' => upload.user_id.to_s,
          'original-filename' => upload.original_filename
        )
      ))
      
      perform_enqueued_jobs do
        FileUploadJob.perform_later(upload.id, upload.temp_file_path)
      end
    end
  end

  describe 'early cancellation handling' do
    let(:temp_path) { upload.temp_file_path || '/tmp/test_file.pdf' }

    context 'when upload is cancelled before job starts' do
      it 'detects cancellation and exits early' do
        upload.mark_cancelled!
        
        expect {
          perform_enqueued_jobs do
            FileUploadJob.perform_later(upload.id, temp_path)
          end
        }.not_to change { upload.reload.status }.from('cancelled')
      end

      it 'cleans up temp file when cancelled' do
        # Create actual temp file
        FileUtils.mkdir_p(File.dirname(temp_path))
        File.write(temp_path, 'test content')
        upload.mark_cancelled!

        expect(File.exist?(temp_path)).to be true

        perform_enqueued_jobs do
          FileUploadJob.perform_later(upload.id, temp_path)
        end

        expect(File.exist?(temp_path)).to be false
      end

      it 'logs cancellation message' do
        upload.mark_cancelled!
        
        # Just verify the job doesn't crash and completes silently
        expect {
          perform_enqueued_jobs do
            FileUploadJob.perform_later(upload.id, temp_path)
          end
        }.not_to raise_error
      end

      it 'does not attempt S3 upload when cancelled' do
        upload.mark_cancelled!
        
        expect_any_instance_of(FileUploadJob).not_to receive(:upload_to_s3)

        perform_enqueued_jobs do
          FileUploadJob.perform_later(upload.id, temp_path)
        end
      end
    end

    context 'when upload is cancelled while in transferred state' do
      it 'can process transferred uploads that are later cancelled' do
        upload.update!(status: :transferred)
        upload.mark_cancelled!

        expect {
          perform_enqueued_jobs do
            FileUploadJob.perform_later(upload.id, temp_path)
          end
        }.not_to change { upload.reload.status }.from('cancelled')
      end
    end
  end
end