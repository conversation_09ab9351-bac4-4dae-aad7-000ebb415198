# ABOUTME: Test for the bulk notification system to ensure it prevents job queue flooding
# ABOUTME: Verifies that bulk notifications respect rate limits and create only one job

require 'rails_helper'

RSpec.describe NotificationMailer, type: :mailer do
  include ActiveJob::<PERSON>Helper
  describe '#bulk_new_project_notification' do
    let(:project) { create(:project, network_only: false, semi_public: true, project_status: true) }
    let(:users) { create_list(:user, 5) }
    let(:user_ids) { users.map(&:id) }

    before do
      # Clear deliveries before each test for clean slate
      ActionMailer::Base.deliveries.clear
      
      # Mock rate_limit_sleep to avoid actual delays in tests
      allow(NotificationMailer).to receive(:rate_limit_sleep)
      
      users.each do |user|
        create(:user_profile, user: user, email: user.email)
      end
    end

    it 'sends individual emails to each user' do
      # Count how many emails are sent using class method
      expect {
        NotificationMailer.bulk_new_project_notification(project, user_ids)
      }.to change { ActionMailer::Base.deliveries.count }.by(5)
    end

    it 'respects rate limiting by calling sleep between emails' do
      NotificationMailer.bulk_new_project_notification(project, user_ids)
      
      # Should call rate_limit_sleep after each email (5 times for 5 users)
      expect(NotificationMailer).to have_received(:rate_limit_sleep).exactly(5).times
    end

    it 'uses the correct subject for each email' do
      NotificationMailer.bulk_new_project_notification(project, user_ids)
      
      emails = ActionMailer::Base.deliveries.last(5)
      emails.each do |email|
        expect(email.subject).to eq('New Project')
      end
    end

    it 'sends to the correct recipients' do
      NotificationMailer.bulk_new_project_notification(project, user_ids)
      
      emails = ActionMailer::Base.deliveries.last(5)
      sent_to_emails = emails.map { |e| e.to.first }.sort
      expected_emails = users.map(&:email).sort
      
      expect(sent_to_emails).to eq(expected_emails)
    end

    it 'creates only one background job when using BulkNotificationJob' do
      # This should create only ONE job, not 5
      expect {
        BulkNotificationJob.perform_later(project, user_ids)
      }.to have_enqueued_job(BulkNotificationJob).with(project, user_ids)
    end

    context 'with many users' do
      let(:many_users) { create_list(:user, 100) }
      let(:many_user_ids) { many_users.map(&:id) }

      before do
        many_users.each do |user|
          create(:user_profile, user: user, email: user.email)
        end
      end

      it 'still creates only one job for 100 users' do
        expect {
          BulkNotificationJob.perform_later(project, many_user_ids)
        }.to have_enqueued_job(BulkNotificationJob).with(project, many_user_ids)
      end
    end
  end
end