# spec/factories/projects.rb
FactoryBot.define do
  factory :project do
    association :user
    summary { "Secure File Test Project" }
    location { "Bratislava, Slovakia" }
    full_description { "A test project for secure file display testing" }
    project_type { "real_estate" }
    category { "homes" }
    subcategory { "flat" }
    price_value { 100000 }
    price_text { "100k-500k EUR" }
    price_currency { "EUR" }
    country { "Slovakia" }
    country_code { "SK" }
    latitude { 48.1486 }
    longitude { 17.1077 }
    approved { false }  # Don't set approved by default - admin only
    project_status { true }
    network_only { true }  # Set visibility option
    
    # For tests only - bypasses validations
    trait :minimal do
      to_create { |instance| instance.save!(validate: false) }
    end
    
    trait :with_files do
      after(:create) do |project|
        # Attach test PDF file
        project.private_files.attach(
          io: File.open(Rails.root.join('spec', 'fixtures', 'files', 'test.pdf')),
          filename: 'test.pdf',
          content_type: 'application/pdf'
        )
        
        # Attach test image file
        project.private_files.attach(
          io: File.open(Rails.root.join('spec', 'fixtures', 'files', 'test_image.png')),
          filename: 'test_image.png',
          content_type: 'image/png'
        )
      end
    end
    
    trait :unapproved do
      approved { false }
    end
    
    trait :inactive do
      project_status { false }
    end
  end
end