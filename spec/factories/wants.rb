# ABOUTME: Factory for Want model that follows exact same patterns as Project factory  
# ABOUTME: Provides realistic test data for want creation and testing scenarios

FactoryBot.define do
  factory :want do
    association :user
    summary { "Looking for real estate investment opportunities in Bratislava area" }
    description { "Detailed description of investment preferences and requirements" }
    place { "Bratislava, Slovakia" }
    want_type { "real_estate" }
    category { "commercial_property" }
    subcategory { "warehouse" }
    price_min { 50000 }
    price_max { 200000 }
    price_currency { "EUR" }
    country { "Slovakia" }
    country_code { "SK" }
    notification { true }
    
    trait :business_want do
      summary { "Seeking business acquisition opportunities" }
      want_type { "business" }
      category { "business_acquisition" }
      subcategory { "asset_purchase" }
    end
    
    trait :old_want do
      created_at { 45.days.ago }
    end
    
    trait :recent_want do
      created_at { 15.days.ago }
    end
    
    trait :with_price_range do
      price_min { 100000 }
      price_max { 500000 }
      price_currency { "EUR" }
    end
    
    trait :without_price do
      price_min { nil }
      price_max { nil }
      price_currency { nil }
    end
  end
end