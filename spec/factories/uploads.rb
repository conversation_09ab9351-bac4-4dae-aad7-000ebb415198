FactoryBot.define do
  factory :upload do
    association :user
    original_filename { "test_file_#{SecureRandom.hex(4)}.pdf" }
    content_type { "application/pdf" }
    file_size { rand(1000..10_000_000) } # 1KB to 10MB
    status { :pending }
    progress_percentage { 0 }
    
    # Traits for different states in enhanced state machine
    trait :pending do
      status { :pending }
      temp_file_path { nil }
    end
    
    trait :transferred do
      status { :transferred }
      temp_file_path { "/tmp/uploads/transferred_file_#{SecureRandom.hex(8)}" }
    end
    
    trait :processing do
      status { :processing }
      temp_file_path { "/tmp/uploads/processing_file_#{SecureRandom.hex(8)}" }
      progress_percentage { rand(1..99) }
    end
    
    trait :completed do
      status { :completed }
      progress_percentage { 100 }
      s3_key { "uploads/#{SecureRandom.hex(16)}/#{original_filename}" }
      temp_file_path { nil } # Cleaned up after completion
    end
    
    trait :failed do
      status { :failed }
      error_message { "Test error: Upload failed during processing" }
      temp_file_path { "/tmp/uploads/failed_file_#{SecureRandom.hex(8)}" }
    end
    
    trait :cancelled do
      status { :cancelled }
      temp_file_path { nil } # Cleaned up after cancellation
    end
    
    # Traits for different file types
    trait :image do
      original_filename { "test_image_#{SecureRandom.hex(4)}.jpg" }
      content_type { "image/jpeg" }
      file_size { rand(100_000..5_000_000) } # 100KB to 5MB
    end
    
    trait :pdf do
      original_filename { "test_document_#{SecureRandom.hex(4)}.pdf" }
      content_type { "application/pdf" }
      file_size { rand(500_000..20_000_000) } # 500KB to 10MB
    end
    
    trait :large_file do
      file_size { rand(50_000_000..100_000_000) } # 50MB to 100MB
    end
    
    trait :with_target do
      association :target, factory: :project
    end
    
    # After build callback to ensure signed_id_token is set
    after(:build) do |upload|
      upload.signed_id_token ||= SecureRandom.urlsafe_base64(32)
    end
  end
end