FactoryBot.define do
  factory :referral_code do
    sequence(:code) { |n| "CODE#{n.to_s.rjust(4, '0')}" }
    association :created_by, factory: :user
    status { :active }
    tier_upgrade_to { :premium }
    duration_months { 1 }
    max_uses { 1 }
    current_uses { 0 }
    expires_at { 1.month.from_now }
    description { "Test referral code" }
    
    trait :vanguard_upgrade do
      tier_upgrade_to { :vanguard }
    end
    
    trait :expired do
      expires_at { 1.day.ago }
    end
    
    trait :used_up do
      status { :used_up }
      current_uses { 1 }
    end
    
    trait :disabled do
      status { :disabled }
    end
  end
end
