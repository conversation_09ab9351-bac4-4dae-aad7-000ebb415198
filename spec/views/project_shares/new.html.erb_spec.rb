require 'rails_helper'

RSpec.describe "project_shares/new", type: :view do
  before(:each) do
    assign(:project_share, ProjectShare.new(
      project: nil,
      user: nil
    ))
  end

  it "renders new project_share form" do
    render

    assert_select "form[action=?][method=?]", project_shares_path, "post" do

      assert_select "input[name=?]", "project_share[project_id]"

      assert_select "input[name=?]", "project_share[user_id]"
    end
  end
end
