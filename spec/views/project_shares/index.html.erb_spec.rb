require 'rails_helper'

RSpec.describe "project_shares/index", type: :view do
  before(:each) do
    assign(:project_shares, [
      ProjectShare.create!(
        project: nil,
        user: nil
      ),
      ProjectShare.create!(
        project: nil,
        user: nil
      )
    ])
  end

  it "renders a list of project_shares" do
    render
    cell_selector = 'div>p'
    assert_select cell_selector, text: Regexp.new(nil.to_s), count: 2
    assert_select cell_selector, text: Regexp.new(nil.to_s), count: 2
  end
end
