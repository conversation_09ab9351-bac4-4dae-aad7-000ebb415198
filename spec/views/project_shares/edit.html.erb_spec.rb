require 'rails_helper'

RSpec.describe "project_shares/edit", type: :view do
  let(:project_share) {
    ProjectShare.create!(
      project: nil,
      user: nil
    )
  }

  before(:each) do
    assign(:project_share, project_share)
  end

  it "renders the edit project_share form" do
    render

    assert_select "form[action=?][method=?]", project_share_path(project_share), "post" do

      assert_select "input[name=?]", "project_share[project_id]"

      assert_select "input[name=?]", "project_share[user_id]"
    end
  end
end
