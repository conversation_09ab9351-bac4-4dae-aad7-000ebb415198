require 'rails_helper'

RSpec.describe "user_profiles/new", type: :view do
  before(:each) do
    assign(:user_profile, UserProfile.new(
      first_name: "MyString",
      last_name: "MyString",
      email: "MyString",
      bio: "MyText",
      phone: "MyString",
      location: "MyString",
      user: nil
    ))
  end

  it "renders new user_profile form" do
    render

    assert_select "form[action=?][method=?]", user_profiles_path, "post" do

      assert_select "input[name=?]", "user_profile[first_name]"

      assert_select "input[name=?]", "user_profile[last_name]"

      assert_select "input[name=?]", "user_profile[email]"

      assert_select "textarea[name=?]", "user_profile[bio]"

      assert_select "input[name=?]", "user_profile[phone]"

      assert_select "input[name=?]", "user_profile[location]"

      assert_select "input[name=?]", "user_profile[user_id]"
    end
  end
end
