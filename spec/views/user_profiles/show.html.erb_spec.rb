require 'rails_helper'

RSpec.describe "user_profiles/show", type: :view do
  before(:each) do
    assign(:user_profile, UserProfile.create!(
      first_name: "First Name",
      last_name: "Last Name",
      email: "Email",
      bio: "MyText",
      phone: "Phone",
      location: "Location",
      user: nil
    ))
  end

  it "renders attributes in <p>" do
    render
    expect(rendered).to match(/First Name/)
    expect(rendered).to match(/Last Name/)
    expect(rendered).to match(/Email/)
    expect(rendered).to match(/MyText/)
    expect(rendered).to match(/Phone/)
    expect(rendered).to match(/Location/)
    expect(rendered).to match(//)
  end
end
