require 'rails_helper'

RSpec.describe "user_profiles/index", type: :view do
  before(:each) do
    assign(:user_profiles, [
      UserProfile.create!(
        first_name: "First Name",
        last_name: "Last Name",
        email: "Email",
        bio: "MyText",
        phone: "Phone",
        location: "Location",
        user: nil
      ),
      UserProfile.create!(
        first_name: "First Name",
        last_name: "Last Name",
        email: "<PERSON>ail",
        bio: "MyText",
        phone: "Phone",
        location: "Location",
        user: nil
      )
    ])
  end

  it "renders a list of user_profiles" do
    render
    cell_selector = 'div>p'
    assert_select cell_selector, text: Regexp.new("First Name".to_s), count: 2
    assert_select cell_selector, text: Regexp.new("Last Name".to_s), count: 2
    assert_select cell_selector, text: Regexp.new("Email".to_s), count: 2
    assert_select cell_selector, text: Regexp.new("MyText".to_s), count: 2
    assert_select cell_selector, text: Regexp.new("Phone".to_s), count: 2
    assert_select cell_selector, text: Regexp.new("Location".to_s), count: 2
    assert_select cell_selector, text: Regexp.new(nil.to_s), count: 2
  end
end
