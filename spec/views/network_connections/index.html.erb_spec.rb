require 'rails_helper'

RSpec.describe "network_connections/index", type: :view do
  before(:each) do
    assign(:network_connections, [
      NetworkConnection.create!(
        level: 2
      ),
      NetworkConnection.create!(
        level: 2
      )
    ])
  end

  it "renders a list of network_connections" do
    render
    cell_selector = 'div>p'
    assert_select cell_selector, text: Regexp.new(2.to_s), count: 2
  end
end
