require 'rails_helper'

RSpec.describe "network_connections/edit", type: :view do
  let(:network_connection) {
    NetworkConnection.create!(
      level: 1
    )
  }

  before(:each) do
    assign(:network_connection, network_connection)
  end

  it "renders the edit network_connection form" do
    render

    assert_select "form[action=?][method=?]", network_connection_path(network_connection), "post" do

      assert_select "input[name=?]", "network_connection[level]"
    end
  end
end
