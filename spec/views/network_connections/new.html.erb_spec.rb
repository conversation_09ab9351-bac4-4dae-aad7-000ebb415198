require 'rails_helper'

RSpec.describe "network_connections/new", type: :view do
  before(:each) do
    assign(:network_connection, NetworkConnection.new(
      level: 1
    ))
  end

  it "renders new network_connection form" do
    render

    assert_select "form[action=?][method=?]", network_connections_path, "post" do

      assert_select "input[name=?]", "network_connection[level]"
    end
  end
end
