require 'rails_helper'

RSpec.describe "projects/new", type: :view do
  before(:each) do
    assign(:project, Project.new(
      user: nil,
      summary: "MyText",
      public_visible: false,
      summary_only: false,
      full_access: false,
      visibility: 1
    ))
    
    # Setup cached translations that the form now requires
    assign(:cached_project_types, Project::PROJECT_TYPES)
    assign(:cached_categories, Project::CATEGORIES)
    assign(:cached_translations, {
      projectTypes: Hash[Project.project_types.keys.map { |t| [t, "#{t.humanize}"] }],
      categories: Hash[Project.categories.keys.map { |c| [c, "#{c.humanize}"] }],
      subcategories: Hash[Project.subcategories.keys.map { |s| [s, "#{s.humanize}"] }]
    })
    assign(:cached_placeholders, {
      category: "Category",
      subcategory: "Subcategory"
    })
  end

  it "renders new project form" do
    render

    assert_select "form[action=?][method=?]", projects_path, "post" do

      assert_select "input[name=?]", "project[user_id]"

      assert_select "textarea[name=?]", "project[summary]"

      assert_select "input[name=?]", "project[public_visible]"

      assert_select "input[name=?]", "project[summary_only]"

      assert_select "input[name=?]", "project[full_access]"

      assert_select "input[name=?]", "project[visibility]"
    end
  end
end
