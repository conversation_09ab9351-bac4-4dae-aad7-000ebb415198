require 'rails_helper'

RSpec.describe "Uploads", type: :request do
  let(:user) { create(:user) }
  let(:project) { create(:project, user: user) }
  
  # Set default locale for all tests
  around(:each) do |example|
    I18n.with_locale(:en) do
      example.run
    end
  end
  
  before do
    # Ensure all users have complete profiles (following existing pattern from secure_file_access_spec)
    [user].each do |u|
      u.user_profile.update!(
        first_name: 'Test',
        last_name: 'User',
        city: 'Bratislava', 
        country: 'Slovakia'
      ) if u.user_profile
    end
    
    sign_in user
  end

  describe "POST /uploads" do
    let(:test_file) { fixture_file_upload('test_file.pdf', 'application/pdf') }
    let(:valid_params) do
      {
        file: test_file,
        target_type: 'Project',
        target_id: project.id
      }
    end

    context "with valid file upload" do
      it "creates upload record in pending status" do
        expect {
          post uploads_path, params: valid_params
        }.to change(Upload, :count).by(1)
        
        upload = Upload.last
        expect(upload.status).to eq('pending')  # Jobs will transition to transferred
        expect(upload.user).to eq(user)
        expect(upload.target).to eq(project)
        expect(upload.original_filename).to eq('test_file.pdf')
        expect(upload.content_type).to eq('application/pdf')
      end
      
      it "saves file to temp storage" do
        post uploads_path, params: valid_params
        
        upload = Upload.last
        expect(upload.temp_file_path).to be_present
        expect(File.exist?(upload.temp_file_path)).to be true
      end
      
      it "creates upload in pending status (job will transition)" do
        post uploads_path, params: valid_params
        
        upload = Upload.last
        expect(upload.status).to eq('pending')
      end
      
      it "enqueues background job with upload_id and temp_path" do
        expect {
          post uploads_path, params: valid_params
        }.to have_enqueued_job(FileUploadJob).with(a_kind_of(Integer), a_kind_of(String))
      end

      it "enqueues background job immediately without delay" do
        expect {
          post uploads_path, params: valid_params
        }.to have_enqueued_job(FileUploadJob).with(a_kind_of(Integer), a_kind_of(String)).at(:no_wait)
      end
      
      it "returns 202 status with upload info" do
        post uploads_path, params: valid_params
        
        expect(response).to have_http_status(:accepted)
        
        json_response = JSON.parse(response.body)
        upload = Upload.last
        
        expect(json_response).to include(
          'id' => upload.id,
          'status' => 'pending',
          'original_filename' => 'test_file.pdf',
          'file_size' => test_file.size,
          'progress_percentage' => 0
        )
        expect(json_response['signed_id']).to be_present
      end
      
      it "broadcasts upload creation via Action Cable" do
        expect(UploadChannel).to receive(:broadcast_to).with(
          an_instance_of(Upload), 
          hash_including(
            status: 'pending',
            progress: 0
          )
        )
        
        post uploads_path, params: valid_params
      end
    end

    context "with multiple files" do
      let(:file1) { fixture_file_upload('test_file.pdf', 'application/pdf') }
      let(:file2) { fixture_file_upload('test_image.jpg', 'image/jpeg') }
      let(:multiple_files_params) do
        {
          files: [file1, file2],
          target_type: 'Project',
          target_id: project.id
        }
      end

      it "creates multiple upload records" do
        expect {
          post uploads_path, params: multiple_files_params
        }.to change(Upload, :count).by(2)
      end
      
      it "enqueues jobs for each file" do
        expect {
          post uploads_path, params: multiple_files_params
        }.to have_enqueued_job(FileUploadJob).twice
      end
      
      it "returns array of upload info" do
        post uploads_path, params: multiple_files_params
        
        expect(response).to have_http_status(:accepted)
        json_response = JSON.parse(response.body)
        expect(json_response['uploads']).to be_an(Array)
        expect(json_response['uploads'].length).to eq(2)
      end
    end

    context "without authentication" do
      before { sign_out user }
      
      it "redirects to sign in page" do
        expect {
          post "/en/uploads", params: valid_params
        }.to raise_error(ActionController::RoutingError, /No route matches/)
      end
    end

    context "with invalid file size" do
      before do
        allow(Rails.application.config).to receive(:max_upload_size).and_return(1.byte)
      end
      
      it "returns 422 with error" do
        post uploads_path, params: valid_params
        
        expect(response).to have_http_status(:unprocessable_entity)
        json_response = JSON.parse(response.body)
        expect(json_response['errors']).to include('File size too large. Maximum size is 1 Byte.')
      end
      
      it "does not create upload record" do
        expect {
          post uploads_path, params: valid_params
        }.not_to change(Upload, :count)
      end
    end

    context "with invalid content type" do
      let(:invalid_file) { fixture_file_upload('test_file.exe', 'application/x-executable') }
      let(:invalid_params) do
        {
          file: invalid_file,
          target_type: 'Project',
          target_id: project.id
        }
      end
      
      it "returns 422 with error" do
        post uploads_path, params: invalid_params
        
        expect(response).to have_http_status(:unprocessable_entity)
        json_response = JSON.parse(response.body)
        expect(json_response['errors']).to include("Content type 'application/x-executable' is not allowed.")
      end
    end

    context "with missing file" do
      it "returns 422 with error" do
        post uploads_path, params: { target_type: 'Project', target_id: project.id }
        
        expect(response).to have_http_status(:unprocessable_entity)
        json_response = JSON.parse(response.body)
        expect(json_response['errors']).to include('File is required')
      end
    end

    context "with invalid target" do
      it "returns 422 when target not found" do
        post uploads_path, params: valid_params.merge(target_id: 99999)
        
        expect(response).to have_http_status(:unprocessable_entity)
        json_response = JSON.parse(response.body)
        expect(json_response['errors']).to include('Target not found')
      end
      
      it "returns 403 when user cannot access target" do
        other_project = create(:project)
        
        post uploads_path, params: valid_params.merge(target_id: other_project.id)
        
        expect(response).to have_http_status(:forbidden)
        json_response = JSON.parse(response.body)
        expect(json_response['errors']).to include('Not authorized to upload to this target')
      end
    end

    context "with rate limiting" do
      before do
        allow(Rails.application.config).to receive(:upload_rate_limit_per_user_per_hour).and_return(1)
        create(:upload, user: user, created_at: 30.minutes.ago)
      end
      
      it "returns 429 when rate limit exceeded" do
        post uploads_path, params: valid_params
        
        expect(response).to have_http_status(:too_many_requests)
        json_response = JSON.parse(response.body)
        expect(json_response['errors']).to include('Rate limit exceeded. Try again later.')
      end
    end

    context "with too many files in single request" do
      before do
        allow(Rails.application.config).to receive(:max_uploads_per_request).and_return(1)
      end
      
      let(:too_many_files) do
        {
          files: [
            fixture_file_upload('test_file.pdf', 'application/pdf'),
            fixture_file_upload('test_image.jpg', 'image/jpeg')
          ],
          target_type: 'Project',
          target_id: project.id
        }
      end
      
      it "returns 422 with error" do
        post uploads_path, params: too_many_files
        
        expect(response).to have_http_status(:unprocessable_entity)
        json_response = JSON.parse(response.body)
        expect(json_response['errors']).to include(a_string_including('Too many files'))
      end
    end
  end

  describe "GET /uploads/:id" do
    let(:upload) { create(:upload, user: user, target: project) }
    
    context "when user owns upload" do
      it "returns upload status" do
        get upload_path(upload)
        
        expect(response).to have_http_status(:ok)
        json_response = JSON.parse(response.body)
        
        expect(json_response).to include(
          'id' => upload.id,
          'status' => upload.status,
          'progress_percentage' => upload.progress_percentage,
          'original_filename' => upload.original_filename
        )
      end
    end
    
    context "when user does not own upload" do
      let(:other_upload) { create(:upload, target: project) }
      
      it "returns 404" do
        get upload_path(other_upload)
        expect(response).to have_http_status(:not_found)
      end
    end
  end

  describe "DELETE /uploads/:id" do
    let(:upload) { create(:upload, :transferred, user: user, target: project) }
    
    context "when upload can be cancelled" do
      it "cancels upload (job will cleanup temp file)" do
        temp_path = upload.temp_file_path
        
        # Create temp file for testing
        FileUtils.mkdir_p(File.dirname(temp_path))
        File.write(temp_path, "test content")
        
        delete upload_path(upload)
        
        expect(response).to have_http_status(:ok)
        expect(upload.reload.status).to eq('cancelled')
        # Temp file persists (job handles cleanup)
        expect(File.exist?(temp_path)).to be true
        
        # Clean up test file
        File.delete(temp_path)
      end
      
      it "broadcasts cancellation" do
        expect(UploadChannel).to receive(:broadcast_to).with(upload, hash_including(
          status: 'cancelled'
        ))
        
        delete upload_path(upload)
      end
    end
    
    context "when upload cannot be cancelled" do
      let(:completed_upload) { create(:upload, :completed, user: user, target: project) }
      
      it "returns 422 with error" do
        delete upload_path(completed_upload)
        
        expect(response).to have_http_status(:unprocessable_entity)
        json_response = JSON.parse(response.body)
        expect(json_response['errors']).to include(/cannot be cancelled/)
      end
    end
  end
end