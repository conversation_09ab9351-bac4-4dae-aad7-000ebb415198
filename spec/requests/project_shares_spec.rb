require 'rails_helper'

# This spec was generated by rspec-rails when you ran the scaffold generator.
# It demonstrates how one might use RSpec to test the controller code that
# was generated by <PERSON>s when you ran the scaffold generator.
#
# It assumes that the implementation code is generated by the rails scaffold
# generator. If you are using any extension libraries to generate different
# controller code, this generated spec may or may not pass.
#
# It only uses APIs available in rails and/or rspec-rails. There are a number
# of tools you can use to make these specs even more expressive, but we're
# sticking to rails and rspec-rails APIs to keep things simple and stable.

RSpec.describe "/project_shares", type: :request do
  
  # This should return the minimal set of attributes required to create a valid
  # ProjectShare. As you add validations to ProjectShare, be sure to
  # adjust the attributes here as well.
  let(:valid_attributes) {
    skip("Add a hash of attributes valid for your model")
  }

  let(:invalid_attributes) {
    skip("Add a hash of attributes invalid for your model")
  }

  describe "GET /index" do
    it "renders a successful response" do
      ProjectShare.create! valid_attributes
      get project_shares_url
      expect(response).to be_successful
    end
  end

  describe "GET /show" do
    it "renders a successful response" do
      project_share = ProjectShare.create! valid_attributes
      get project_share_url(project_share)
      expect(response).to be_successful
    end
  end

  describe "GET /new" do
    it "renders a successful response" do
      get new_project_share_url
      expect(response).to be_successful
    end
  end

  describe "GET /edit" do
    it "renders a successful response" do
      project_share = ProjectShare.create! valid_attributes
      get edit_project_share_url(project_share)
      expect(response).to be_successful
    end
  end

  describe "POST /create" do
    context "with valid parameters" do
      it "creates a new ProjectShare" do
        expect {
          post project_shares_url, params: { project_share: valid_attributes }
        }.to change(ProjectShare, :count).by(1)
      end

      it "redirects to the created project_share" do
        post project_shares_url, params: { project_share: valid_attributes }
        expect(response).to redirect_to(project_share_url(ProjectShare.last))
      end
    end

    context "with invalid parameters" do
      it "does not create a new ProjectShare" do
        expect {
          post project_shares_url, params: { project_share: invalid_attributes }
        }.to change(ProjectShare, :count).by(0)
      end

      it "renders a response with 422 status (i.e. to display the 'new' template)" do
        post project_shares_url, params: { project_share: invalid_attributes }
        expect(response).to have_http_status(:unprocessable_entity)
      end
    end
  end

  describe "PATCH /update" do
    context "with valid parameters" do
      let(:new_attributes) {
        skip("Add a hash of attributes valid for your model")
      }

      it "updates the requested project_share" do
        project_share = ProjectShare.create! valid_attributes
        patch project_share_url(project_share), params: { project_share: new_attributes }
        project_share.reload
        skip("Add assertions for updated state")
      end

      it "redirects to the project_share" do
        project_share = ProjectShare.create! valid_attributes
        patch project_share_url(project_share), params: { project_share: new_attributes }
        project_share.reload
        expect(response).to redirect_to(project_share_url(project_share))
      end
    end

    context "with invalid parameters" do
      it "renders a response with 422 status (i.e. to display the 'edit' template)" do
        project_share = ProjectShare.create! valid_attributes
        patch project_share_url(project_share), params: { project_share: invalid_attributes }
        expect(response).to have_http_status(:unprocessable_entity)
      end
    end
  end

  describe "DELETE /destroy" do
    it "destroys the requested project_share" do
      project_share = ProjectShare.create! valid_attributes
      expect {
        delete project_share_url(project_share)
      }.to change(ProjectShare, :count).by(-1)
    end

    it "redirects to the project_shares list" do
      project_share = ProjectShare.create! valid_attributes
      delete project_share_url(project_share)
      expect(response).to redirect_to(project_shares_url)
    end
  end
end
