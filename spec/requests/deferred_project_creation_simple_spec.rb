# ABOUTME: Simplified tests for UNL-42 deferred project creation to verify core functionality
# ABOUTME: Focuses on essential behavior without complex edge cases to ensure basic implementation works

require 'rails_helper'

RSpec.describe "Deferred Project Creation Core Tests (UNL-42)", type: :request do
  let(:user) { create(:user) }
  
  before do
    sign_in user
  end

  describe "Core deferred creation behavior" do
    it "does not create project on /new visit" do
      initial_count = Project.count
      
      get new_project_path
      
      expect(response).to have_http_status(:success)
      expect(Project.count).to eq(initial_count)
    end
    
    it "does not create project with empty autosave" do
      initial_count = Project.count
      
      post projects_path, params: {
        project: {
          summary: "",
          location: "",
          project_type: ""
        },
        autosave: "true"
      }
      
      # Should return OK but not create project
      expect(response).to have_http_status(:ok)
      expect(Project.count).to eq(initial_count)
    end
    
    it "creates project with meaningful autosave content" do
      initial_count = Project.count
      
      post projects_path, params: {
        project: {
          summary: "Test Project Title"
        },
        autosave: "true"
      }
      
      expect(response).to have_http_status(:ok)
      expect(Project.count).to eq(initial_count + 1)
      
      project = Project.last
      expect(project.summary).to eq("Test Project Title")
      expect(project.user).to eq(user)
      expect(project.project_status).to be_falsey
      
      # Should return JSON with project ID
      json_response = JSON.parse(response.body)
      expect(json_response['project_id']).to eq(project.id)
    end
    
    it "updates existing project with subsequent autosave" do
      # First create project via autosave
      post projects_path, params: {
        project: { summary: "Initial Title" },
        autosave: "true"
      }
      
      project = Project.last
      initial_count = Project.count
      
      # Update existing project
      patch project_path(project), params: {
        project: {
          location: "Bratislava, Slovakia"
        },
        autosave: "true"
      }
      
      expect(response).to have_http_status(:ok)
      expect(Project.count).to eq(initial_count) # No new project
      
      project.reload
      expect(project.location).to eq("Bratislava, Slovakia")
      expect(project.summary).to eq("Initial Title") # Preserved
    end
    
    it "filters out blank values in autosave" do
      post projects_path, params: {
        project: {
          summary: "Real Content",
          location: "",
          project_type: "real_estate",
          category: ""
        },
        autosave: "true"
      }
      
      expect(response).to have_http_status(:ok)
      expect(Project.count).to eq(1)
      
      project = Project.last
      expect(project.summary).to eq("Real Content")
      expect(project.project_type).to eq("real_estate")
      expect(project.location).to be_blank
      expect(project.category).to be_blank
    end
  end
  
  describe "Regular form submission (non-autosave)" do
    it "creates project immediately with complete data" do
      initial_count = Project.count
      
      post projects_path, params: {
        project: {
          summary: "Regular Form Project",
          location: "Test Location",
          project_type: "real_estate",
          category: "homes",
          subcategory: "flat",
          full_description: "Complete description"
        }
      }
      
      expect(Project.count).to eq(initial_count + 1)
      expect(response).to redirect_to(edit_project_path(Project.last))
      
      project = Project.last
      expect(project.summary).to eq("Regular Form Project")
      expect(project.project_status).to be_falsey # Always draft initially
    end
  end
end