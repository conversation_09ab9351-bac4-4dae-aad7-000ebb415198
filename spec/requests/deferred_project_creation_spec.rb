# ABOUTME: Tests for UNL-42 deferred project creation - ensures projects are only created when users make actual changes
# ABOUTME: Verifies that empty form submissions don't create database records and autosave behavior works correctly

require 'rails_helper'

RSpec.describe "Deferred Project Creation (UNL-42)", type: :request do
  let(:user) { create(:user) }
  
  before do
    sign_in user
  end
  
  # Helper method to create valid project params for regular submission
  def valid_project_params
    {
      summary: "Test Project",
      location: "Bratislava, Slovakia",
      project_type: "real_estate",
      category: "homes",
      subcategory: "flat",
      full_description: "A complete project description",
      price_value: "100000",
      price_currency: "EUR"
    }
  end

  describe "GET /projects/new" do
    it "does not create a project in the database" do
      expect {
        get new_project_path
      }.not_to change(Project, :count)
      
      expect(response).to have_http_status(:success)
    end
    
    it "renders the new project form" do
      get new_project_path
      expect(response).to have_http_status(:success)
      expect(response.body).to include("project_form")
    end
  end

  describe "POST /projects with autosave=true (first save)" do
    context "with empty form data" do
      let(:empty_params) {
        {
          project: {
            summary: "",
            location: "",
            project_type: "",
            category: "",
            full_description: ""
          },
          autosave: "true"
        }
      }
      
      it "does not create a project in the database" do
        expect {
          post projects_path, params: empty_params
        }.not_to change(Project, :count)
        
        expect(response).to have_http_status(:ok)
        expect(response.body).to be_blank
      end
    end
    
    context "with meaningful content" do
      let(:valid_params) {
        {
          project: {
            summary: "Test Project Title",
            location: "",
            project_type: "",
            category: ""
          },
          autosave: "true"
        }
      }
      
      it "creates a project when user provides actual content" do
        expect {
          post projects_path, params: valid_params
        }.to change(Project, :count).by(1)
        
        project = Project.last
        expect(project.summary).to eq("Test Project Title")
        expect(project.user).to eq(user)
        expect(project.project_status).to be_falsey # Always created as draft
        
        expect(response).to have_http_status(:ok)
        expect(response.content_type).to include('application/json')
        
        json_response = JSON.parse(response.body)
        expect(json_response['project_id']).to eq(project.id)
      end
      
      it "filters out blank values but saves non-blank ones" do
        params = {
          project: {
            summary: "Real Title",
            location: "",  # This should be filtered out
            project_type: "real_estate",
            category: "",  # This should be filtered out
            full_description: "Real description"
          },
          autosave: "true"
        }
        
        post projects_path, params: params
        
        project = Project.last
        expect(project.summary).to eq("Real Title")
        expect(project.project_type).to eq("real_estate")
        expect(project.full_description).to eq("Real description")
        expect(project.location).to be_blank
        expect(project.category).to be_blank
      end
    end
    
    context "with only whitespace content" do
      let(:whitespace_params) {
        {
          project: {
            summary: "   ",
            location: "\t",
            full_description: "\n"
          },
          autosave: "true"
        }
      }
      
      it "does not create a project" do
        expect {
          post projects_path, params: whitespace_params
        }.not_to change(Project, :count)
        
        expect(response).to have_http_status(:ok)
      end
    end
  end

  describe "PATCH /projects/:id with autosave=true (subsequent saves)" do
    let!(:project) { create(:project, :minimal, user: user, summary: "Original Title") }
    
    it "updates existing project with new content" do
      patch project_path(project), params: {
        project: {
          summary: "Updated Title",
          location: "New Location"
        },
        autosave: "true"
      }
      
      expect(response).to have_http_status(:ok)
      
      project.reload
      expect(project.summary).to eq("Updated Title")
      expect(project.location).to eq("New Location")
    end
    
    it "does not save if no meaningful changes" do
      original_updated_at = project.updated_at
      
      patch project_path(project), params: {
        project: {
          summary: "",
          location: "",
          project_type: ""
        },
        autosave: "true"
      }
      
      expect(response).to have_http_status(:ok)
      
      project.reload
      expect(project.updated_at).to be_within(1.second).of(original_updated_at)
      expect(project.summary).to eq("Original Title") # Unchanged
    end
  end

  describe "POST /projects (regular form submission, non-autosave)" do
    it "creates project immediately for regular form submission" do
      regular_params = { project: valid_project_params }
      
      expect {
        post projects_path, params: regular_params
      }.to change(Project, :count).by(1)
      
      project = Project.last
      expect(project.summary).to eq("Test Project")
      expect(project.project_status).to be_falsey # Always draft initially
      
      expect(response).to redirect_to(edit_project_path(project))
    end
  end

  describe "Edge cases and error scenarios" do
    context "when autosave params are malformed" do
      it "handles missing project params gracefully" do
        expect {
          post projects_path, params: { autosave: "true" }
        }.to raise_error(ActionController::ParameterMissing)
      end
    end
    
    context "with maximum length content" do
      let(:long_content_params) {
        {
          project: {
            summary: "A" * 255,  # Assuming summary has length limit
            full_description: "B" * 5000
          },
          autosave: "true"
        }
      }
      
      it "creates project with long content" do
        expect {
          post projects_path, params: long_content_params
        }.to change(Project, :count).by(1)
        
        project = Project.last
        expect(project.summary.length).to eq(255)
        expect(project.full_description.length).to eq(5000)
      end
    end
  end

  describe "Rails dirty tracking integration" do
    context "with new project" do
      it "correctly identifies changes in new project" do
        params = {
          project: { summary: "New Title" },
          autosave: "true"
        }
        
        # Mock to verify that changed? is being called
        allow_any_instance_of(Project).to receive(:changed?).and_return(true)
        
        post projects_path, params: params
        
        expect(Project.count).to eq(1)
      end
    end
    
    context "with existing project" do
      let!(:project) { create(:project, :minimal, user: user) }
      
      it "only saves when Rails detects actual changes" do
        # Set up project with initial state
        project.update_column(:summary, "Original")
        original_updated_at = project.reload.updated_at
        
        # Send same data - should not trigger save
        patch project_path(project), params: {
          project: { summary: "Original" },
          autosave: "true"
        }
        
        project.reload
        expect(project.updated_at).to be_within(1.second).of(original_updated_at)
      end
    end
  end
end