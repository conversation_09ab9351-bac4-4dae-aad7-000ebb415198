# ABOUTME: Integration test for project notification system to verify bulk notifications
# ABOUTME: Tests both update and update_approval actions to ensure no job queue flooding

require 'rails_helper'

RSpec.describe "Project Notifications", type: :request do
  include ActiveJob::TestHelper
  let(:admin_user) { create(:user, role: 'admin') }
  let(:project_owner) { create(:user) }
  let(:project) do
    create(:project, 
      user: project_owner,
      network_only: false,
      semi_public: true,
      project_status: false,
      first_published_at: nil
    )
  end

  before do
    # Clear deliveries before each test
    ActionMailer::Base.deliveries.clear
    
    # Create some active users who would receive notifications
    # Must have both confirmed_at AND invitation_accepted_at for User.active scope
    5.times do
      user = create(:user, 
        confirmed_at: Time.current, 
        invitation_accepted_at: Time.current
      )
      create(:user_profile, user: user)
    end
    
    sign_in admin_user
  end

  describe "PATCH /projects/:id/update_approval" do
    it "creates only one notification job when approving a semi-public project" do
      # Approve and activate the project
      expect {
        patch update_approval_project_path(project), params: {
          project: { approved: true, project_status: true }
        }
      }.to have_enqueued_job(BulkNotificationJob).on_queue('mailers')
      
      expect(response).to redirect_to(admin_dashboard_path)
      expect(project.reload.first_published_at).not_to be_nil
    end

    it "creates no jobs for network_only projects with no connections" do
      project.update!(network_only: true, semi_public: false)
      
      expect {
        patch update_approval_project_path(project), params: {
          project: { approved: true, project_status: true }
        }
      }.not_to have_enqueued_job(BulkNotificationJob)
      
      # Should still mark as published even with no notifications
      expect(project.reload.first_published_at).not_to be_nil
    end
  end

  describe "PATCH /projects/:id (update action)" do
    before do
      sign_in project_owner
      # Use update_column to bypass validation for test setup
      project.update_column(:approved, true) # Pre-approved
    end

    it "creates only one notification job when activating a project" do
      expect {
        patch project_path(project), params: {
          project: { project_status: true }
        }
      }.to have_enqueued_job(BulkNotificationJob).on_queue('mailers')
      
      expect(project.reload.first_published_at).not_to be_nil
    end

    it "does not create duplicate jobs if project already published" do
      project.update_column(:first_published_at, 1.day.ago)
      
      expect {
        patch project_path(project), params: {
          project: { title: "Updated title" }
        }
      }.not_to have_enqueued_job(BulkNotificationJob)
    end
  end

  describe "Performance under load" do
    it "handles 100 users without creating 100 jobs" do
      # Create many active users  
      100.times do
        user = create(:user, 
          confirmed_at: Time.current, 
          invitation_accepted_at: Time.current
        )
        create(:user_profile, user: user)
      end
      
      # This should create only 1 job, not 100+
      expect {
        patch update_approval_project_path(project), params: {
          project: { approved: true, project_status: true }
        }
      }.to have_enqueued_job(BulkNotificationJob).on_queue('mailers')
    end
  end
end