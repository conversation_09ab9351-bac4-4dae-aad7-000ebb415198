# ABOUTME: Comprehensive tests for WantsController that follow exact patterns as ProjectsController
# ABOUTME: Tests all CRUD operations, authorization, filtering, and special actions like show_my

require 'rails_helper'

RSpec.describe "Wants", type: :request do
  let(:user) { create(:user) }
  let(:other_user) { create(:user) }
  
  before do
    # Ensure all users have complete profiles to avoid redirection
    [user, other_user].each do |u|
      u.user_profile.update!(
        first_name: 'Test',
        last_name: 'User',
        city: 'Bratislava', 
        country: 'Slovakia'
      ) if u.user_profile
    end
    
    sign_in user
  end
  
  describe "GET /wants" do
    let!(:want1) { create(:want, user: user, want_type: :real_estate, category: :commercial_property) }
    let!(:want2) { create(:want, user: other_user, want_type: :business, category: :business_acquisition) }
    
    it "returns successful response" do
      get "/sk/wants"
      expect(response).to be_successful
    end
    
    it "displays all wants" do
      get "/sk/wants"
      expect(response.body).to include(want1.summary)
      expect(response.body).to include(want2.summary)
    end
    
    context "with filtering parameters" do
      it "filters by want_type" do
        get "/sk/wants", params: { want_type: 'real_estate' }
        expect(response).to be_successful
      end
      
      it "filters by category" do
        get "/sk/wants", params: { category: 'commercial_property' }
        expect(response).to be_successful
      end
      
      it "filters by search term" do
        get "/sk/wants", params: { search: 'investment' }
        expect(response).to be_successful
      end
      
      it "filters by location" do
        get "/sk/wants", params: { location: 'Bratislava' }
        expect(response).to be_successful
      end
    end
  end
  
  describe "GET /wants/show_my" do
    let!(:my_want) { create(:want, user: user, summary: "My investment want - looking for commercial properties") }
    let!(:other_want) { create(:want, user: other_user, summary: "Other user investment opportunity - seeking residential property") }
    
    it "returns successful response" do
      get "/sk/wants/show_my"
      expect(response).to be_successful
    end
    
    it "shows only current user's wants" do
      get "/sk/wants/show_my"
      expect(response.body).to include(my_want.summary)
      expect(response.body).not_to include(other_want.summary)
    end
  end
  
  describe "GET /wants/:id" do
    let(:want) { create(:want, user: user) }
    
    it "returns successful response for valid want" do
      get "/sk/wants/#{want.id}"
      expect(response).to be_successful
    end
    
    it "displays want details" do
      get "/sk/wants/#{want.id}"
      expect(response.body).to include(want.summary)
    end
    
    it "redirects for non-existent want" do
      get "/sk/wants/999999"
      expect(response).to redirect_to(wants_path)
    end
  end
  
  describe "GET /wants/new" do
    it "returns successful response" do
      get "/sk/wants/new"
      expect(response).to be_successful
    end
    
    it "displays want form" do
      get "/sk/wants/new"
      expect(response.body).to include('form')
    end
  end
  
  describe "POST /wants" do
    let(:valid_attributes) do
      {
        summary: "Looking for commercial real estate in Bratislava area",
        description: "Detailed description of specific requirements and preferences",
        want_type: "real_estate",
        category: "commercial_property", 
        subcategory: "warehouse",
        place: "Bratislava, Slovakia",
        price_min: 100000,
        price_max: 500000,
        price_currency: "EUR"
      }
    end
    
    let(:invalid_attributes) do
      {
        summary: "short", # Too short
        want_type: "",
        category: "",
        subcategory: ""
      }
    end
    
    context "with valid parameters" do
      it "creates a new want" do
        expect {
          post "/sk/wants", params: { want: valid_attributes }
        }.to change(Want, :count).by(1)
      end
      
      it "redirects to the created want" do
        post "/sk/wants", params: { want: valid_attributes }
        expect(response).to redirect_to(want_path(Want.last))
      end
      
      it "assigns the want to current user" do
        post "/sk/wants", params: { want: valid_attributes }
        expect(Want.last.user).to eq(user)
      end
      
      it "saves the description" do
        post "/sk/wants", params: { want: valid_attributes }
        expect(Want.last.description).to eq("Detailed description of specific requirements and preferences")
      end
    end
    
    context "with invalid parameters" do
      it "does not create a new want" do
        expect {
          post "/sk/wants", params: { want: invalid_attributes }
        }.to change(Want, :count).by(0)
      end
      
      it "returns unprocessable entity status" do
        post "/sk/wants", params: { want: invalid_attributes }
        expect(response).to have_http_status(:unprocessable_entity)
      end
    end
  end
  
  describe "GET /wants/:id/edit" do
    let(:want) { create(:want, user: user) }
    let(:other_want) { create(:want, user: other_user) }
    
    it "returns successful response for own want" do
      get "/sk/wants/#{want.id}/edit"
      expect(response).to be_successful
    end
    
    it "displays edit form" do
      get "/sk/wants/#{want.id}/edit"
      expect(response.body).to include('form')
      expect(response.body).to include(want.summary)
    end
  end
  
  describe "PATCH /wants/:id" do
    let(:want) { create(:want, user: user) }
    let(:other_want) { create(:want, user: other_user) }
    
    let(:new_attributes) do
      {
        summary: "Updated want description with more details about requirements",
        price_min: 200000
      }
    end
    
    context "with valid parameters" do
      it "updates the want" do
        patch "/sk/wants/#{want.id}", params: { want: new_attributes }
        want.reload
        expect(want.summary).to eq("Updated want description with more details about requirements")
        expect(want.price_min).to eq(200000)
      end
      
      it "redirects to the want" do
        patch "/sk/wants/#{want.id}", params: { want: new_attributes }
        expect(response).to redirect_to(want_path(want))
      end
    end
    
    context "with invalid parameters" do
      it "returns unprocessable entity status" do
        patch "/sk/wants/#{want.id}", params: { want: { summary: "short" } }
        expect(response).to have_http_status(:unprocessable_entity)
      end
    end
  end
  
  describe "DELETE /wants/:id" do
    let!(:want) { create(:want, user: user) }
    let!(:other_want) { create(:want, user: other_user) }
    
    it "destroys the want" do
      expect {
        delete "/sk/wants/#{want.id}"
      }.to change(Want, :count).by(-1)
    end
    
    it "redirects to show_my wants" do
      delete "/sk/wants/#{want.id}"
      expect(response).to redirect_to(show_my_wants_path)
    end
  end
  
  describe "authorization" do
    let(:want) { create(:want, user: user) }
    let(:other_want) { create(:want, user: other_user) }
    
    context "when not signed in" do
      before { sign_out user }
      
      it "redirects to sign in for index" do
        get "/sk/wants"
        expect(response).to redirect_to(new_user_session_path)
      end
      
      it "redirects to sign in for show" do
        get "/sk/wants/#{want.id}"
        expect(response).to redirect_to(new_user_session_path)
      end
      
      it "redirects to sign in for new" do
        get "/sk/wants/new"
        expect(response).to redirect_to(new_user_session_path)
      end
    end
  end
end