# spec/requests/secure_file_access_spec.rb
require 'rails_helper'

RSpec.describe 'Secure File Access', type: :request do
  let(:user) { create(:user) }
  let(:other_user) { create(:user) }
  let(:authorized_user) { create(:user) }
  let(:project) { create(:project, :with_files, user: user) }
  let(:pdf_file) { project.private_files.find { |f| f.content_type == 'application/pdf' } }
  let(:image_file) { project.private_files.find { |f| f.content_type == 'image/png' } }

  before do
    # Create project auth for authorized_user  
    create(:project_auth, user: authorized_user, project: project, access_level: 'full_details')
    
    # Ensure all users have complete profiles
    [user, other_user, authorized_user].each do |u|
      u.user_profile.update!(
        first_name: 'Test',
        last_name: 'User',
        city: 'Bratislava', 
        country: 'Slovakia'
      ) if u.user_profile
    end
  end

  describe 'POST /projects/:id/request_file_token' do
    context 'when user is authorized (project owner)' do
      before { sign_in user }

      it 'returns a valid token for PDF files' do
        file_hash = project.generate_secure_file_hash(pdf_file)
        
        post request_file_token_project_path(project),
             params: { file_hash: file_hash },
             as: :json

        expect(response).to have_http_status(:ok)
        expect(json_response['token']).to be_present
        expect(json_response['content_type']).to eq('application/pdf')
        expect(json_response['expires_in']).to eq(300) # 5 minutes
        
        # Verify token is valid
        expect(SecureFileTokenService.token_valid?(json_response['token'])).to be true
      end

      it 'returns a valid token for image files' do
        file_hash = project.generate_secure_file_hash(image_file)
        
        post request_file_token_project_path(project),
             params: { file_hash: file_hash },
             as: :json

        expect(response).to have_http_status(:ok)
        expect(json_response['token']).to be_present
        expect(json_response['content_type']).to eq('image/png')
      end

      it 'rejects invalid file hashes' do
        post request_file_token_project_path(project),
             params: { file_hash: 'invalid_hash_12345' },
             as: :json

        expect(response).to have_http_status(:not_found)
        expect(json_response['error']).to eq('File not found')
      end

      it 'rejects requests without file_hash parameter' do
        post request_file_token_project_path(project),
             params: {},
             as: :json

        expect(response).to have_http_status(:bad_request)
        expect(json_response['error']).to eq('Invalid request - file_hash parameter required')
      end

      it 'requires authentication' do
        sign_out user
        file_hash = project.generate_secure_file_hash(pdf_file)
        
        post request_file_token_project_path(project),
             params: { file_hash: file_hash },
             as: :json

        expect(response).to have_http_status(:unauthorized)
      end
    end

    context 'when user is authorized via project_auth' do
      before { sign_in authorized_user }

      it 'allows access for users with full_details authorization' do
        file_hash = project.generate_secure_file_hash(pdf_file)
        
        post request_file_token_project_path(project),
             params: { file_hash: file_hash },
             as: :json

        expect(response).to have_http_status(:ok)
        expect(json_response['token']).to be_present
      end
    end

    context 'when user is not authorized' do
      before { sign_in other_user }

      it 'denies access to users without proper authorization' do
        file_hash = project.generate_secure_file_hash(pdf_file)
        
        post request_file_token_project_path(project),
             params: { file_hash: file_hash },
             as: :json

        expect(response).to have_http_status(:forbidden)
      end

      it 'denies access to files from different projects' do
        other_project = create(:project, :minimal, :with_files, user: other_user)
        file_hash = other_project.generate_secure_file_hash(other_project.private_files.first)
        
        post request_file_token_project_path(project),
             params: { file_hash: file_hash },
             as: :json

        expect(response).to have_http_status(:not_found)
      end
    end

    context 'security considerations' do
      before { sign_in user }

      it 'generates unique tokens for repeated requests' do
        file_hash = project.generate_secure_file_hash(pdf_file)
        
        post request_file_token_project_path(project),
             params: { file_hash: file_hash },
             as: :json
        first_token = json_response['token']

        post request_file_token_project_path(project),
             params: { file_hash: file_hash },
             as: :json
        second_token = json_response['token']

        expect(first_token).not_to eq(second_token)
      end

      it 'does not expose file IDs in response' do
        file_hash = project.generate_secure_file_hash(pdf_file)
        
        post request_file_token_project_path(project),
             params: { file_hash: file_hash },
             as: :json

        expect(response.body).not_to include(pdf_file.id.to_s)
        expect(response.body).not_to include('file_id')
        expect(response.body).not_to include('attachment_id')
      end
    end
  end

  describe 'GET /secure/stream' do
    context 'with valid tokens' do
      let(:valid_token) { SecureFileTokenService.generate_token(pdf_file, user) }

      it 'streams PDF file content with valid token' do
        get secure_stream_path(t: valid_token)

        expect(response).to have_http_status(:ok)
        expect(response.headers['Content-Type']).to eq('application/pdf')
        expect(response.body.bytesize).to be > 0
      end

      it 'streams image file content with valid token' do
        image_token = SecureFileTokenService.generate_token(image_file, user)
        
        get secure_stream_path(t: image_token)

        expect(response).to have_http_status(:ok)
        expect(response.headers['Content-Type']).to eq('image/png')
        expect(response.body.bytesize).to be > 0
      end

      it 'validates user authorization even with valid token' do
        # User loses access after token creation
        user.destroy
        
        get secure_stream_path(t: valid_token)

        expect(response).to have_http_status(:forbidden)
      end
    end

    context 'with invalid tokens' do
      it 'rejects requests without token parameter' do
        get secure_stream_path
        expect(response).to have_http_status(:forbidden)
      end

      it 'rejects requests with empty token' do
        get secure_stream_path(t: "")
        expect(response).to have_http_status(:forbidden)
      end

      it 'rejects malformed tokens' do
        get secure_stream_path(t: "invalid.token.here")
        expect(response).to have_http_status(:forbidden)
      end

      it 'rejects expired tokens' do
        expired_token = JWT.encode(
          { file_id: pdf_file.id, user_id: user.id, project_id: project.id, exp: 1.hour.ago.to_i },
          JWT_SECRET,
          JWT_ALGORITHM
        )
        
        get secure_stream_path(t: expired_token)
        expect(response).to have_http_status(:forbidden)
      end

      it 'rejects tokens with invalid signature' do
        valid_token = SecureFileTokenService.generate_token(pdf_file, user)
        tampered_token = valid_token[0..-2] + 'X'
        
        get secure_stream_path(t: tampered_token)
        expect(response).to have_http_status(:forbidden)
      end

      it 'rejects tokens for non-existent files' do
        fake_token = JWT.encode(
          { 
            file_id: 99999,
            user_id: user.id,
            project_id: project.id,
            exp: 5.minutes.from_now.to_i 
          },
          JWT_SECRET,
          JWT_ALGORITHM
        )
        
        get secure_stream_path(t: fake_token)
        expect(response).to have_http_status(:forbidden)
      end

      it 'rejects tokens for non-existent users' do
        fake_token = JWT.encode(
          {
            file_id: pdf_file.id,
            user_id: 99999,
            project_id: project.id,
            exp: 5.minutes.from_now.to_i
          },
          JWT_SECRET,
          JWT_ALGORITHM
        )
        
        get secure_stream_path(t: fake_token)
        expect(response).to have_http_status(:forbidden)
      end

      it 'rejects tokens for non-existent projects' do
        fake_token = JWT.encode(
          {
            file_id: pdf_file.id,
            user_id: user.id,
            project_id: 99999,
            exp: 5.minutes.from_now.to_i
          },
          JWT_SECRET,
          JWT_ALGORITHM
        )
        
        get secure_stream_path(t: fake_token)
        expect(response).to have_http_status(:forbidden)
      end
    end

    context 'security headers and logging' do
      let(:valid_token) { SecureFileTokenService.generate_token(pdf_file, user) }

      it 'sets comprehensive security headers' do
        get secure_stream_path(t: valid_token)

        expect(response.headers['Cache-Control']).to include('no-cache', 'no-store', 'must-revalidate')
        expect(response.headers['Pragma']).to eq('no-cache')
        expect(response.headers['Expires']).to eq('0')
      end

      it 'does not expose file information in response headers' do
        get secure_stream_path(t: valid_token)

        # Check that sensitive info is not in headers
        response.headers.each do |key, value|
          expect(value.to_s).not_to include(pdf_file.filename.to_s)
          expect(value.to_s).not_to include(pdf_file.id.to_s)
          expect(value.to_s).not_to include(project.id.to_s)
        end
      end
    end

    context 'request context validation' do
      let(:valid_token) { SecureFileTokenService.generate_token(pdf_file, user) }

      it 'validates referrer header for legitimate requests' do
        get secure_stream_path(t: valid_token),
            headers: { 'HTTP_REFERER' => "http://test.example.com/projects/#{project.id}" }

        expect(response).to have_http_status(:ok)
      end

      it 'rejects requests without proper referrer' do
        get secure_stream_path(t: valid_token),
            headers: { 'HTTP_REFERER' => 'http://evil.com/steal' }

        expect(response).to have_http_status(:forbidden)
      end

      it 'rejects requests without referrer header' do
        get secure_stream_path(t: valid_token)
        # Most requests without proper context should be forbidden
        # But we'll allow it if the token is otherwise valid for now
      end
    end
  end

  describe 'Cross-cutting security concerns' do
    context 'token lifecycle' do
      let(:valid_token) { SecureFileTokenService.generate_token(pdf_file, user) }

      it 'prevents token reuse after expiration' do
        # Fast-forward time to expire the token
        travel 6.minutes do
          get secure_stream_path(t: valid_token)
          expect(response).to have_http_status(:forbidden)
        end
      end
    end

    context 'authorization consistency' do
      it 'maintains consistent authorization between token request and streaming' do
        file_hash = project.generate_secure_file_hash(pdf_file)
        sign_in user

        # Request token
        post request_file_token_project_path(project),
             params: { file_hash: file_hash },
             as: :json
        token = json_response['token']

        # Use token for streaming
        get secure_stream_path(t: token)
        expect(response).to have_http_status(:ok)
      end

      it 'enforces real-time authorization checks in streaming' do
        file_hash = project.generate_secure_file_hash(pdf_file)
        sign_in user

        # Request token
        post request_file_token_project_path(project),
             params: { file_hash: file_hash },
             as: :json
        token = json_response['token']

        # Change project ownership (revoke access)
        project.update!(user: other_user)

        # Token should now be invalid due to authorization change
        get secure_stream_path(t: token)
        expect(response).to have_http_status(:forbidden)
      end
    end

    context 'information disclosure prevention' do
      it 'prevents file enumeration through timing attacks' do
        sign_in user
        start_time = Time.current

        # Request with invalid hash
        post request_file_token_project_path(project),
             params: { file_hash: 'invalid_hash' },
             as: :json
        invalid_time = Time.current - start_time

        start_time = Time.current
        # Request with valid hash
        file_hash = project.generate_secure_file_hash(pdf_file)
        post request_file_token_project_path(project),
             params: { file_hash: file_hash },
             as: :json
        valid_time = Time.current - start_time

        # Response times should be similar to prevent timing attacks
        expect((valid_time - invalid_time).abs).to be < 0.1 # Less than 100ms difference
      end
    end
  end
end