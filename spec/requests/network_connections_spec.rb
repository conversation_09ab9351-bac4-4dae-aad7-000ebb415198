require 'rails_helper'

# This spec was generated by rspec-rails when you ran the scaffold generator.
# It demonstrates how one might use RSpec to test the controller code that
# was generated by <PERSON>s when you ran the scaffold generator.
#
# It assumes that the implementation code is generated by the rails scaffold
# generator. If you are using any extension libraries to generate different
# controller code, this generated spec may or may not pass.
#
# It only uses APIs available in rails and/or rspec-rails. There are a number
# of tools you can use to make these specs even more expressive, but we're
# sticking to rails and rspec-rails APIs to keep things simple and stable.

RSpec.describe "/network_connections", type: :request do
  
  # This should return the minimal set of attributes required to create a valid
  # NetworkConnection. As you add validations to NetworkConnection, be sure to
  # adjust the attributes here as well.
  let(:valid_attributes) {
    skip("Add a hash of attributes valid for your model")
  }

  let(:invalid_attributes) {
    skip("Add a hash of attributes invalid for your model")
  }

  describe "GET /index" do
    it "renders a successful response" do
      NetworkConnection.create! valid_attributes
      get network_connections_url
      expect(response).to be_successful
    end
  end

  describe "GET /show" do
    it "renders a successful response" do
      network_connection = NetworkConnection.create! valid_attributes
      get network_connection_url(network_connection)
      expect(response).to be_successful
    end
  end

  describe "GET /new" do
    it "renders a successful response" do
      get new_network_connection_url
      expect(response).to be_successful
    end
  end

  describe "GET /edit" do
    it "renders a successful response" do
      network_connection = NetworkConnection.create! valid_attributes
      get edit_network_connection_url(network_connection)
      expect(response).to be_successful
    end
  end

  describe "POST /create" do
    context "with valid parameters" do
      it "creates a new NetworkConnection" do
        expect {
          post network_connections_url, params: { network_connection: valid_attributes }
        }.to change(NetworkConnection, :count).by(1)
      end

      it "redirects to the created network_connection" do
        post network_connections_url, params: { network_connection: valid_attributes }
        expect(response).to redirect_to(network_connection_url(NetworkConnection.last))
      end
    end

    context "with invalid parameters" do
      it "does not create a new NetworkConnection" do
        expect {
          post network_connections_url, params: { network_connection: invalid_attributes }
        }.to change(NetworkConnection, :count).by(0)
      end

      it "renders a response with 422 status (i.e. to display the 'new' template)" do
        post network_connections_url, params: { network_connection: invalid_attributes }
        expect(response).to have_http_status(:unprocessable_entity)
      end
    end
  end

  describe "PATCH /update" do
    context "with valid parameters" do
      let(:new_attributes) {
        skip("Add a hash of attributes valid for your model")
      }

      it "updates the requested network_connection" do
        network_connection = NetworkConnection.create! valid_attributes
        patch network_connection_url(network_connection), params: { network_connection: new_attributes }
        network_connection.reload
        skip("Add assertions for updated state")
      end

      it "redirects to the network_connection" do
        network_connection = NetworkConnection.create! valid_attributes
        patch network_connection_url(network_connection), params: { network_connection: new_attributes }
        network_connection.reload
        expect(response).to redirect_to(network_connection_url(network_connection))
      end
    end

    context "with invalid parameters" do
      it "renders a response with 422 status (i.e. to display the 'edit' template)" do
        network_connection = NetworkConnection.create! valid_attributes
        patch network_connection_url(network_connection), params: { network_connection: invalid_attributes }
        expect(response).to have_http_status(:unprocessable_entity)
      end
    end
  end

  describe "DELETE /destroy" do
    it "destroys the requested network_connection" do
      network_connection = NetworkConnection.create! valid_attributes
      expect {
        delete network_connection_url(network_connection)
      }.to change(NetworkConnection, :count).by(-1)
    end

    it "redirects to the network_connections list" do
      network_connection = NetworkConnection.create! valid_attributes
      delete network_connection_url(network_connection)
      expect(response).to redirect_to(network_connections_url)
    end
  end
end
