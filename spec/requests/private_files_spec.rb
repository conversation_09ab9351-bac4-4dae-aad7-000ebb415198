require 'rails_helper'

RSpec.describe "PrivateFiles", type: :request do
  let(:user) { create(:user) }
  let(:other_user) { create(:user) }
  let(:project) { create(:project, user: user) }
  let(:other_project) { create(:project, user: other_user) }
  let(:test_file) { fixture_file_upload('spec/fixtures/files/test.pdf', 'application/pdf') }
  let(:image_file) { fixture_file_upload('spec/fixtures/files/test_image.png', 'image/png') }

  before do
    project.files.attach(test_file)
  end

  describe "POST /projects/:id/request_file_token" do
    let(:file_attachment) { project.files.first }
    let(:file_hash) { project.generate_secure_file_hash(file_attachment) }
    let(:valid_params) { { file_hash: file_hash } }

    context "authorization" do
      it "returns 401 Unauthorized for unauthenticated requests" do
        post "/projects/#{project.id}/request_file_token", params: valid_params
        
        expect(response).to have_http_status(:unauthorized)
      end

      it "returns 404 Not Found for unauthorized users (prevents enumeration)" do
        sign_in other_user
        
        post "/projects/#{project.id}/request_file_token", params: valid_params
        
        expect(response).to have_http_status(:not_found)
      end
    end

    context "input validation" do
      before { sign_in user }

      it "returns 400 Bad Request when file_hash parameter is missing" do
        post "/projects/#{project.id}/request_file_token", params: {}
        
        expect(response).to have_http_status(:bad_request)
        expect(JSON.parse(response.body)['error']).to include('file_hash')
      end

      it "returns 404 Not Found when file_hash doesn't exist for project (prevents enumeration)" do
        invalid_hash = "nonexistent" + "a" * 32
        
        post "/projects/#{project.id}/request_file_token", params: { file_hash: invalid_hash }
        
        expect(response).to have_http_status(:not_found)
      end

      it "returns 400 Bad Request for malformed file_hash (wrong format/length)" do
        malformed_hashes = [
          "short",
          "toolongofhashstringthatexceedsnormallength" * 3,
          "contains-invalid-chars!@#$%^&*()"
        ]
        
        malformed_hashes.each do |bad_hash|
          post "/projects/#{project.id}/request_file_token", params: { file_hash: bad_hash }
          
          expect(response).to have_http_status(:bad_request), "Expected 400 for hash: #{bad_hash}"
        end
      end

      it "returns 400 Bad Request for empty file_hash" do
        post "/projects/#{project.id}/request_file_token", params: { file_hash: "" }
        
        expect(response).to have_http_status(:bad_request)
      end
    end

    context "success cases" do
      before { sign_in user }

      it "returns 200 OK with valid JSON payload for authorized user" do
        post "/projects/#{project.id}/request_file_token", params: valid_params
        
        expect(response).to have_http_status(:ok)
        
        json_response = JSON.parse(response.body)
        expect(json_response).to have_key('token')
        expect(json_response).to have_key('content_type')
        expect(json_response).to have_key('expires_in')
        
        expect(json_response['token']).to be_present
        expect(json_response['content_type']).to eq('application/pdf')
        expect(json_response['expires_in']).to be_a(Integer)
      end

      it "defaults content_type to application/octet-stream for files with missing content_type" do
        # Simulate missing content_type
        allow_any_instance_of(ActiveStorage::Blob).to receive(:content_type).and_return(nil)
        
        post "/projects/#{project.id}/request_file_token", params: valid_params
        
        expect(response).to have_http_status(:ok)
        
        json_response = JSON.parse(response.body)
        expect(json_response['content_type']).to eq('application/octet-stream')
      end
    end

    context "security (rate limiting)" do
      before { sign_in user }

      it "eventually returns 429 Too Many Requests for burst of requests" do
        # Simulate burst of requests (this would need to be configured in rack-attack)
        # The exact number depends on rate limiting configuration
        
        # Make multiple rapid requests
        31.times do
          post "/projects/#{project.id}/request_file_token", params: valid_params
        end
        
        # Should eventually get rate limited
        expect(response).to have_http_status(:too_many_requests)
      end

      it "blocks subsequent requests during cooldown period after rate limit" do
        # Trigger rate limit
        31.times do
          post "/projects/#{project.id}/request_file_token", params: valid_params
        end
        
        expect(response).to have_http_status(:too_many_requests)
        
        # Subsequent request should still be blocked
        post "/projects/#{project.id}/request_file_token", params: valid_params
        expect(response).to have_http_status(:too_many_requests)
      end
    end
  end

  describe "GET /secure/stream" do
    let(:file_attachment) { project.files.first }
    let(:file_hash) { project.generate_secure_file_hash(file_attachment) }
    let(:valid_token) { SecureFileTokenService.generate_token(project.id, file_hash, user.id) }
    let(:expired_token) { SecureFileTokenService.generate_token(project.id, file_hash, user.id, expires_in: -1.minute) }

    context "token handling" do
      it "returns 403 Forbidden when no token provided" do
        get "/secure/stream"
        
        expect(response).to have_http_status(:forbidden)
      end

      it "returns 403 Forbidden for expired token" do
        get "/secure/stream", params: { t: expired_token }
        
        expect(response).to have_http_status(:forbidden)
      end

      it "returns 403 Forbidden for invalid/malformed token" do
        invalid_tokens = [
          "invalid.token.here",
          "short",
          "",
          "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.invalid.signature"
        ]
        
        invalid_tokens.each do |bad_token|
          get "/secure/stream", params: { t: bad_token }
          
          expect(response).to have_http_status(:forbidden), "Expected 403 for token: #{bad_token}"
        end
      end

      it "returns 403 Forbidden for token replay attack (single-use tokens)" do
        # First request should succeed
        get "/secure/stream", params: { t: valid_token }, headers: { 'Referer' => "http://example.com/projects/#{project.id}" }
        expect(response).to have_http_status(:ok)
        
        # Second request with same token should fail
        get "/secure/stream", params: { t: valid_token }, headers: { 'Referer' => "http://example.com/projects/#{project.id}" }
        expect(response).to have_http_status(:forbidden)
      end
    end

    context "context and re-authorization" do
      it "returns 403 Forbidden when Referer header is missing" do
        get "/secure/stream", params: { t: valid_token }
        
        expect(response).to have_http_status(:forbidden)
      end

      it "returns 403 Forbidden when Referer header is from wrong domain" do
        get "/secure/stream", params: { t: valid_token }, headers: { 'Referer' => "http://malicious-site.com/attack" }
        
        expect(response).to have_http_status(:forbidden)
      end

      it "returns 403 Forbidden when user permissions revoked after token issued" do
        # Change project ownership to revoke access
        project.update!(user: other_user)
        
        get "/secure/stream", params: { t: valid_token }, headers: { 'Referer' => "http://example.com/projects/#{project.id}" }
        
        expect(response).to have_http_status(:forbidden)
      end

      it "returns 404 Not Found when project deleted after token issued" do
        project_id = project.id
        project.destroy!
        
        get "/secure/stream", params: { t: valid_token }, headers: { 'Referer' => "http://example.com/projects/#{project_id}" }
        
        expect(response).to have_http_status(:not_found)
      end

      it "returns 404 Not Found when file deleted after token issued" do
        project.files.purge
        
        get "/secure/stream", params: { t: valid_token }, headers: { 'Referer' => "http://example.com/projects/#{project.id}" }
        
        expect(response).to have_http_status(:not_found)
      end
    end

    context "success cases" do
      let(:valid_headers) { { 'Referer' => "http://example.com/projects/#{project.id}" } }

      it "returns 200 OK and streams file content with proper headers" do
        get "/secure/stream", params: { t: valid_token }, headers: valid_headers
        
        expect(response).to have_http_status(:ok)
        
        # Verify security headers
        expect(response.headers['Content-Type']).to eq('application/pdf')
        expect(response.headers['Cache-Control']).to include('no-cache', 'no-store', 'must-revalidate')
        expect(response.headers['X-Content-Type-Options']).to eq('nosniff')
        expect(response.headers['X-Frame-Options']).to eq('DENY')
        expect(response.headers['Content-Disposition']).to be_present
        
        # Verify file content is streamed
        expect(response.body).to be_present
        expect(response.body.length).to be > 0
      end

      it "sets appropriate Content-Disposition header for downloads" do
        get "/secure/stream", params: { t: valid_token }, headers: valid_headers
        
        expect(response.headers['Content-Disposition']).to match(/attachment/)
      end
    end

    context "error handling and upstream failures" do
      let(:valid_headers) { { 'Referer' => "http://example.com/projects/#{project.id}" } }

      it "returns 503 Service Unavailable when underlying storage fails" do
        # Simulate S3/storage failure
        allow_any_instance_of(ActiveStorage::Blob).to receive(:download).and_raise(ActiveStorage::FileNotFoundError)
        
        get "/secure/stream", params: { t: valid_token }, headers: valid_headers
        
        expect(response).to have_http_status(:service_unavailable)
      end

      it "returns 422 Unprocessable Entity for corrupted or zero-byte file" do
        # Simulate corrupted file
        allow_any_instance_of(ActiveStorage::Blob).to receive(:byte_size).and_return(0)
        
        get "/secure/stream", params: { t: valid_token }, headers: valid_headers
        
        expect(response).to have_http_status(:unprocessable_entity)
        expect(JSON.parse(response.body)['error']).to include('corrupted')
      end
    end

    context "DoS protection" do
      let(:valid_headers) { { 'Referer' => "http://example.com/projects/#{project.id}" } }

      it "streams large files efficiently without loading into memory" do
        # This is more of an implementation test - verify it completes quickly
        start_time = Time.current
        
        get "/secure/stream", params: { t: valid_token }, headers: valid_headers
        
        end_time = Time.current
        
        expect(response).to have_http_status(:ok)
        expect(end_time - start_time).to be < 2.seconds
      end

      it "applies rate limiting to multiple concurrent streaming requests" do
        # Make multiple rapid streaming requests
        threads = []
        
        5.times do |i|
          threads << Thread.new do
            token = SecureFileTokenService.generate_token(project.id, file_hash, user.id)
            get "/secure/stream", params: { t: token }, headers: valid_headers
          end
        end
        
        threads.each(&:join)
        
        # Should eventually get rate limited (implementation specific)
        # This test verifies the system can handle concurrent requests
      end
    end
  end

  describe "token security and lifecycle" do
    let(:file_attachment) { project.files.first }
    let(:file_hash) { project.generate_secure_file_hash(file_attachment) }

    it "generates different tokens for multiple requests for same file" do
      sign_in user
      
      post "/projects/#{project.id}/request_file_token", params: { file_hash: file_hash }
      token1 = JSON.parse(response.body)['token']
      
      post "/projects/#{project.id}/request_file_token", params: { file_hash: file_hash }
      token2 = JSON.parse(response.body)['token']
      
      expect(token1).not_to eq(token2)
    end

    it "validates token scope to specific file and project" do
      # Generate token for specific file/project
      token = SecureFileTokenService.generate_token(project.id, file_hash, user.id)
      
      # Token should only work for that exact file on that exact project
      get "/secure/stream", params: { t: token }, headers: { 'Referer' => "http://example.com/projects/#{project.id}" }
      expect(response).to have_http_status(:ok)
      
      # Token should not work for different project
      other_project.files.attach(test_file)
      other_file_hash = other_project.generate_secure_file_hash(other_project.files.first)
      
      get "/secure/stream", params: { t: token }, headers: { 'Referer' => "http://example.com/projects/#{other_project.id}" }
      expect(response).to have_http_status(:forbidden)
    end

    it "completes current stream but rejects new requests when token expires during stream" do
      # Generate token with very short expiration
      short_token = SecureFileTokenService.generate_token(project.id, file_hash, user.id, expires_in: 1.second)
      
      # First request should work
      get "/secure/stream", params: { t: short_token }, headers: { 'Referer' => "http://example.com/projects/#{project.id}" }
      expect(response).to have_http_status(:ok)
      
      # Wait for expiration
      sleep(2)
      
      # New request with expired token should fail
      new_token = SecureFileTokenService.generate_token(project.id, file_hash, user.id, expires_in: -1.second)
      get "/secure/stream", params: { t: new_token }, headers: { 'Referer' => "http://example.com/projects/#{project.id}" }
      expect(response).to have_http_status(:forbidden)
    end
  end
end