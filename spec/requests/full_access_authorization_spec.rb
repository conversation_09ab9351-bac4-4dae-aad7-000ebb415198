require 'rails_helper'

RSpec.describe "Full Access Authorization", type: :request do
  let(:owner) { create(:user) }
  let(:connected_user) { create(:user) }
  let(:unconnected_user) { create(:user) }
  
  before do
    # Create network connection between owner and connected_user
    create(:network_connection, inviter: owner, invitee: connected_user)
  end

  describe "GET /projects/:id (project show page)" do
    context "with full_access + network_only project" do
      let(:project) do
        create(:project, 
          user: owner,
          summary: "Full Access Network Project",
          full_description: "Full project description",
          full_access: true, 
          network_only: true,
          summary_only: false,
          semi_public: false,
          approved: true
        )
      end

      context "when user is project owner" do
        before { sign_in owner }

        it "allows access" do
          get project_path(project)
          expect(response).to have_http_status(:ok)
          expect(response.body).to include(project.summary)
        end
      end

      context "when user is connected to owner" do
        before { sign_in connected_user }

        it "allows access automatically (no ProjectAuth needed)" do
          get project_path(project)
          expect(response).to have_http_status(:ok)
          expect(response.body).to include(project.summary)
          expect(response.body).to include("Full project description")
        end
      end

      context "when user is not connected to owner" do
        before { sign_in unconnected_user }

        it "denies access with 403 Forbidden" do
          expect {
            get project_path(project)
          }.to raise_error(ActionPolicy::Unauthorized)
        end
      end

      context "when user is not authenticated (guest)" do
        it "redirects to sign in page" do
          get project_path(project)
          expect(response).to redirect_to(new_user_session_path)
        end
      end
    end

    context "with full_access + semi_public project" do
      let(:project) do
        create(:project, 
          user: owner,
          summary: "Full Access Public Project",
          full_description: "Public project description",
          full_access: true, 
          semi_public: true,
          summary_only: false,
          network_only: false,
          approved: true
        )
      end

      context "when user is connected to owner" do
        before { sign_in connected_user }

        it "allows access" do
          get project_path(project)
          expect(response).to have_http_status(:ok)
          expect(response.body).to include("Public project description")
        end
      end

      context "when user is not connected to owner" do
        before { sign_in unconnected_user }

        it "allows access (public access)" do
          get project_path(project)
          expect(response).to have_http_status(:ok)
          expect(response.body).to include("Public project description")
        end
      end

      context "when user is not authenticated (guest)" do
        it "redirects to sign in page (no guest access)" do
          get project_path(project)
          expect(response).to redirect_to(new_user_session_path)
        end
      end
    end

    context "with summary_only project (traditional behavior)" do
      let(:project) do
        create(:project, 
          user: owner,
          summary: "Summary Only Project",
          full_description: "This should not be visible",
          summary_only: true, 
          network_only: true,
          full_access: false,
          semi_public: false,
          approved: true
        )
      end

      context "when user is connected to owner" do
        before { sign_in connected_user }

        it "denies access (requires explicit ProjectAuth)" do
          expect {
            get project_path(project)
          }.to raise_error(ActionPolicy::Unauthorized)
        end
      end

      context "when user has explicit ProjectAuth" do
        before do
          sign_in connected_user
          create(:project_auth, project: project, user: connected_user, access_level: 'full_details')
        end

        it "allows access" do
          get project_path(project)
          expect(response).to have_http_status(:ok)
          expect(response.body).to include("This should not be visible")
        end
      end
    end

    context "with unapproved projects (approved: false)" do
      let(:project) do
        create(:project, 
          user: owner,
          summary: "Unapproved Full Access Project",
          full_description: "This should not be visible to anyone except owner",
          full_access: true, 
          semi_public: true, # Most permissive settings
          summary_only: false,
          network_only: false,
          approved: false    # But not approved
        )
      end

      context "when user is project owner" do
        before { sign_in owner }

        it "allows access (owners can see their own unapproved projects)" do
          get project_path(project)
          expect(response).to have_http_status(:ok)
          expect(response.body).to include("This should not be visible to anyone except owner")
        end
      end

      context "when user is connected to owner" do
        before { sign_in connected_user }

        it "denies access" do
          expect {
            get project_path(project)
          }.to raise_error(ActionPolicy::Unauthorized)
        end
      end

      context "when user is not connected to owner" do
        before { sign_in unconnected_user }

        it "denies access" do
          expect {
            get project_path(project)
          }.to raise_error(ActionPolicy::Unauthorized)
        end
      end

      context "when user has explicit ProjectAuth but project is unapproved" do
        before do
          sign_in connected_user
          create(:project_auth, project: project, user: connected_user, access_level: 'full_details')
        end

        it "denies access (approval overrides ProjectAuth)" do
          expect {
            get project_path(project)
          }.to raise_error(ActionPolicy::Unauthorized)
        end
      end

      context "when user is not authenticated (guest)" do
        it "redirects to sign in page" do
          get project_path(project)
          expect(response).to redirect_to(new_user_session_path)
        end
      end
    end
  end

  describe "GET /projects (project index page)" do
    let!(:full_access_network_project) do
      create(:project, 
        user: owner,
        summary: "Full Access Network Project",
        full_access: true, 
        network_only: true,
        summary_only: false,
        semi_public: false,
        approved: true
      )
    end

    let!(:full_access_public_project) do
      create(:project, 
        user: owner,
        summary: "Full Access Public Project",
        full_access: true, 
        semi_public: true,
        summary_only: false,
        network_only: false,
        approved: true
      )
    end

    let!(:summary_only_project) do
      create(:project, 
        user: owner,
        summary: "Summary Only Project",
        summary_only: true, 
        network_only: true,
        full_access: false,
        semi_public: false,
        approved: true
      )
    end

    context "when user is connected to project owners" do
      before { sign_in connected_user }

      it "shows correct access indicators for each project type" do
        get projects_path
        expect(response).to have_http_status(:ok)
        
        # Full access network project - should show View link
        expect(response.body).to include("Full Access Network Project")
        
        # Full access public project - should show View link  
        expect(response.body).to include("Full Access Public Project")
        
        # Summary only project - should show Request Access link
        expect(response.body).to include("Summary Only Project")
      end
    end

    context "when user is not connected to project owners" do
      before { sign_in unconnected_user }

      it "shows correct access indicators based on project visibility" do
        get projects_path
        expect(response).to have_http_status(:ok)
        
        # Full access public project should be visible with View link
        expect(response.body).to include("Full Access Public Project")
        
        # Network-only projects may not be visible or show Request Access
        # This depends on the index scoping logic
      end
    end
  end

  describe "File access integration" do
    let(:project) do
      create(:project, 
        user: owner,
        full_access: true, 
        network_only: true,
        summary_only: false,
        semi_public: false,
        approved: true
      )
    end

    before do
      # Attach a test file to the project
      project.private_files.attach(
        io: File.open(Rails.root.join('spec', 'fixtures', 'files', 'test.pdf')),
        filename: 'test.pdf',
        content_type: 'application/pdf'
      )
    end

    context "when connected user tries to access project files" do
      before { sign_in connected_user }

      it "allows file access through the secure file system" do
        file = project.private_files.first
        file_hash = project.generate_secure_file_hash(file)
        
        get "/projects/#{project.id}/files/#{file.id}/inline"
        expect(response).to have_http_status(:ok)
      end
    end

    context "when unconnected user tries to access project files" do
      before { sign_in unconnected_user }

      it "denies file access" do
        file = project.private_files.first
        
        expect {
          get "/projects/#{project.id}/files/#{file.id}/inline"
        }.to raise_error(ActionPolicy::Unauthorized)
      end
    end
  end

  describe "Edge cases and security" do
    let(:project) do
      create(:project, 
        user: owner,
        full_access: true, 
        network_only: true,
        summary_only: false,
        semi_public: false,
        approved: true
      )
    end

    context "when user authentication changes during session" do
      it "re-evaluates permissions correctly" do
        sign_in unconnected_user
        
        expect {
          get project_path(project)
        }.to raise_error(ActionPolicy::Unauthorized)
        
        # Simulate user getting connected (in real app this would be through invitation)
        create(:network_connection, inviter: owner, invitee: unconnected_user)
        
        # Should now have access (would require session refresh in real app)
        policy = ProjectPolicy.new(unconnected_user, project)
        expect(policy.view_full_details?).to be true
      end
    end

    context "when project settings change after user loads page" do
      before { sign_in connected_user }

      it "respects current project settings" do
        # Initially has access
        get project_path(project)
        expect(response).to have_http_status(:ok)
        
        # Project changes to summary_only
        project.update!(full_access: false, summary_only: true)
        
        # Should now be denied access
        expect {
          get project_path(project)
        }.to raise_error(ActionPolicy::Unauthorized)
      end
    end

    context "with invalid project sharing combinations" do
      it "prevents creation of projects with invalid settings" do
        invalid_project = build(:project,
          user: owner,
          full_access: true,
          summary_only: true,  # Both detail levels true - invalid
          network_only: true,
          semi_public: false
        )
        
        expect(invalid_project).not_to be_valid
        expect(invalid_project.errors[:base]).to include("Choose either 'Title Only' or 'Everything' for sharing level")
      end
    end
  end

  private

  def sign_in(user)
    post user_session_path, params: {
      user: {
        email: user.email,
        password: user.password
      }
    }
  end
end