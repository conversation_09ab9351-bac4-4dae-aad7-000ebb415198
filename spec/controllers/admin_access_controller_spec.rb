require 'rails_helper'

RSpec.describe AdminAccessController, type: :controller do
  let(:admin_user) { create(:user, role: :super_boss) }
  let(:regular_user) { create(:user) }

  before do
    sign_in admin_user
  end

  describe "GET #index" do
    let!(:draft_project) { create(:project, user: regular_user, project_status: false, approved: false) }
    let!(:pending_project) { create(:project, user: regular_user, project_status: true, approved: false) }
    let!(:published_project) do
      project = create(:project, user: regular_user, project_status: true, approved: false)
      project.admin_approver = admin_user
      project.update!(approved: true)
      project
    end

    context "without status filter" do
      it "returns all projects" do
        get :index
        expect(assigns(:projects)).to contain_exactly(draft_project, pending_project, published_project)
      end

      it "sets status counts correctly" do
        get :index
        expect(assigns(:status_counts)).to eq({
          total: 3,
          draft: 1,
          pending: 1,
          published: 1
        })
      end
    end

    context "with draft status filter" do
      it "returns only draft projects" do
        get :index, params: { status: 'draft' }
        expect(assigns(:projects)).to contain_exactly(draft_project)
        expect(assigns(:status_filter)).to eq('draft')
      end
    end

    context "with pending status filter" do
      it "returns only pending projects" do
        get :index, params: { status: 'pending' }
        expect(assigns(:projects)).to contain_exactly(pending_project)
        expect(assigns(:status_filter)).to eq('pending')
      end
    end

    context "with published status filter" do
      it "returns only published projects" do
        get :index, params: { status: 'published' }
        expect(assigns(:projects)).to contain_exactly(published_project)
        expect(assigns(:status_filter)).to eq('published')
      end
    end

    context "with invalid status filter" do
      it "returns all projects" do
        get :index, params: { status: 'invalid' }
        expect(assigns(:projects)).to contain_exactly(draft_project, pending_project, published_project)
        expect(assigns(:status_filter)).to eq('invalid')
      end
    end
  end

  describe "DELETE #destroy_project" do
    let!(:project) { create(:project, user: regular_user) }

    it "deletes the project and redirects with success notice" do
      expect {
        delete :destroy_project, params: { id: project.id }
      }.to change(Project, :count).by(-1)

      expect(response).to redirect_to(admin_dashboard_path)
      expect(flash[:notice]).to eq("Project was successfully deleted.")
    end

    it "handles non-existent project gracefully" do
      expect {
        delete :destroy_project, params: { id: 99999 }
      }.not_to change(Project, :count)

      expect(response).to redirect_to(admin_dashboard_path)
      expect(flash[:alert]).to eq("Project not found.")
    end
  end
end
