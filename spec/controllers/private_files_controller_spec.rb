require 'rails_helper'

RSpec.describe PrivateFilesController, type: :controller do
  let(:user) { User.create!(email: '<EMAIL>', password: 'password123', password_confirmation: 'password123') }
  let(:project) { Project.create!(name: 'Test Project', user: user, project_type: 'real_estate', category: 'apartment') }
  let(:other_user) { User.create!(email: '<EMAIL>', password: 'password123', password_confirmation: 'password123') }
  let(:pdf_file) { fixture_file_upload('spec/fixtures/files/test.pdf', 'application/pdf') }
  
  before do
    project.private_files.attach(pdf_file)
  end

  describe 'GET #secure_download' do
    context 'when user is the project owner' do
      before { sign_in user }

      it 'streams the file with attachment disposition' do
        file = project.private_files.first
        get :secure_download, params: { project_id: project.id, id: file.id }
        
        expect(response).to have_http_status(:success)
        expect(response.headers['Content-Type']).to eq('application/pdf')
        expect(response.headers['Content-Disposition']).to include('attachment')
      end
    end

    context 'when user has full_details access' do
      before do
        sign_in other_user
        ProjectAuth.create!(project: project, user: other_user, access_level: 'full_details')
      end

      it 'streams the file' do
        file = project.private_files.first
        get :secure_download, params: { project_id: project.id, id: file.id }
        
        expect(response).to have_http_status(:success)
      end
    end

    context 'when user lacks authorization' do
      before { sign_in other_user }

      it 'raises authorization error' do
        file = project.private_files.first
        expect {
          get :secure_download, params: { project_id: project.id, id: file.id }
        }.to raise_error(ActionPolicy::Unauthorized)
      end
    end
  end

  describe 'GET #stream_content' do
    context 'when user is authorized' do
      before { sign_in user }

      it 'streams the file with inline disposition' do
        file = project.private_files.first
        get :stream_content, params: { project_id: project.id, id: file.id }
        
        expect(response).to have_http_status(:success)
        expect(response.headers['Content-Type']).to eq('application/pdf')
        expect(response.headers['Content-Disposition']).to include('inline')
      end
    end
  end

  describe 'GET #stream_content with token' do
    let(:file) { project.private_files.first }
    # Mocking the service, assuming it has a `generate_token` method
    let(:token_service) { class_double("SecureFileTokenService").as_stubbed_const }
    let(:valid_payload) { { 'file_id' => file.id, 'user_id' => user.id, 'project_id' => project.id } }
    let(:valid_token) { "valid-token" }
    let(:invalid_token) { "invalid-token" }

    before do
      allow(token_service).to receive(:decode_token).with(invalid_token).and_return(nil)
      allow(token_service).to receive(:decode_token).with(valid_token).and_return(valid_payload)
    end

    context 'with a valid token' do
      it 'streams the file and sets cache-control headers' do
        get :stream_content, params: { t: valid_token }
        
        expect(response).to have_http_status(:success)
        expect(response.headers['Content-Type']).to eq('application/pdf')
        expect(response.headers['Content-Disposition']).to include('inline')
        expect(response.headers['Cache-Control']).to eq('no-cache, no-store, must-revalidate, max-age=0')
      end
    end

    context 'with an invalid token' do
      it 'returns forbidden' do
        get :stream_content, params: { t: invalid_token }
        expect(response).to have_http_status(:forbidden)
      end
    end

    context 'when user access is not authorized' do
      let(:unauthorized_user) { create(:user) }
      let(:unauthorized_payload) { { 'file_id' => file.id, 'user_id' => unauthorized_user.id, 'project_id' => project.id } }
      let(:unauthorized_token) { "unauthorized-token" }

      it 'returns forbidden' do
        allow(token_service).to receive(:decode_token).with(unauthorized_token).and_return(unauthorized_payload)
        get :stream_content, params: { t: unauthorized_token }
        expect(response).to have_http_status(:forbidden)
      end
    end

    context 'when file does not belong to project in token' do
      let(:other_project) { create(:project) }
      let(:wrong_project_payload) { { 'file_id' => file.id, 'user_id' => user.id, 'project_id' => other_project.id } }
      let(:wrong_project_token) { "wrong-project-token" }

      it 'returns forbidden' do
        # This tests the critical security check
        allow(token_service).to receive(:decode_token).with(wrong_project_token).and_return(wrong_project_payload)
        get :stream_content, params: { t: wrong_project_token }
        expect(response).to have_http_status(:forbidden)
      end
    end
  end

  describe 'POST #request_file_token' do
    let(:file) { project.private_files.first }
    let(:valid_file_hash) { project.generate_secure_file_hash(file) }
    
    context 'when user is authorized' do
      before { sign_in user }

      it 'returns a token for valid file hash' do
        post :request_file_token, 
             params: { project_id: project.id, file_hash: valid_file_hash },
             format: :json

        expect(response).to have_http_status(:ok)
        
        json = JSON.parse(response.body)
        expect(json['token']).to be_present
        expect(json['content_type']).to eq('application/pdf')
        expect(json['expires_in']).to eq(JWT_EXPIRATION.to_i)
      end

      it 'validates token content' do
        post :request_file_token, 
             params: { project_id: project.id, file_hash: valid_file_hash },
             format: :json

        token = JSON.parse(response.body)['token']
        payload = SecureFileTokenService.decode_token(token)
        
        expect(payload['file_id']).to eq(file.id)
        expect(payload['user_id']).to eq(user.id)
        expect(payload['project_id']).to eq(project.id)
      end

      it 'rejects invalid file hash' do
        post :request_file_token, 
             params: { project_id: project.id, file_hash: 'invalid_hash' },
             format: :json

        expect(response).to have_http_status(:not_found)
        
        json = JSON.parse(response.body)
        expect(json['error']).to eq('File not found')
      end

      it 'rejects missing file hash' do
        post :request_file_token, 
             params: { project_id: project.id },
             format: :json

        expect(response).to have_http_status(:bad_request)
        
        json = JSON.parse(response.body)
        expect(json['error']).to eq('Invalid request')
      end
    end

    context 'when user is not authorized' do
      before { sign_in other_user }

      it 'denies access' do
        expect {
          post :request_file_token, 
               params: { project_id: project.id, file_hash: valid_file_hash },
               format: :json
        }.to raise_error(ActionPolicy::Unauthorized)
      end
    end

    context 'when user has full_details access' do
      before do 
        sign_in other_user
        ProjectAuth.create!(project: project, user: other_user, access_level: 'full_details')
      end

      it 'allows access for authorized users' do
        post :request_file_token, 
             params: { project_id: project.id, file_hash: valid_file_hash },
             format: :json

        expect(response).to have_http_status(:ok)
        
        json = JSON.parse(response.body)
        expect(json['token']).to be_present
      end
    end

    context 'security validations' do
      before { sign_in user }

      it 'generates unique tokens for repeated requests' do
        post :request_file_token, 
             params: { project_id: project.id, file_hash: valid_file_hash },
             format: :json
        token1 = JSON.parse(response.body)['token']

        post :request_file_token, 
             params: { project_id: project.id, file_hash: valid_file_hash },
             format: :json
        token2 = JSON.parse(response.body)['token']

        expect(token1).not_to eq(token2)
      end

      it 'does not expose sensitive file information' do
        post :request_file_token, 
             params: { project_id: project.id, file_hash: valid_file_hash },
             format: :json

        response_body = response.body
        expect(response_body).not_to include(file.id.to_s)
        expect(response_body).not_to include(file.filename.to_s)
        expect(response_body).not_to include(file.key)
      end
    end
  end

  describe 'GET /secure/stream (token-based streaming)' do
    let(:file) { project.private_files.first }
    let(:valid_token) { SecureFileTokenService.generate_token(file, user) }

    context 'with valid token and authorization' do
      it 'streams file content with security headers' do
        request.env['HTTP_REFERER'] = "http://test.host/projects/#{project.id}"
        get :stream_content, params: { t: valid_token }

        expect(response).to have_http_status(:ok)
        expect(response.headers['Content-Type']).to eq('application/pdf')
        expect(response.headers['Cache-Control']).to eq('no-cache, no-store, must-revalidate, max-age=0')
        expect(response.headers['Pragma']).to eq('no-cache')
        expect(response.headers['Expires']).to eq('0')
      end

      it 'validates referrer header for malicious sites' do
        request.env['HTTP_REFERER'] = "http://malicious-site.com"
        get :stream_content, params: { t: valid_token }

        expect(response).to have_http_status(:forbidden)
      end

      it 'rejects requests without referrer header' do
        # SECURITY POLICY: Enforce referrer requirement for all secure streams
        # request.env['HTTP_REFERER'] is nil by default in tests
        get :stream_content, params: { t: valid_token }

        expect(response).to have_http_status(:forbidden)
      end
    end

    context 'with invalid tokens' do
      it 'rejects malformed tokens' do
        get :stream_content, params: { t: 'invalid_token' }
        expect(response).to have_http_status(:forbidden)
      end

      it 'rejects expired tokens' do
        # Use the service to generate an already-expired token
        expired_token = SecureFileTokenService.generate_token(file, user, expires_in: -1.hour)
        
        get :stream_content, params: { t: expired_token }
        expect(response).to have_http_status(:forbidden)
      end

      it 'rejects tokens for non-existent files' do
        # Create a valid token, then simulate file deletion
        token = SecureFileTokenService.generate_token(file, user)
        
        # Simulate the file being deleted after token generation
        file.purge
        
        get :stream_content, params: { t: token }
        expect(response).to have_http_status(:forbidden)
      end
    end

    context 'authorization revalidation' do
      it 'revalidates authorization even with valid token' do
        # Create a token for a user who doesn't own the project
        unauthorized_token = SecureFileTokenService.generate_token(file, other_user)
        
        get :stream_content, params: { t: unauthorized_token }
        expect(response).to have_http_status(:forbidden)
      end

      it 'handles authorization changes after token generation' do
        # Get valid token for authorized user
        token = valid_token
        
        # Remove user's access by changing project ownership
        project.update!(user: other_user)
        
        # Token should now be rejected
        request.env['HTTP_REFERER'] = "http://test.host/projects/#{project.id}"
        get :stream_content, params: { t: token }
        expect(response).to have_http_status(:forbidden)
      end
    end
  end
end