require "rails_helper"

RSpec.describe ProjectSharesController, type: :routing do
  describe "routing" do
    it "routes to #index" do
      expect(get: "/project_shares").to route_to("project_shares#index")
    end

    it "routes to #new" do
      expect(get: "/project_shares/new").to route_to("project_shares#new")
    end

    it "routes to #show" do
      expect(get: "/project_shares/1").to route_to("project_shares#show", id: "1")
    end

    it "routes to #edit" do
      expect(get: "/project_shares/1/edit").to route_to("project_shares#edit", id: "1")
    end


    it "routes to #create" do
      expect(post: "/project_shares").to route_to("project_shares#create")
    end

    it "routes to #update via PUT" do
      expect(put: "/project_shares/1").to route_to("project_shares#update", id: "1")
    end

    it "routes to #update via PATCH" do
      expect(patch: "/project_shares/1").to route_to("project_shares#update", id: "1")
    end

    it "routes to #destroy" do
      expect(delete: "/project_shares/1").to route_to("project_shares#destroy", id: "1")
    end
  end
end
