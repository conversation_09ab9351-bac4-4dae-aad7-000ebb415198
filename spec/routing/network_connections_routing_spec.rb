require "rails_helper"

RSpec.describe NetworkConnectionsController, type: :routing do
  describe "routing" do
    it "routes to #index" do
      expect(get: "/network_connections").to route_to("network_connections#index")
    end

    it "routes to #new" do
      expect(get: "/network_connections/new").to route_to("network_connections#new")
    end

    it "routes to #show" do
      expect(get: "/network_connections/1").to route_to("network_connections#show", id: "1")
    end

    it "routes to #edit" do
      expect(get: "/network_connections/1/edit").to route_to("network_connections#edit", id: "1")
    end


    it "routes to #create" do
      expect(post: "/network_connections").to route_to("network_connections#create")
    end

    it "routes to #update via PUT" do
      expect(put: "/network_connections/1").to route_to("network_connections#update", id: "1")
    end

    it "routes to #update via PATCH" do
      expect(patch: "/network_connections/1").to route_to("network_connections#update", id: "1")
    end

    it "routes to #destroy" do
      expect(delete: "/network_connections/1").to route_to("network_connections#destroy", id: "1")
    end
  end
end
