# TDD PROMPT FOR PRIVATE<PERSON><PERSON>SCONTROLLER API

I want to implement the secure API endpoints in `PrivateFilesController` using strict test-driven development (TDD). 

**IMPORTANT TDD RULES:**
- We are doing test-driven development, so DO NOT write any implementation code yet.
- Write only tests based on the expected input/output pairs I'll provide below.
- Do not create mock implementations or stub functions.
- Make sure all tests fail initially (red phase).
- Only write implementation code when I explicitly ask you to make tests pass.

**Step 1: Write Tests Only**
Please write comprehensive tests for the `PrivateFilesController` endpoints that cover:
- Happy path scenarios
- Edge cases 
- Error conditions
- Input validation
- Security scenarios

**Expected behavior:**
Create a new request spec `spec/requests/private_files_spec.rb` or add to an existing one with these tests.

*   **Endpoint: `POST /projects/:id/request_file_token`**
    *   **Authorization:**
        *   **Given:** An unauthenticated request.
        *   **It should:** Return a `401 Unauthorized` status.
        *   **Given:** A request from an authenticated user who is NOT authorized for the project.
        *   **It should:** Return a `404 Not Found` status (prevents enumeration of valid projects).
    
    *   **Input Validation:**
        *   **Given:** A request with a missing `file_hash` parameter.
        *   **It should:** Return a `400 Bad Request` status.
        *   **Given:** A request with a `file_hash` that doesn't exist for the project.
        *   **It should:** Return a `404 Not Found` status (prevents enumeration of valid file hashes).
        *   **Given:** A request with a malformed `file_hash` (wrong format/length).
        *   **It should:** Return a `400 Bad Request` status.
        *   **Given:** A request with an empty `file_hash`.
        *   **It should:** Return a `400 Bad Request` status.
    
    *   **Success Case:**
        *   **Given:** A valid request from an authorized user.
        *   **It should:** Return a `200 OK` status with a JSON payload containing `token`, `content_type`, and `expires_in`.
        *   **Given:** A request for a file with missing content_type.
        *   **It should:** Return a `200 OK` status with `content_type` defaulting to `application/octet-stream`.
    
    *   **Security (Rate Limiting):**
        *   **Given:** A burst of requests from the same IP or user.
        *   **It should:** Eventually return a `429 Too Many Requests` status.
        *   **Given:** Requests that exceed the rate limit threshold.
        *   **It should:** Block subsequent requests for the cooldown period.

*   **Endpoint: `GET /secure/stream`**
    *   **Token Handling:**
        *   **Given:** A request with no token.
        *   **It should:** Return a `403 Forbidden` status.
        *   **Given:** A request with an expired token.
        *   **It should:** Return a `403 Forbidden` status.
        *   **Given:** A request with an invalid/malformed token.
        *   **It should:** Return a `403 Forbidden` status.
        *   **Given:** A request with a valid token that has already been used (token replay attack).
        *   **It should:** Return a `403 Forbidden` status (single-use tokens).
    
    *   **Context and Re-Authorization:**
        *   **Given:** A valid token but a missing `Referer` header.
        *   **It should:** Return a `403 Forbidden` status.
        *   **Given:** A valid token but an invalid `Referer` header (wrong domain).
        *   **It should:** Return a `403 Forbidden` status.
        *   **Given:** A valid token for a user whose permissions were revoked after the token was issued.
        *   **It should:** Return a `403 Forbidden` status (re-authorization check).
        *   **Given:** A valid token for a project that was deleted after the token was issued.
        *   **It should:** Return a `404 Not Found` status.
        *   **Given:** A valid token for a file that was deleted after the token was issued.
        *   **It should:** Return a `404 Not Found` status.
    
    *   **Success Case:**
        *   **Given:** A valid token from a valid context.
        *   **It should:**
            *   Return a `200 OK` status.
            *   Stream the raw file content in the response body.
            *   Set the `Content-Type` header to match the file's type.
            *   Set `Cache-Control` headers to prevent caching (`no-cache, no-store, must-revalidate`).
            *   Set `X-Content-Type-Options: nosniff` header.
            *   Set `X-Frame-Options: DENY` header.
            *   Set appropriate `Content-Disposition` header for downloads.
    
    *   **Error Handling and Upstream Failures:**
        *   **Given:** A valid token but the underlying file storage (S3/GCS) fails.
        *   **It should:** Return a `503 Service Unavailable` status (not 500).
        *   **Given:** A valid token but the file is corrupted or zero-byte.
        *   **It should:** Return a `422 Unprocessable Entity` status with an error message.
    
    *   **DoS Protection:**
        *   **Given:** Requests for extremely large files that could exhaust server resources.
        *   **It should:** Stream content efficiently without loading entire file into memory.
        *   **Given:** Multiple concurrent streaming requests from the same user.
        *   **It should:** Apply rate limiting to prevent resource exhaustion.

*   **Token Security and Lifecycle**
    *   **Given:** Multiple token generation requests for the same file within a short period.
    *   **It should:** Generate different tokens each time (prevent token prediction).
    
    *   **Given:** A token generated for a specific file_hash and project_id.
    *   **It should:** Only be valid for that exact file on that exact project (scope validation).
    
    *   **Given:** A token that expires while a streaming request is in progress.
    *   **It should:** Complete the current stream but reject new requests with the same token.

After writing tests, run them to confirm they fail, then commit the tests when you're satisfied with coverage.

Wait for my confirmation before proceeding to implementation. 