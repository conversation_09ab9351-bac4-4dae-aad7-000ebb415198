# TDD PROMPT FOR SECURE FILE DISPLAY FEATURE

I want to implement the frontend interactions for the secure file display feature using strict test-driven development (TDD) with system tests.

**IMPORTANT TDD RULES:**
- We are doing test-driven development, so DO NOT write any implementation code yet for the frontend logic.
- Write only tests based on the expected user flows I'll provide below.
- Use a tool like <PERSON>y<PERSON> to simulate user interaction.
- Make sure all tests fail initially (red phase).
- Only write implementation code when I explicitly ask you to make tests pass.

**Step 1: Write Tests Only**
Please write comprehensive system tests for the secure file display feature that cover:
- Happy path scenarios
- Edge cases 
- Error conditions
- Security scenarios

**Expected behavior:**
Create a new system/feature spec `spec/features/secure_file_display_spec.rb` with these tests.

*   **Feature: Secure File Preview**
    *   **Scenario:** A user views the project page and clicks on a previewable file (image or PDF).
    *   **Expected flow:**
        1.  The page displays a lightbox/modal with a loading indicator.
        2.  The lightbox content is replaced by either an `<img>` tag or an `<iframe>` tag.
        3.  The `src` attribute of the image/iframe points to the `/secure/stream?t=...` endpoint.
        4.  The lightbox can be closed by clicking a close button, the backdrop, or pressing the `Escape` key.
        5.  After closing, the `<img>` or `<iframe>` tag is removed from the DOM to prevent memory leaks.
    
    *   **Scenario:** A user clicks on a non-previewable file (e.g., ZIP, DOC, XLS).
    *   **Expected flow:**
        1.  The lightbox is NOT displayed.
        2.  A file download is initiated immediately.
        3.  The downloaded file has a generic, non-identifying name.
    
    *   **Scenario:** A user clicks on a corrupted or zero-byte file.
    *   **Expected flow:**
        1.  The lightbox displays with a loading indicator.
        2.  The lightbox shows a specific error message: "File is empty or cannot be previewed".
    
    *   **Scenario:** The server returns an error when requesting the token.
    *   **Expected flow:** 
        1.  The lightbox displays a specific error message based on the error type:
            - 404: "File not found"
            - 503: "Service temporarily unavailable"
            - Default: "Unable to load file"

*   **Feature: Secure File Download**
    *   **Scenario:** A user clicks a "Download" button on the project page or within the lightbox.
    *   **Expected flow:**
        1.  A file download is initiated in the browser.
        2.  The downloaded file has a generic, non-identifying name (e.g., `secure_file_[timestamp]`).
    
    *   **Scenario:** A user attempts to download a file that was deleted after page load.
    *   **Expected flow:**
        1.  An error message is displayed: "File no longer available".
        2.  No download is initiated.

*   **Feature: DOM Security and Cleanup**
    *   **Scenario:** Multiple preview/close cycles.
    *   **Expected flow:**
        1.  User opens and closes the lightbox multiple times.
        2.  No duplicate DOM elements accumulate.
        3.  No memory leaks from lingering secure content.
        4.  Previous secure URLs are not reused or cached.

After writing tests, run them to confirm they fail, then commit the tests when you're satisfied with coverage.

Wait for my confirmation before proceeding to implementation. 