# TDD PROMPT FOR SECUREFILEACCESS CONCERN

I want to implement the `SecureFileAccess` concern using strict test-driven development (TDD). 

**IMPORTANT TDD RULES:**
- We are doing test-driven development, so DO NOT write any implementation code yet.
- Write only tests based on the expected input/output pairs I'll provide below.
- Do not create mock implementations or stub functions.
- Make sure all tests fail initially (red phase).
- Only write implementation code when I explicitly ask you to make tests pass.

**Step 1: Write Tests Only**
Please write comprehensive tests for the `SecureFileAccess` concern that cover:
- Happy path scenarios
- Edge cases 
- Error conditions
- Input validation
- Security scenarios

**Expected behavior:**
Integrate these tests into your existing model spec (e.g., `spec/models/project_spec.rb`) or a new concern spec.

*   **Hash Generation (`#generate_secure_file_hash`)**
    *   **Given:** A `Project` instance and an attached file.
    *   **It should:**
        *   Generate a 32-character hexadecimal string (minimum).
        *   Always produce the same hash for the same file on the same project.
        *   Produce a different hash for a different file on the same project.
        *   **SECURITY:** Produce a different hash for the same file on different projects (project-scoped hashing).
        *   **SECURITY:** Not be a simple concatenation of public IDs (should use cryptographic hashing).
    
    *   **Given:** Invalid inputs to `#generate_secure_file_hash`:
        *   **Project not saved (no ID):** Should raise `ArgumentError`.
        *   **File not attached:** Should raise `ArgumentError`.
        *   **Nil file parameter:** Should raise `ArgumentError`.

*   **File Lookup (`#find_file_by_secure_hash`)**
    *   **Given:** A valid hash that corresponds to a file attached to the project.
    *   **It should:** Return the correct `ActiveStorage::Attachment` instance.
    
    *   **Given:** A hash that does not correspond to any file attached to the project.
    *   **It should:** Return `nil`.
    
    *   **Given:** A hash that corresponds to a file on a different project.
    *   **It should:** Return `nil` (project isolation).
    
    *   **Given:** Invalid inputs to `#find_file_by_secure_hash`:
        *   **Empty string hash:** Should return `nil`.
        *   **Nil hash:** Should return `nil`.
        *   **Malformed hash (wrong length/format):** Should return `nil`.

*   **Hash Consistency and Security**
    *   **Given:** The same file attached to two different projects.
    *   **It should:** Generate different hashes for each project (prevents cross-project access).
    
    *   **Given:** Multiple calls to `#generate_secure_file_hash` for the same file.
    *   **It should:** Always return the same hash (deterministic).
    
    *   **Given:** A file that is re-attached to the same project (e.g., after deletion and re-upload).
    *   **It should:** Generate the same hash as before (based on stable identifiers).

*   **DoS Protection and Resource Limits**
    *   **Given:** A very large file (>100MB).
    *   **It should:** Generate hash without loading entire file into memory.
    
    *   **Given:** A file with missing or corrupted metadata.
    *   **It should:** Handle gracefully and return a valid hash or raise a specific exception.

After writing tests, run them to confirm they fail, then commit the tests when you're satisfied with coverage.

Wait for my confirmation before proceeding to implementation. 