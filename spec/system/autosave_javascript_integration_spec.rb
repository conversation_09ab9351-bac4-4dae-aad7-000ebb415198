# ABOUTME: System tests for JavaScript autosave integration in UNL-42 deferred project creation
# ABOUTME: Tests the complete browser-side behavior including form updates and URL switching

require 'rails_helper'

RSpec.describe "Autosave JavaScript Integration (UNL-42)", type: :system, js: true do
  let(:user) { create(:user) }
  
  before do
    driven_by(:selenium_chrome_headless)
    login_as(user, scope: :user)
  end

  scenario "Form action URL updates after first autosave" do
    visit new_project_path
    
    # Initially form should POST to /projects
    form = find('#project_form')
    expect(form['action']).to include('/projects')
    expect(form['action']).not_to match(%r{/projects/\d+})
    
    # Fill in content to trigger autosave
    fill_in 'Summary', with: 'JavaScript Test Project'
    
    # Wait for autosave to complete and form to update
    sleep 4
    
    # Form action should now be updated to PATCH specific project
    project = Project.last
    expect(project).to be_present
    
    form = find('#project_form')
    expect(form['action']).to include("/projects/#{project.id}")
    expect(form['data-project-id']).to eq(project.id.to_s)
  end

  scenario "Subsequent form inputs use PATCH instead of POST" do
    visit new_project_path
    
    # First input triggers creation
    fill_in 'Summary', with: 'Initial Content'
    sleep 4
    
    project = Project.last
    expect(project.summary).to eq('Initial Content')
    
    # Add more content - should update existing project
    fill_in 'Location', with: 'Test Location'
    sleep 4
    
    # Should not create new project
    expect(Project.count).to eq(1)
    
    project.reload
    expect(project.location).to eq('Test Location')
    expect(project.summary).to eq('Initial Content')
  end

  scenario "Autosave indicator provides user feedback" do
    visit new_project_path
    
    # Start typing to trigger autosave
    fill_in 'Summary', with: 'User Feedback Test'
    
    # Should show some kind of saving indicator
    # (This test might need adjustment based on actual UI implementation)
    expect(page).to have_css('#autosave-indicator', wait: 5)
    
    # Wait for save to complete
    sleep 4
    
    # Indicator should change or disappear
    expect(page).not_to have_content('Saving...', wait: 2)
  end

  scenario "Multiple rapid changes are debounced correctly" do
    visit new_project_path
    
    # Type rapidly without pauses
    fill_in 'Summary', with: 'A'
    fill_in 'Summary', with: 'Ab'
    fill_in 'Summary', with: 'Abc'
    fill_in 'Summary', with: 'Rapid Changes Test'
    
    # Wait for debounced save
    sleep 4
    
    # Should create only one project with final content
    expect(Project.count).to eq(1)
    expect(Project.last.summary).to eq('Rapid Changes Test')
  end

  scenario "Page refresh after autosave preserves project" do
    visit new_project_path
    
    # Create project via autosave
    fill_in 'Summary', with: 'Refresh Test'
    sleep 4
    
    project = Project.last
    expect(project).to be_present
    
    # Refresh the page
    page.refresh
    
    # Project should still exist
    expect(Project.find(project.id)).to be_present
    
    # Should be on new project page (not edit page after refresh)
    expect(current_path).to eq(new_project_path)
  end

  scenario "Browser back button after project creation" do
    # Start from a known page
    visit projects_path
    
    # Navigate to new project
    click_link 'New Project' rescue visit(new_project_path)
    
    # Create project
    fill_in 'Summary', with: 'Back Button Test'
    sleep 4
    
    project = Project.last
    expect(project).to be_present
    
    # Go back using browser button
    page.go_back
    
    # Should be back at projects page
    expect(current_path).to eq(projects_path)
    
    # Project should still exist
    expect(Project.find(project.id)).to be_present
  end

  scenario "Form validation works after autosave creation" do
    visit new_project_path
    
    # Create project via autosave
    fill_in 'Summary', with: 'Validation Test'
    sleep 4
    
    project = Project.last
    
    # Try to submit incomplete form for publishing
    # (This test may need adjustment based on actual validation rules)
    click_button 'Save' rescue click_button 'Update'
    
    # Should handle form submission appropriately
    expect(page).to have_current_path(edit_project_path(project))
    
    project.reload
    expect(project.summary).to eq('Validation Test')
  end

  scenario "Network disconnection handling", skip: "Requires network simulation" do
    # This would test offline behavior
    # Implementation depends on actual offline handling in the app
  end

  scenario "Concurrent user sessions don't interfere" do
    # First session
    visit new_project_path
    fill_in 'Summary', with: 'Session 1 Project'
    sleep 4
    
    project1 = Project.last
    
    # Simulate second user session in new browser window
    using_session(:user2) do
      other_user = create(:user)
      login_as(other_user, scope: :user)
      
      visit new_project_path
      fill_in 'Summary', with: 'Session 2 Project'
      sleep 4
      
      project2 = Project.last
      expect(project2.user).to eq(other_user)
      expect(project2).not_to eq(project1)
    end
    
    # Back to first session - should still work independently
    fill_in 'Location', with: 'Updated Location'
    sleep 4
    
    project1.reload
    expect(project1.location).to eq('Updated Location')
    expect(project1.user).to eq(user)
  end

  scenario "Form field types are handled correctly" do
    visit new_project_path
    
    # Test different field types
    fill_in 'Summary', with: 'Field Types Test'
    
    # Select dropdown (if exists)
    select 'Real Estate', from: 'Project Type' rescue nil
    
    # Text area
    fill_in 'Full Description', with: 'Detailed description content'
    
    # Wait for autosave
    sleep 4
    
    project = Project.last
    expect(project.summary).to eq('Field Types Test')
    expect(project.full_description).to eq('Detailed description content')
  end

  scenario "Empty to content to empty transitions" do
    visit new_project_path
    initial_count = Project.count
    
    # Start with empty form - no project created
    fill_in 'Summary', with: ''
    sleep 4
    expect(Project.count).to eq(initial_count)
    
    # Add content - project created
    fill_in 'Summary', with: 'Now has content'
    sleep 4
    expect(Project.count).to eq(initial_count + 1)
    
    project = Project.last
    expect(project.summary).to eq('Now has content')
    
    # Clear content - project remains but with empty summary
    fill_in 'Summary', with: ''
    sleep 4
    
    # Should still be same project count
    expect(Project.count).to eq(initial_count + 1)
    
    project.reload
    expect(project.summary).to be_blank
  end
end