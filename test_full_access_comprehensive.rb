# Comprehensive test for full_access implementation

Rails.application.eager_load!

puts "Comprehensive Full Access Implementation Test"
puts "=" * 60

# Get test users
owner = User.first
connected_user = User.second  
unconnected_user = User.third

if !owner || !connected_user || !unconnected_user
  puts "ERROR: Need at least 3 users in the database"
  exit 1
end

puts "Test Users:"
puts "- Owner: #{owner.email} (ID: #{owner.id})"
puts "- Connected User: #{connected_user.email} (ID: #{connected_user.id})"
puts "- Unconnected User: #{unconnected_user.email} (ID: #{unconnected_user.id})"

# Ensure connection between owner and connected_user
connection = NetworkConnection.where(
  "(inviter_id = ? AND invitee_id = ?) OR (inviter_id = ? AND invitee_id = ?)",
  owner.id, connected_user.id, connected_user.id, owner.id
).first

if !connection
  puts "\nCreating network connection between owner and connected_user..."
  connection = NetworkConnection.create!(inviter: owner, invitee: connected_user)
  puts "Connection created!"
else
  puts "\nNetwork connection already exists between owner and connected_user"
end

# Test Scenario 1: full_access + network_only
puts "\n" + "-" * 60
puts "Test Scenario 1: full_access=true, network_only=true"
puts "-" * 60

project1 = Project.find_or_create_by(
  user: owner,
  summary: "Test Project - Full Access Network Only"
) do |p|
  p.full_description = "Testing full_access with network_only"
  p.full_access = true
  p.network_only = true
  p.summary_only = false
  p.semi_public = false
  p.project_type = "other"
  p.category = "uncategorized"
  p.subcategory = "unspecified"
  p.approved = true
end

puts "\nProject settings:"
puts "- full_access: #{project1.full_access}"
puts "- network_only: #{project1.network_only}"
puts "- summary_only: #{project1.summary_only}"
puts "- semi_public: #{project1.semi_public}"

puts "\nTesting user_has_access?:"
puts "- Owner: #{project1.user_has_access?(owner)}"
puts "- Connected User: #{project1.user_has_access?(connected_user)}"
puts "- Unconnected User: #{project1.user_has_access?(unconnected_user)}"

# Test Scenario 2: full_access + semi_public
puts "\n" + "-" * 60
puts "Test Scenario 2: full_access=true, semi_public=true"
puts "-" * 60

project2 = Project.find_or_create_by(
  user: owner,
  summary: "Test Project - Full Access Everyone"
) do |p|
  p.full_description = "Testing full_access with semi_public"
  p.full_access = true
  p.semi_public = true
  p.summary_only = false
  p.network_only = false
  p.project_type = "other"
  p.category = "uncategorized"
  p.subcategory = "unspecified"
  p.approved = true
end

puts "\nProject settings:"
puts "- full_access: #{project2.full_access}"
puts "- semi_public: #{project2.semi_public}"
puts "- summary_only: #{project2.summary_only}"
puts "- network_only: #{project2.network_only}"

puts "\nTesting user_has_access?:"
puts "- Owner: #{project2.user_has_access?(owner)}"
puts "- Connected User: #{project2.user_has_access?(connected_user)}"
puts "- Unconnected User: #{project2.user_has_access?(unconnected_user)}"

# Test Scenario 3: summary_only (traditional behavior)
puts "\n" + "-" * 60
puts "Test Scenario 3: summary_only=true (traditional behavior)"
puts "-" * 60

project3 = Project.find_or_create_by(
  user: owner,
  summary: "Test Project - Summary Only"
) do |p|
  p.full_description = "Testing traditional summary_only behavior"
  p.summary_only = true
  p.network_only = true
  p.full_access = false
  p.semi_public = false
  p.project_type = "other"
  p.category = "uncategorized"
  p.subcategory = "unspecified"
  p.approved = true
end

puts "\nProject settings:"
puts "- summary_only: #{project3.summary_only}"
puts "- network_only: #{project3.network_only}"
puts "- full_access: #{project3.full_access}"
puts "- semi_public: #{project3.semi_public}"

puts "\nTesting user_has_access?:"
puts "- Owner: #{project3.user_has_access?(owner)}"
puts "- Connected User: #{project3.user_has_access?(connected_user)}"
puts "- Unconnected User: #{project3.user_has_access?(unconnected_user)}"

puts "\n" + "=" * 60
puts "Summary of Expected vs Actual Results:"
puts "=" * 60
puts "\nScenario 1 (full_access + network_only):"
puts "✓ Owner should have access: #{project1.user_has_access?(owner)}"
puts "✓ Connected user should have access: #{project1.user_has_access?(connected_user)}"
puts "✓ Unconnected user should NOT have access: #{!project1.user_has_access?(unconnected_user)}"

puts "\nScenario 2 (full_access + semi_public):"
puts "✓ Owner should have access: #{project2.user_has_access?(owner)}"
puts "✓ Connected user should have access: #{project2.user_has_access?(connected_user)}"
puts "✓ Unconnected user should have access: #{project2.user_has_access?(unconnected_user)}"

puts "\nScenario 3 (summary_only):"
puts "✓ Owner should have access: #{project3.user_has_access?(owner)}"
puts "✓ Connected user should NOT have access: #{!project3.user_has_access?(connected_user)}"
puts "✓ Unconnected user should NOT have access: #{!project3.user_has_access?(unconnected_user)}"

puts "\n" + "=" * 60
puts "Test complete!"