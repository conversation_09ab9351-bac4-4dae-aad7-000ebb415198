# BACKLOG.md

Product and technical improvement suggestions based on architectural analysis.

## Recently Implemented ✅ (June 2025)

### Thumbnail Generation Race Condition Solution
- **Problem**: Thumbnail jobs ran before S3 uploads completed due to `after_commit` callbacks
- **Solution**: Implemented ActiveJob event subscriber listening to `perform.active_job`
- **Pattern**: Subscribe to `ActiveStorage::AnalyzeJob` completion events
- **Result**: Zero race conditions - thumbnails only generated after files are uploaded and analyzed
- **Files**: 
  - `config/initializers/active_storage_subscribers.rb` (NEW)
  - `app/models/project.rb` (removed callback)
  - `app/jobs/thumbnail_generation_job.rb` (simplified)
- **Documentation**: See `THUMBNAIL_GENERATION_SOLUTION.md`

---

## Test Quality Improvements (December 2025)

### 🔴 High Priority Security Test Enhancements

#### 1. File Access Security Testing  
**Priority**: High  
**Issue**: Request specs test direct file ID endpoints instead of secure hash endpoints  
**Current Problem**: 
- Test uses `/projects/:id/files/:file_id/inline` with direct file IDs
- Security implementation uses `generate_secure_file_hash` to prevent enumeration attacks
- Tests bypass the actual security mechanism being used in production

**Recommended Fix**:
- Update request specs to test secure hash-based file access routes
- Ensure tests match actual production file access patterns
- Add tests for file access enumeration attack prevention

**Files to Update**:
- `spec/requests/full_access_authorization_spec.rb:199`
- May require route updates to accept hash instead of file ID

#### 2. Strengthen Request Spec Assertions
**Priority**: High  
**Issue**: Weak assertions in index page tests don't verify actual UI elements  
**Current Problem**:
- Test claims to check "access indicators" but only verifies project summary presence
- Missing verification of "View" vs "Request Access" buttons
- Regressions in view logic would not be caught

**Recommended Fix**:
- Add specific assertions for presence/absence of action links
- Use more precise matchers to verify correct button types
- Consider using `assert_select` or Nokogiri for HTML structure verification

**Files to Update**:
- `spec/requests/full_access_authorization_spec.rb:161`
- `spec/features/full_access_integration_spec.rb` (already better, but could strengthen)

---

## New Backlog Items (June 2025)

### 🔧 Thumbnail Generation Refactoring

#### 1. Extract Active Storage Subscriber to Service Object
**Priority**: Medium
**Current Issue**: Event subscriber logic is in an initializer, making it harder to test and maintain
**Suggestion**:
- Create `app/services/thumbnail_event_service.rb` to handle event processing
- Make the service testable with proper dependency injection
- Add comprehensive unit tests for event handling logic
- Keep initializer slim - just register the service

#### 2. Add Comprehensive Test Coverage for Thumbnail Generation
**Priority**: High
**Current Issue**: No tests for the ActiveJob event subscription system
**Suggestion**:
- Test that thumbnails are created after file analysis
- Test that PDFs are skipped (Lambda handles them)
- Test error handling when blob has no attachments
- Test idempotency (no duplicate thumbnails)
- Add integration test for full upload → analyze → thumbnail flow

#### 3. Implement Thumbnail Generation Monitoring
**Priority**: Medium
**Current Issue**: No visibility into thumbnail generation success/failure rates
**Suggestion**:
- Add logging with structured data for monitoring
- Track metrics: generation time, success rate, file types
- Add alerts for high failure rates
- Consider Scout APM custom metrics

#### 4. Optimize Thumbnail Storage
**Priority**: Low
**Current Issue**: All thumbnails stored as PNG, may not be optimal
**Suggestion**:
- Use WebP format for better compression
- Implement multiple thumbnail sizes (small, medium, large)
- Add lazy thumbnail generation for rarely accessed files
- Consider CDN for thumbnail delivery

## Architecture & Code Quality Improvements

Based on comprehensive architectural analysis of non-obvious design decisions (January 2025).

### 🔧 Refactoring Opportunities

#### 1. Simplify Dual Project Visibility System
**Current Issue**: The two-dimensional access control (audience + detail level) creates 4 combinations that are complex to manage and understand.
**Non-obvious complexity**: Projects have network_only/semi_public dimension crossed with summary_only/full_access dimension, creating a matrix that's difficult for developers and users to understand.
**Suggestion**: 
- Consider consolidating into a single enum with clear naming: `public_summary`, `network_summary`, `network_full`, `private`
- This would simplify UI, validation, and database queries
- Alternative: Add helper methods that clearly explain the 4 combinations with business-friendly names
- Document these patterns in code comments for future developers

#### 2. Extract NetworkConnection Logic
**Current Issue**: User model handles complex bidirectional connection logic that's hard to test and maintain.
**Non-obvious complexity**: The `NetworkConnection.where('inviter_id = ? OR invitee_id = ?', id, id)` pattern appears throughout the codebase, making maintenance difficult.
**Suggestion**:
- Create `NetworkConnectionService` to handle connection creation, validation, and querying
- Add `User#connected_users` method that encapsulates the bidirectional logic
- Move complex queries out of User model into dedicated service methods
- Add proper error handling for edge cases (duplicate connections, self-connections)
- Create comprehensive specs for edge cases in bidirectional relationships

#### 3. Decouple Project Approval from Content Changes
**Current Issue**: Any summary change triggers approval reset, which may be too restrictive for minor edits.
**Non-obvious complexity**: The `set_approval_status_based_on_changes` callback and virtual attributes like `admin_approver` create tight coupling between content changes and approval workflow.
**Suggestion**:
- Implement change severity detection (major vs minor changes)
- Allow admins to configure which fields trigger re-approval
- Extract approval logic into `ProjectApprovalService`
- Add approval history/audit trail
- Consider allowing "trusted users" to make minor edits without re-approval

#### 4. **NEW: Rationalize Invitation-Only Registration Logic**
**Current Issue**: Registration toggle via `ENV['INVITE_ONLY']` is validated in User model rather than controller level.
**Non-obvious complexity**: Business logic about invitation requirements is embedded in model validations, making it hard to change registration flows.
**Suggestion**:
- Move invitation-only logic to a dedicated `RegistrationPolicy`
- Add controller-level checks with clear error messages
- Allow for gradual rollout of open registration (percentage-based, geographic, etc.)
- Add admin interface for managing registration settings
- Consider different invitation types (full access vs limited trial)

#### 5. **NEW: Simplify Project Type Hierarchy**
**Current Issue**: Three-level taxonomy (project_type → category → subcategory) with validation dependencies.
**Non-obvious complexity**: The nested `PROJECT_TYPES` and `CATEGORIES` constants create complex validation chains that aren't obvious from the schema.
**Suggestion**:
- Consider flattening to two levels if business requirements allow
- Extract category management to a separate admin interface
- Add clear documentation of valid combinations
- Implement database constraints to enforce referential integrity
- Create visual hierarchy documentation for users and developers

#### 6. **NEW: Decouple Authentication from Social Networking**
**Current Issue**: Automatic connection creation via `after_invitation_accepted` tightly couples authentication with social features.
**Non-obvious complexity**: Users may want authentication without automatic networking, but current design assumes all invited users want connections.
**Suggestion**:
- Make connection creation optional during invitation acceptance
- Add user preference for automatic connections
- Allow admin control over connection policies
- Consider separating invitation for access vs. invitation for networking
- Add "connection preferences" to user onboarding flow

#### 7. **NEW: Improve Complex Query Documentation**
**Current Issue**: Complex queries like `Project.full_list_for_user` contain non-obvious business logic.
**Non-obvious complexity**: The scope combines multiple access control dimensions with performance optimizations that aren't immediately clear.
**Suggestion**:
- Add comprehensive inline documentation explaining each JOIN and WHERE clause
- Create query diagram showing relationships and access paths
- Document performance implications of each query component
- Add query performance benchmarks to test suite
- Consider query analysis tools for monitoring production performance

#### 8. **NEW: Create Business Logic Documentation Layer**
**Current Issue**: Non-obvious business rules are scattered across models, controllers, and callbacks.
**Non-obvious complexity**: Understanding why certain decisions were made requires reading multiple files and reverse-engineering intent.
**Suggestion**:
- Create architectural decision records (ADRs) for major design choices
- Add business rule documentation to model files
- Create visual flowcharts for complex workflows (approval, connection creation)
- Implement living documentation that generates from code comments
- Add "why" comments alongside complex code blocks

### 🚀 Performance Optimizations

#### 7. Optimize Project Listing Queries
**Current Issue**: `Project.full_list_for_user` performs complex JOINs that may not scale well.
**Suggestions**:
- Add database indexes for common query patterns
- Consider caching user connection lists
- Implement pagination with proper ordering
- Add query analysis and monitoring

#### 8. Implement Connection Caching
**Current Issue**: Network connection checks happen frequently and involve database queries.
**Suggestion**:
- Cache user connection lists in Redis
- Invalidate cache on connection changes
- Add background jobs for cache warming

### 🛡️ Security & Data Integrity

#### 9. Strengthen Project Access Controls
**Current Issue**: Authorization logic is spread across models and controllers.
**Suggestions**:
- Centralize all project access logic in ActionPolicy policies
- Add comprehensive authorization tests
- Implement audit logging for sensitive operations
- Add rate limiting for project creation/updates

#### 10. Improve File Security
**Current Issue**: File access relies on application-level authorization only.
**Suggestions**:
- Implement signed URLs with expiration for S3 downloads
- Add virus scanning for uploaded files
- Implement file access logging
- Add automatic file cleanup for deleted projects

### 📊 User Experience Improvements

#### 11. Enhanced Project Discovery
**Current Issue**: Limited search and filtering capabilities.
**Suggestions**:
- Implement full-text search with Elasticsearch or similar
- Add advanced filtering by location, project type, price range
- Implement recommendation engine based on user connections and interests
- Add saved searches and notifications

#### 12. Connection Management Dashboard
**Current Issue**: Limited visibility into network connections and requests.
**Suggestions**:
- Build comprehensive connection management interface
- Add connection analytics (mutual connections, network growth)
- Implement connection suggestions based on project interests
- Add bulk connection management tools

### 🔄 System Reliability

#### 13. Background Job Infrastructure
**Current Issue**: Email sending and other async operations happen inline.
**Suggestions**:
- Implement Sidekiq or similar for background processing
- Move email sending, file processing, and geocoding to background jobs
- Add job monitoring and failure handling
- Implement job retries with exponential backoff

#### 14. Comprehensive Monitoring
**Current Issue**: Limited application monitoring beyond Scout APM.
**Suggestions**:
- Add application health checks and metrics
- Implement error tracking (Sentry, Rollbar)
- Add business metrics dashboards
- Set up alerting for critical failures

### 🌐 Internationalization Enhancements

#### 15. Dynamic Language Switching
**Current Issue**: Language preference is stored but UI switching may be limited.
**Suggestions**:
- Implement seamless language switching without page reload
- Add user language preference persistence
- Localize all user-generated content appropriately
- Add RTL language support if needed

### 📱 API & Integration Readiness

#### 16. API Development
**Current Issue**: No documented API for potential mobile app or integrations.
**Suggestions**:
- Design RESTful API with proper versioning
- Implement API authentication (JWT tokens)
- Add API rate limiting and documentation
- Consider GraphQL for complex queries

#### 17. Third-party Integrations
**Suggestions**:
- CRM integration for lead management
- Payment processing for premium features
- Calendar integration for project deadlines
- Document signing integration for contracts

## Priority Recommendations

### High Priority (Next Sprint)
1. **Critical Network Connections Memory Issues** (#18) - 70-80% memory reduction needed
2. **Extract NetworkConnection Logic** (#2) - Improves maintainability and performance
3. **Optimize Project Listing Queries** (#7) - Critical for performance as user base grows
4. **Strengthen Project Access Controls** (#9) - Security is paramount

### Medium Priority (Next Quarter)
5. **Simplify Dual Project Visibility System** (#1) - Improves UX and development velocity
6. **Rationalize Invitation-Only Registration Logic** (#4) - Better user experience
7. **Enhanced Project Discovery** (#11) - Key differentiator for user engagement
8. **Background Job Infrastructure** (#13) - Foundation for reliability

### Low Priority (Future Considerations)
9. **Decouple Authentication from Social Networking** (#6) - Flexibility improvement
10. **Simplify Project Type Hierarchy** (#5) - Development simplification
11. **API Development** (#16) - Important for long-term growth
12. **Comprehensive Monitoring** (#14) - Operational excellence
13. **Dynamic Language Switching** (#15) - User experience enhancement

## Technical Debt Items

### Code Quality
- Remove commented-out code in models and views
- Standardize error message formatting across the application
- Implement consistent validation message internationalization
- Clean up unused translations and assets

### Database & Performance
- Add missing database indexes identified through query analysis
- Review and optimize the `Project.full_list_for_user` complex JOIN queries
- Add database constraints to enforce referential integrity for project type hierarchy

### Documentation & Architecture
- Add comprehensive documentation for complex business logic patterns:
  - Bidirectional network connections (`User#network_connections`)
  - Dual project visibility system (4-combination matrix)
  - Automatic connection creation on invitation acceptance
  - Project approval workflow with state management
- Document the invitation-only registration toggle logic
- Create architectural decision records (ADRs) for non-obvious design choices

## Memory Management & Monitoring (Jan 2025)

### Completed in Recent Session
- ✅ Memory debugging infrastructure (debug controller, endpoints, testing tools)
- ✅ Geocoder memory leak resolution (86% reduction achieved)
- ✅ Automated memory leak testing script
- ✅ Memory profiling configuration with allocation tracing

### Future Memory Monitoring Tasks

#### 15. Enhanced Memory Monitoring
**Priority**: Medium
**Description**: Expand memory monitoring beyond current debugging tools
**Tasks**:
- Set up automated memory alerts in production (Scout APM integration)
- Implement memory usage trends dashboard
- Add memory leak detection in CI/CD pipeline
- Create memory usage regression tests

#### 16. Memory Optimization Review
**Priority**: Low  
**Description**: Systematic review of memory usage patterns
**Tasks**:
- Analyze heap dumps for other potential memory leaks
- Review object retention in ActionView rendering
- Optimize large dataset processing (project listings, user networks)
- Implement memory-efficient pagination strategies

#### 17. Production Memory Safeguards
**Priority**: Medium
**Description**: Add production safety measures for memory management
**Tasks**:
- Implement automatic memory cleanup triggers
- Add memory circuit breakers for high-usage operations
- Create memory usage logging and alerting
- Document memory troubleshooting runbook for ops team

### Network Connections Memory Optimization Analysis (Jan 2025)

**Analysis Completed**: Comprehensive memory leak analysis of NetworkConnectionsController and NetworkConnection model

#### 18. Critical Network Connections Memory Issues **[HIGH PRIORITY]**
**Priority**: High - Sprint Priority  
**Status**: Analysis Complete, Implementation Needed  
**Estimated Impact**: 70-80% memory reduction in network connections features

**Critical Issues Identified**:
- **Complex 4-table joins** in `UserProfile.with_connections_for`/`without_connections_for` scopes
- **No pagination** in `NetworkConnectionsController#index` - loads all matching records
- **String interpolation in SQL** prevents query plan caching
- **Double user joins + eager loading** in `my` action creates object bloat
- **N+1 query potential** in view rendering

**Files Affected**:
- `app/controllers/network_connections_controller.rb` (lines 7-49)
- `app/models/user_profile.rb` (scopes lines 4-37) 
- `app/models/network_connection.rb` (validation queries)

**Immediate Fixes Required**:

1. **Add Pagination to index action**:
   ```ruby
   # Replace unpaginated query with pagy
   @pagy, @user_profiles = pagy(base_query, limit: 20)
   ```
   *Impact*: Reduces memory from ~50MB (1000 users) to ~1MB (20 users per page)

2. **Simplify UserProfile scopes with subqueries**:
   ```ruby
   # Replace complex 4-table joins with exists subqueries
   scope :with_connections_for, ->(user_id) {
     where(user_id: NetworkConnection.select(:inviter_id, :invitee_id)
       .for_user(user_id).select("CASE WHEN inviter_id = ? THEN invitee_id ELSE inviter_id END", user_id))
   }
   ```
   *Impact*: Eliminates 4-table cartesian joins, ~60% memory reduction

3. **Add Database Indexes**:
   ```ruby
   add_index :network_connections, [:inviter_id, :invitee_id]
   add_index :user_profiles, [:user_id, :first_name, :last_name]
   add_composite_index :user_profiles, [:city, :country]
   ```

4. **Extract NetworkConnection Service Layer**:
   - Move complex queries out of controller
   - Implement caching for frequently accessed connection lists
   - Add query result memoization

5. **Implement Connection Caching Strategy**:
   ```ruby
   # Redis-based user connection cache
   Rails.cache.fetch("user_connections:#{user_id}", expires_in: 1.hour) do
     NetworkConnection.for_user(user_id).pluck(:connected_user_ids)
   end
   ```

**Testing Strategy**:
- Add `/memory_debug/test_network_connections_leak` endpoint
- Monitor: `NetworkConnectionsController#index`, `#my` actions  
- Track: Object allocation, query count, memory growth patterns

**Acceptance Criteria**:
- Memory usage reduced by 70-80% for network connections features
- Page load times improve for network connections index
- No regression in functionality or user experience
- Comprehensive test coverage for optimized queries

### 🚀 Infrastructure Improvements

#### 19. **NEXT PRIORITY: Redis Implementation for Rate Limiting**
**Priority**: High - Next Sprint Priority  
**Status**: Backlog (DatabaseStore currently implemented)  
**Estimated Impact**: Significant performance improvement for rate limiting under high load

**Current State**: Successfully implemented DatabaseStore for Rack::Attack rate limiting cache to enable multi-process support without introducing new infrastructure dependencies.

**Why Redis is Next Priority**:
- **Performance**: DatabaseStore adds database load for every rate-limited request
- **DoS Risk**: High-volume attacks could overwhelm database with cache queries
- **Scalability**: Redis is purpose-built for high-frequency read/write operations
- **Industry Standard**: GitHub, Shopify (Rack::Attack maintainers) use Redis

**Implementation Plan**:

1. **Add Redis Dependency**:
   ```ruby
   # Gemfile
   gem 'redis', '~> 5.0'
   gem 'hiredis', '~> 0.6.3'  # Optional: C-based parser for performance
   ```

2. **Configure Redis for Rate Limiting**:
   ```ruby
   # config/initializers/redis.rb
   REDIS_URL = ENV.fetch('REDIS_URL', 'redis://localhost:6379/1')
   Redis.current = Redis.new(url: REDIS_URL)
   
   # config/initializers/rack_attack.rb
   # Replace: Rack::Attack.cache.store = ActiveSupport::Cache::DatabaseStore.new
   # With:
   Rack::Attack.cache.store = ActiveSupport::Cache::RedisCacheStore.new(url: REDIS_URL)
   ```

3. **Infrastructure Setup Options**:
   - **Render Redis** (recommended for Render deployments): $7/month for 25MB
   - **ElastiCache** (AWS): Various tiers available
   - **Self-hosted**: Redis container alongside app
   - **Heroku Redis**: If using Heroku

4. **Migration Strategy**:
   ```ruby
   # Gradual migration approach
   # 1. Deploy Redis alongside DatabaseStore
   # 2. Test Redis performance in staging
   # 3. Switch production cache store
   # 4. Remove database cache table if desired
   ```

5. **Performance Expectations**:
   - **DatabaseStore**: ~10-50ms per rate limit check (database query)
   - **Redis**: ~1-5ms per rate limit check (in-memory)
   - **Under Attack**: Database could become bottleneck, Redis handles gracefully

**Files to Update**:
- `config/initializers/rack_attack.rb` (cache store configuration)
- `config/initializers/redis.rb` (new file)
- `Gemfile` (add Redis gems)
- Infrastructure configuration (Redis service setup)

**Testing Strategy**:
- Verify rate limiting continues to work after Redis migration
- Performance testing under load (use enhanced `test_rate_limiting.rb`)
- Failover testing (Redis unavailable scenarios)

**Cost Considerations**:
- Current: $0 (uses existing database)
- Redis: ~$7-15/month for managed service
- ROI: Database performance protection worth the cost

**Acceptance Criteria**:
- Rate limiting performance improves significantly under load
- No service disruption during migration
- Database load decreases for rate limiting operations
- Monitoring shows Redis is functioning correctly

## Security Test Quality Improvements (Deferred from Chunk 12)

### 20. **MEDIUM: Remove Flaky Timing Attack Test**
**Priority**: Medium  
**Status**: Deferred from Gemini Code Review (Jan 2025)  
**Description**: Replace unreliable timing measurement with implementation-level security validation

**Current Issue**: The timing attack prevention test in `spec/requests/secure_file_access_spec.rb:365` measures response times, which is flaky and unreliable in test environments. This can lead to false positives/negatives.

**Solution**:
- Remove the wall-clock time measurement test
- Ensure the implementation uses `ActiveSupport::SecurityUtils.secure_compare` for constant-time comparison
- Focus test on functional behavior (correct status codes) rather than timing
- Add code review requirement for constant-time operations

**Files Affected**:
- `spec/requests/secure_file_access_spec.rb` (simplify timing attack test)
- `app/controllers/private_files_controller.rb` (verify constant-time comparison usage)

### 21. **MEDIUM: Fix Controller Test Coupling**
**Priority**: Medium  
**Status**: Deferred from Gemini Code Review (Jan 2025)  
**Description**: Improve test isolation with consistent mocking patterns

**Current Issue**: Controller test `validates token content` calls real `SecureFileTokenService.decode_token`, creating coupling between controller and service tests.

**Solution**:
- Mock `SecureFileTokenService` consistently in controller tests
- Verify service method calls with correct arguments
- Keep service behavior testing in service specs only
- Improve test isolation and failure diagnosis

**Files Affected**:
- `spec/controllers/private_files_controller_spec.rb` (add consistent mocking)

### 22. **LOW: Optimize Test Performance**
**Priority**: Low  
**Status**: Deferred from Gemini Code Review (Jan 2025)  
**Description**: Use lazy loading for better test performance

**Current Issue**: Using `let!` forces creation of test records before every test, even when not used, slowing down test suite.

**Solution**:
- Replace `let!` with `let` for lazy loading
- Only create test data when first accessed
- Improve test suite performance as it grows

**Files Affected**:
- `spec/requests/secure_file_access_spec.rb` (lazy loading for file_attachment, image_attachment)
- `spec/services/secure_file_token_service_spec.rb` (lazy loading for file_attachment)

**Estimated Impact**: 10-20% faster test suite execution

## High Priority

### Refactor `Project.full_list_for_user` to be Composable and Countable

**Ticket:** [TICKET-ID-001]
**Status:** To Do
**Owner:** TBD
**Priority:** High

#### Problem Statement
The current `Project.full_list_for_user` scope is monolithic. It combines filtering logic (finding visible projects) with data-shaping logic (adding custom `SELECT` aliases like `auth_level`). This makes it incompatible with ActiveRecord's default `.count` method, which breaks pagination gems like `pagy`. The current workaround in `ProjectsController` uses `unscope(:select).count`, which is a tactical fix but can be brittle.

#### Proposed Solution
Refactor the single scope into two smaller, composable scopes with distinct responsibilities: one for filtering and one for data shaping. This is a more robust, maintainable, and "Rails-like" solution.

1.  **Create a "countable" scope for filtering (`:visible_to`)**: This scope will contain all the necessary `JOIN`s and `WHERE` conditions for determining project visibility. It will not have a custom `.select()` clause, making it fully compatible with `.count`.

2.  **Create a "data-shaping" scope (`:with_details_for_user`)**: This scope will add the required custom `SELECT` aliases (`owner_id`, `auth_level`, etc.). It should be applied *after* pagination, only to the records for the current page.

#### Implementation Plan

**1. Refactor Scopes in `app/models/project.rb`:**

Replace `full_list_for_user` with the following two scopes:

```ruby
# This scope contains all the necessary joins and conditions for filtering and visibility.
# It is fully "countable" because it has no custom .select() clause.
scope :visible_to, ->(user) {
  joins(<<~SQL)
    LEFT JOIN network_connections ON
      (network_connections.invitee_id = #{connection.quote(user.id)} AND network_connections.inviter_id = projects.user_id) OR
      (network_connections.inviter_id = #{connection.quote(user.id)} AND network_connections.invitee_id = projects.user_id)
    LEFT JOIN project_auths ON
      project_auths.project_id = projects.id AND
      project_auths.user_id = #{connection.quote(user.id)}
  SQL
  .where(
    "(projects.network_only = true AND network_connections.id IS NOT NULL) OR
     projects.semi_public = true OR
     projects.user_id = :user_id",
    user_id: user.id
  )
}

# This scope adds the custom selects. It should be applied *after* pagination.
scope :with_details_for_user, -> {
  # The left_outer_joins(:user) can be added here if not already present.
  # The project_auths join is already in :visible_to
  select(
    'projects.*',
    'users.id as owner_id', # Assumes a join to users is present
    'project_auths.access_level AS auth_level',
    'project_auths.id AS auth_id'
  )
}
```
*Note: The `with_details_for_user` scope relies on the `project_auths` table being joined in the base query (`visible_to`). A `left_outer_joins(:user)` might also be needed if not already included.*


**2. Update `app/controllers/projects_controller.rb`:**

Modify the `index` action to use the new, composable scopes. This removes the need for the `unscope` workaround.

```ruby
# app/controllers/projects_controller.rb

def index
  if current_user
    # 1. Start with the "countable" base query
    base_query = Project.visible_to(current_user).active.approved

    # 2. Apply all filters.
    # ... (filtering logic for project_type, search, etc.) ...
    
    # 3. Paginate the countable query. Pagy handles the count automatically.
    @pagy, paged_projects = pagy(base_query, limit: 10)

    # 4. Apply the custom select *only to the page of results*.
    @projects = paged_projects.with_details_for_user.preload(user: :user_profile)

  else
    # ...
  end
end
```

#### Acceptance Criteria
-   Create comprehensive RSpec tests for the new scopes (`visible_to`, `with_details_for_user`) to ensure correctness.
-   Update controller tests for `ProjectsController#index` to verify pagination works as expected.
-   The `unscope(:select)` workaround is removed from the controller.
-   The project list page functions identically to before, with pagination and all data displaying correctly.

## File Processing Security Enhancements

### 23. **Phase 2: Pre-signed S3 URLs for Worker Isolation**
**Priority**: Medium  
**Status**: Backlog (Phase 1 temporary URLs completed)  
**Description**: Eliminate all S3 credentials from background worker

**Implementation**:
```ruby
# Main app generates pre-signed URLs for worker
def enqueue_thumbnail_job(project, file)
  input_url = file.service_url(expires_in: 1.hour)
  
  output_key = "thumbnails/#{SecureRandom.uuid}.png"
  output_url = s3_client.presigned_url(:put_object,
    bucket: 'unlisters-files',
    key: output_key,
    expires_in: 1.hour,
    content_type: 'image/png'
  )
  
  PdfThumbnailGenerationJob.perform_later({
    input_url: input_url,
    output_put_url: output_url,
    project_id: project.id,
    output_key: output_key
  })
end

# Worker has zero S3 credentials
class PdfThumbnailGenerationJob < ApplicationJob
  def perform(job_payload)
    download_and_process(job_payload[:input_url], job_payload[:output_put_url])
  end
end
```

**Benefits**:
- Worker cannot access any S3 files except those explicitly provided
- Time-limited access (1 hour maximum)
- No persistent S3 credentials stored on worker
- Complete S3 isolation if worker is compromised

### 24. **Phase 3: Separate S3 Buckets with Minimal IAM**
**Priority**: Low  
**Status**: Backlog (for high-security scenarios)  
**Description**: Complete S3 isolation with separate worker bucket

**Architecture**:
```
Main Bucket (unlisters-files):     Worker Bucket (unlisters-worker):
├── private-files/                 ├── processing/job-uuid/
└── thumbnails/                    └── output/job-uuid/
```

**IAM Policy for Worker**:
```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": ["s3:GetObject"],
      "Resource": "arn:aws:s3:::unlisters-worker/processing/*"
    },
    {
      "Effect": "Allow", 
      "Action": ["s3:PutObject"],
      "Resource": "arn:aws:s3:::unlisters-worker/output/*"
    }
  ]
}
```

**Benefits**:
- Complete isolation of worker S3 access
- Main application bucket remains completely inaccessible to worker
- Automatic cleanup of temporary worker files
- Defense in depth for high-security requirements

---

## Notification System Enhancements (June 2025)

### 25. **HIGH PRIORITY: Comprehensive User Notification Preferences System**
**Priority**: High - Next Major Feature  
**Status**: Backlog (bulk notification hotfix completed)  
**Description**: Implement granular notification preferences to solve the mass notification problem properly

**Business Problem**: 
Current system sends notifications to ALL active users for `semi_public` projects, causing:
- Email fatigue and unsubscribes
- Job queue flooding (hundreds of individual email jobs)
- Poor user experience (irrelevant notifications)
- Potential compliance issues (GDPR, CAN-SPAM)

**Proposed Solution**: 
Create a comprehensive notification preferences system allowing users to:
- Opt in/out of different notification types
- Choose notification frequency (instant, daily digest, weekly digest)
- Select specific categories/project types of interest
- Set geographic/network-based filters
- Manage communication preferences per channel (email, SMS, push)

#### 25.1 **Database Schema Design**

**New Tables**:
```ruby
# User notification preferences
class CreateNotificationPreferences < ActiveRecord::Migration[7.0]
  def change
    create_table :notification_preferences do |t|
      t.references :user, null: false, foreign_key: true
      t.string :notification_type, null: false # 'new_project', 'connection_request', etc.
      t.string :frequency, default: 'instant' # 'instant', 'daily', 'weekly', 'disabled'
      t.json :filters # Category, location, network-based filters
      t.boolean :enabled, default: true
      t.timestamps
    end
    
    add_index :notification_preferences, [:user_id, :notification_type], unique: true
    add_index :notification_preferences, :notification_type
  end
end

# Notification queue for digest emails
class CreateNotificationQueue < ActiveRecord::Migration[7.0]
  def change
    create_table :notification_queue_items do |t|
      t.references :user, null: false, foreign_key: true
      t.string :notification_type, null: false
      t.references :subject, polymorphic: true, null: false # Project, ConnectionRequest, etc.
      t.json :data # Serialized notification data
      t.boolean :processed, default: false
      t.datetime :scheduled_for
      t.timestamps
    end
    
    add_index :notification_queue_items, [:user_id, :processed, :scheduled_for]
    add_index :notification_queue_items, [:notification_type, :processed]
  end
end
```

#### 25.2 **Models and Services**

**NotificationPreference Model**:
```ruby
class NotificationPreference < ApplicationRecord
  belongs_to :user
  
  NOTIFICATION_TYPES = %w[
    new_project
    connection_request
    project_update
    access_request
    admin_notification
  ].freeze
  
  FREQUENCIES = %w[instant daily weekly disabled].freeze
  
  validates :notification_type, inclusion: { in: NOTIFICATION_TYPES }
  validates :frequency, inclusion: { in: FREQUENCIES }
  validates :notification_type, uniqueness: { scope: :user_id }
  
  scope :enabled, -> { where(enabled: true) }
  scope :for_type, ->(type) { where(notification_type: type) }
  scope :instant, -> { where(frequency: 'instant') }
  scope :digest, -> { where(frequency: ['daily', 'weekly']) }
end
```

**NotificationTargetingService**:
```ruby
class NotificationTargetingService
  def initialize(project, notification_type = 'new_project')
    @project = project
    @notification_type = notification_type
  end
  
  def target_users
    base_users = project_visibility_users
    filter_by_preferences(base_users)
  end
  
  def instant_notification_users
    target_users.joins(:notification_preferences)
                .where(notification_preferences: { 
                  notification_type: @notification_type, 
                  frequency: 'instant',
                  enabled: true 
                })
  end
  
  def digest_notification_users(frequency)
    target_users.joins(:notification_preferences)
                .where(notification_preferences: { 
                  notification_type: @notification_type, 
                  frequency: frequency,
                  enabled: true 
                })
  end
  
  private
  
  def project_visibility_users
    if @project.network_only?
      @project.user.connected_users_bidirectional.active
    elsif @project.semi_public?
      User.active
    else
      User.none
    end
  end
  
  def filter_by_preferences(users)
    # Apply category filters, geographic filters, etc.
    # Based on NotificationPreference filters JSON
  end
end
```

#### 25.3 **Enhanced Controller Integration**

**Updated ProjectsController**:
```ruby
# Replace current bulk notification with targeted approach
users_to_notify = NotificationTargetingService.new(@project, 'new_project')

# Instant notifications
instant_users = users_to_notify.instant_notification_users
if instant_users.any?
  NotificationMailer.bulk_new_project_notification(@project, instant_users.pluck(:id)).deliver_later
end

# Queue digest notifications
['daily', 'weekly'].each do |frequency|
  digest_users = users_to_notify.digest_notification_users(frequency)
  digest_users.find_each do |user|
    NotificationQueueItem.create!(
      user: user,
      notification_type: 'new_project',
      subject: @project,
      data: { project_id: @project.id, created_at: Time.current },
      scheduled_for: next_digest_time(frequency, user)
    )
  end
end
```

#### 25.4 **User Interface Components**

**Notification Preferences Page**:
- `/user_profile/notification_preferences`
- Checkbox matrix: Notification Type vs Frequency
- Category/project type filters with checkboxes
- Geographic radius selector for location-based filtering
- Preview of notification volume based on current settings

**Dashboard Integration**:
- Notification preferences link in user menu
- Unsubscribe links in all emails with granular options
- Quick notification settings in project creation form

#### 25.5 **Digest Email System**

**Daily/Weekly Digest Jobs**:
```ruby
class DigestNotificationJob < ApplicationJob
  def perform(frequency) # 'daily' or 'weekly'
    NotificationQueueItem.joins(:user)
                         .where(processed: false, scheduled_for: ..Time.current)
                         .includes(:user, :subject)
                         .group_by(&:user)
                         .each do |user, notifications|
      
      DigestMailer.send_digest(user, notifications, frequency).deliver_now
      
      # Mark as processed
      NotificationQueueItem.where(id: notifications.map(&:id)).update_all(processed: true)
    end
  end
end

# Schedule with cron or good_job cron
class DigestScheduler
  def self.schedule_daily
    DigestNotificationJob.set(cron: '0 8 * * *').perform_later('daily')
  end
  
  def self.schedule_weekly  
    DigestNotificationJob.set(cron: '0 8 * * 1').perform_later('weekly')
  end
end
```

#### 25.6 **Migration and Default Settings**

**User Onboarding Defaults**:
```ruby
class User < ApplicationRecord
  after_create :create_default_notification_preferences
  
  private
  
  def create_default_notification_preferences
    NotificationPreference::NOTIFICATION_TYPES.each do |type|
      notification_preferences.create!(
        notification_type: type,
        frequency: type == 'new_project' ? 'weekly' : 'instant', # Conservative default
        enabled: true,
        filters: default_filters_for_type(type)
      )
    end
  end
end
```

#### 25.7 **Compliance and Privacy**

**GDPR/Privacy Features**:
- Export notification preferences in user data export
- Clear unsubscribe process with granular options
- Notification preference history/audit trail
- Double opt-in for marketing-style notifications
- One-click unsubscribe from email footers

#### 25.8 **Analytics and Monitoring**

**Notification Metrics Dashboard**:
- Notification volume by type and frequency
- User engagement rates (open/click rates)
- Unsubscribe patterns and reasons
- Digest email effectiveness
- A/B testing framework for notification content

#### 25.9 **Implementation Phases**

**Phase 1 (Sprint 1-2): Foundation**
- Database schema and basic models
- Default preference creation for existing users
- Basic preferences UI

**Phase 2 (Sprint 3-4): Integration**
- Replace current notification system with targeted approach
- Implement digest email system
- Enhanced filtering and targeting

**Phase 3 (Sprint 5-6): Advanced Features**
- Analytics dashboard
- A/B testing capabilities
- Advanced filtering (AI-based interest prediction)

#### 25.10 **Success Metrics**

**Key Performance Indicators**:
- 50% reduction in email unsubscribes
- 80% reduction in notification job queue volume
- 30% increase in user engagement with notifications
- 90% user satisfaction with notification relevance
- Zero compliance violations (GDPR, CAN-SPAM)

**Technical Metrics**:
- Notification targeting query performance < 100ms
- Digest email generation time < 5 minutes
- Background job queue stability (no throttling errors)

---

### 26. **MEDIUM: Advanced Notification Features**
**Priority**: Medium - Future Enhancement  
**Status**: Backlog (depends on #25)  
**Description**: Advanced notification system capabilities

#### Features:
- **Smart Notification Timing**: ML-based optimal send time per user
- **Push Notifications**: Browser/mobile push notification support
- **SMS Integration**: Twilio integration for critical notifications
- **Slack/Teams Integration**: Workspace notification channels
- **AI Content Personalization**: Personalized email content based on user interests
- **Advanced Analytics**: Notification performance optimization dashboard

---

## Notification System Race Condition Prevention (June 2025)

### 27. **MEDIUM: File Processing State Awareness in Notifications**
**Priority**: Medium - UX Enhancement
**Status**: Backlog (analysis complete)
**Description**: Improve user experience when clicking notification links while files are processing

**Current Issue**: 
- Notifications sent immediately when project published
- Users may click email link while files still uploading/processing
- Results in confusing "processing..." status instead of complete files

**Proposed Solutions**:

#### Option 1: Delayed Notifications (Simple)
```ruby
# In ProjectsController#update and #update_approval
if @project.private_files.any? { |file| !file.blob.analyzed? }
  # Delay notification by 5 minutes to allow file processing
  BulkNotificationJob.set(wait: 5.minutes).perform_later(@project, users_to_notify.pluck(:id))
else
  # Send immediately if no files or all analyzed
  BulkNotificationJob.perform_later(@project, users_to_notify.pluck(:id))
end
```

#### Option 2: Smart File Status Display (Recommended)
```ruby
# In views/projects/_full_details_project.html.erb
<% if @project.private_files.attached? %>
  <% analyzed_files = @project.private_files.select { |file| file.blob.analyzed? } %>
  <% processing_files = @project.private_files.reject { |file| file.blob.analyzed? } %>
  
  <% if processing_files.any? %>
    <div class="alert alert-info">
      <%= t('projects.files.processing_notice', count: processing_files.count) %>
    </div>
  <% end %>
  
  <!-- Show only analyzed files -->
  <% analyzed_files.each do |file| %>
    <!-- File display logic -->
  <% end %>
<% end %>
```

#### Option 3: Notification Content Disclaimer
```erb
<!-- In notification email template -->
<p><%= t('new_project_notification.files_notice', 
  default: 'Note: Some project files may still be processing. 
           They will appear once upload is complete.') %></p>
```

**Benefits**:
- Better user experience - no confusion about missing files
- No delay in notifications for projects without files
- Clear communication about system state

**Implementation Priority**: After critical fixes, before major feature additions

---

## Security Vulnerabilities (Critical Priority)

### 28. **CRITICAL: Fix IDOR Vulnerability in `delete_access` Action**
**Priority**: Critical - Security Issue  
**Status**: Discovered during upload cancellation system review (Jan 2025)  
**Description**: Insecure Direct Object Reference allows unauthorized access to ProjectAuth records

**Vulnerability Details**:
```ruby
# Current vulnerable code in ProjectsController#delete_access
@project_auth = ProjectAuth.find(params[:id])  # ❌ Direct find by ID
@project = @project_auth.project
authorize! @project, to: :manage_access?       # ✅ But authorizes after access
```

**Security Risk**:
- Attacker can pass ANY ProjectAuth ID in URL
- Code finds record first, THEN checks authorization
- Information disclosure risk in error messages
- Violates principle of least privilege

**Required Fix**:
```ruby
# Secure implementation - scope find through user's projects
auth_scope = ProjectAuth.joins(:project).where(projects: { user_id: current_user.id })
@project_auth = auth_scope.find(params[:id])
@project = @project_auth.project
authorize! @project, to: :manage_access?  # Defense in depth
```

**Files Affected**:
- `app/controllers/projects_controller.rb` (line ~150)

**Testing Requirements**:
- Verify user cannot access ProjectAuth from other projects
- Ensure error handling doesn't leak information
- Add security test for IDOR prevention

**Impact**: Potential unauthorized access to project authorization data

---

## Upload System Maintenance & Cleanup (July 2025)

### 29. **Upload Cleanup System**
**Priority**: High  
**Status**: ✅ **IMPLEMENTED** - Ready for deployment  
**Description**: Automated maintenance system for cleaning stuck uploads, orphaned temp files, and identifying unused S3 objects to optimize storage costs and system health.

**Documentation**: 
- Architecture: `docs/features/file-system/13_upload-cleanup-system.md`
- Usage Guide: `docs/maintenance/UPLOAD_CLEANUP_GUIDE.md`  
- Deployment: `docs/deployment/CLEANUP_CRON_SETUP.md`

### 30. **Network Connections Query Optimization**
**Priority**: High  
**Status**: ✅ **TEMPORARY FIX IMPLEMENTED** - Requires SQL optimization  
**Date Added**: July 18, 2025  
**Description**: Replace Ruby-based deduplication with optimized SQL-based queries for network connections to improve performance and maintainability.

**Current State**: 
- **Temporary Solution**: Controller-level deduplication using Ruby `group_by` and array processing
- **Works**: Eliminates duplicates and maintains all functionality
- **Performance**: Acceptable for current user base but not scalable
- **Maintainability**: Ruby-based filtering less efficient than SQL

**Required SQL Optimization**:
The current solution prioritizes **reliability and maintainability** over SQL optimization. We need to develop a SQL-optimized query that:

1. **Eliminates duplicates** at the database level using proper SQL techniques
2. **Maintains ActiveRecord compatibility** (works with `count()`, pagination, etc.)
3. **Provides connection information** without complex JOINs that cause cartesian products
4. **Handles edge cases** including multiple UserProfile records per user

**Technical Requirements**:

```ruby
# Target optimized implementation
scope :with_connections_for, ->(current_user_id) {
  # SQL-optimized query that:
  # 1. Returns ALL users with connection status
  # 2. No duplicates (handle multiple UserProfile records per user)
  # 3. Compatible with count(), pagination, etc.
  # 4. Efficient SQL execution plan
}

scope :without_connections_for, ->(current_user_id) {
  # SQL-optimized query that:
  # 1. Returns users WITHOUT connections to current_user
  # 2. No duplicates
  # 3. Efficient exclusion logic
}
```

**Database Issues to Address**:
- **Root Cause**: Multiple UserProfile records exist for single users (78 profiles for 67 users)
- **Database Constraint**: Add unique constraint on `user_profiles.user_id`
- **Data Cleanup**: Clean up duplicate UserProfile records before adding constraint

**Implementation Approach**:
1. **Phase 1**: Add database constraints to prevent future duplicates
2. **Phase 2**: Clean up existing duplicate UserProfile records
3. **Phase 3**: Implement optimized SQL queries with proper DISTINCT handling
4. **Phase 4**: Performance testing and benchmark comparison

**Files Affected**:
- `app/models/user_profile.rb` (scopes optimization)
- `app/controllers/network_connections_controller.rb` (remove Ruby-based deduplication)
- `app/views/network_connections/_users.html.erb` (revert to SQL-based connection status)
- Database migration for unique constraints

**Success Criteria**:
- ✅ No duplicate users in network connections list
- ✅ Maintains all existing functionality
- ✅ Works with ActiveRecord `count()` and pagination
- ✅ Improved query performance (target: 50% faster)
- ✅ Reduced memory usage (target: 60% reduction)
- ✅ SQL-based filtering instead of Ruby-based
- ✅ Passes all existing tests

**Related Documentation**:
- Fix details: `docs/fixes/network-connections/duplicate-connections-fix.md`
- Current temporary implementation works correctly
- Database integrity issues documented

**Priority Justification**:
- **High Priority**: Current Ruby-based approach doesn't scale with user growth
- **Performance Impact**: Memory usage increases linearly with user count
- **Code Quality**: SQL-based solution is more maintainable and Rails-idiomatic
- **User Experience**: Better performance for network connections interface