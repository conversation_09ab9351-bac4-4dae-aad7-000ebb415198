#!/usr/bin/env ruby
# Memory Leak Reproduction Script for Unlisters App
# Usage: ruby test_memory_leak.rb

require 'net/http'
require 'uri'
require 'json'

class MemoryLeakTester
  BASE_URL = 'http://localhost:3000'
  
  def initialize
    @results = []
    puts "🔍 Memory Leak Detection Script"
    puts "Make sure your Rails server is running with MEMORY_PROFILING=1"
    puts "=" * 60
  end
  
  def run_full_test
    puts "\n📊 Phase 1: Baseline Measurement"
    baseline = get_memory_stats
    return unless baseline
    
    puts "📊 Baseline: #{baseline['process_memory_mb'].round(2)} MB"
    
    puts "\n🔄 Phase 2: Heap Dump (Before)"
    heap_before = create_heap_dump
    
    puts "\n⚡ Phase 3: Load Testing - Projects Index"
    load_test_projects_index(50)
    
    puts "\n🔄 Phase 4: Heap Dump (After)"
    heap_after = create_heap_dump
    
    puts "\n📊 Phase 5: Final Memory Measurement"
    final = get_memory_stats
    return unless final
    
    memory_growth = final['process_memory_mb'] - baseline['process_memory_mb']
    object_growth = final['object_counts']['TOTAL'] - baseline['object_counts']['TOTAL']
    
    puts "\n" + "=" * 60
    puts "🎯 MEMORY LEAK TEST RESULTS"
    puts "=" * 60
    puts "Memory Growth: #{memory_growth.round(2)} MB"
    puts "Object Growth: #{object_growth} objects"
    puts "Baseline Memory: #{baseline['process_memory_mb'].round(2)} MB"
    puts "Final Memory: #{final['process_memory_mb'].round(2)} MB"
    
    if memory_growth > 10
      puts "🚨 POTENTIAL MEMORY LEAK DETECTED! (>10MB growth)"
    elsif memory_growth > 5
      puts "⚠️  MODERATE MEMORY GROWTH (>5MB)"
    else
      puts "✅ Memory usage appears normal"
    end
    
    puts "\nSuspect Objects:"
    if final['suspect_objects']
      final['suspect_objects'].each do |type, count|
        baseline_count = baseline.dig('suspect_objects', type) || 0
        growth = count - baseline_count
        puts "  #{type}: #{count} (+#{growth})" if growth > 0
      end
    end
    
    puts "\nHeap Dumps Created:"
    puts "  Before: #{heap_before}"
    puts "  After: #{heap_after}"
    puts "\nAnalyze with: heapy diff #{heap_before} #{heap_after}"
  end
  
  def test_specific_geocoder_leak
    puts "\n🧪 Testing Geocoder-Specific Memory Leak"
    
    baseline = get_memory_stats
    
    puts "Running geocoder test..."
    response = post_request('/memory_debug/test_projects_leak')
    
    if response && response['memory_growth_mb']
      growth = response['memory_growth_mb']
      puts "Geocoder test memory growth: #{growth.round(2)} MB"
      
      if growth > 5
        puts "🚨 GEOCODER LEAK DETECTED!"
      else
        puts "✅ Geocoder appears OK"
      end
    end
  end
  
  def force_garbage_collection
    puts "\n🗑️  Forcing Garbage Collection"
    response = post_request('/memory_debug/force_gc')
    
    if response
      puts "Memory freed: #{response['freed_mb']} MB"
      puts "Before: #{response['memory_before_mb']} MB"
      puts "After: #{response['memory_after_mb']} MB"
    end
  end
  
  private
  
  def get_memory_stats
    response = get_request('/memory_debug/stats')
    if response
      puts "Current memory: #{response['process_memory_mb'].round(2)} MB, Objects: #{response['object_counts']['TOTAL']}"
    end
    response
  end
  
  def create_heap_dump
    response = get_request('/memory_debug/heap_dump')
    if response
      filename = File.basename(response['filename'])
      puts "Heap dump created: #{filename}"
      return filename
    end
    nil
  end
  
  def load_test_projects_index(requests = 50)
    puts "Making #{requests} requests to /projects..."
    
    requests.times do |i|
      response = get_request('/projects')
      print "." if i % 10 == 0
      sleep(0.1) # Small delay to avoid overwhelming
    end
    
    puts "\nLoad test completed!"
  end
  
  def get_request(path)
    uri = URI("#{BASE_URL}#{path}")
    
    begin
      response = Net::HTTP.get_response(uri)
      if response.code == '200'
        JSON.parse(response.body)
      else
        puts "❌ Error: #{response.code} - #{response.message}"
        nil
      end
    rescue => e
      puts "❌ Request failed: #{e.message}"
      nil
    end
  end
  
  def post_request(path)
    uri = URI("#{BASE_URL}#{path}")
    
    begin
      response = Net::HTTP.post(uri, '')
      if response.code == '200'
        JSON.parse(response.body)
      else
        puts "❌ Error: #{response.code} - #{response.message}"
        nil
      end
    rescue => e
      puts "❌ Request failed: #{e.message}"
      nil
    end
  end
end

# Run the test
if __FILE__ == $0
  tester = MemoryLeakTester.new
  
  puts "\nSelect test to run:"
  puts "1. Full memory leak test (recommended)"
  puts "2. Quick geocoder test"
  puts "3. Force garbage collection"
  puts "4. Just get current memory stats"
  
  print "Enter choice (1-4): "
  choice = gets.chomp
  
  case choice
  when '1'
    tester.run_full_test
  when '2'
    tester.test_specific_geocoder_leak
  when '3'
    tester.force_garbage_collection
  when '4'
    tester.get_memory_stats
  else
    puts "Invalid choice. Running full test..."
    tester.run_full_test
  end
end