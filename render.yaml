services:
  # Single web service with integrated worker (foreman-based)
  - type: web
    name: unlisters-app
    env: ruby
    plan: standard
    packages:
      - name: libvips
      # - name: poppler-utils  # No longer needed - PDF thumbnails handled by Lambda microservice
    buildCommand: ./bin/render-build.sh
    startCommand: bundle exec foreman start -f Procfile
    envVars:
      - key: RAILS_ENV
        value: production
      - key: DATABASE_URL
        fromDatabase:
          name: unlisters-app-db
          property: connectionString
      - key: RAILS_MAX_THREADS
        value: 5
      - key: RAILS_DB_POOL_SIZE
        value: 10
      - key: RAILS_MASTER_KEY
        sync: false

  # REMOVED: Worker service no longer needed - functionality moved to main web service
  # Previous worker service can be deleted from Render dashboard
  # All background job processing now happens within the main web service container

databases:
  - name: unlisters-app-db
    databaseName: unlisters_app_production
    user: unlisters_app