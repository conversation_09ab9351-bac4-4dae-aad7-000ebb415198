class NotificationMailerPreview < ActionMailer::Preview
  
  # http://localhost:3000/rails/mailers/notification_mailer/access_request_notification
  def access_request_notification
    project = Project.last
    user = User.last
    NotificationMailer.access_request_notification(project, user)
  end
  
  # http://localhost:3000/rails/mailers/notification_mailer/admin_project_notification
  def admin_project_notification
    project = Project.last
    admin = User.where(role: 'super_boss').first
    current_user = User.last
    NotificationMailer.admin_project_notification(project, admin, current_user)
  end
  
  # http://localhost:3000/rails/mailers/notification_mailer/new_project_notification
  def new_project_notification
    project = Project.first
    user = User.last
    NotificationMailer.new_project_notification(project, user)
  end

  # http://localhost:3000/rails/mailers/notification_mailer/approved_access_notification
  def approved_access_notification
    project = Project.last
    user = User.last
    current_user = User.last
    NotificationMailer.approved_access_notification(project, user, current_user)
  end

end 