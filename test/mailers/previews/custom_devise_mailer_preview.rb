# Preview all emails at http://localhost:3000/rails/mailers/custom_devise_mailer/invitation_instructions
class CustomDeviseMailerPreview < ActionMailer::Preview
  def invitation_instructions
    # Set locale from params if provided
    I18n.locale = params[:locale] if params[:locale].present?
    
    # Use User.find_or_initialize_by to get a controlled user instance for the preview.
    # This approach avoids issues with User.first if it lacks necessary attributes or doesn't exist.
    user = User.find_or_initialize_by(email: '<EMAIL>') do |u|
      # Set any default attributes required by your User model if the user is new.
      # For example:
      # if u.new_record? && u.respond_to?(:password=)
      #   u.password = u.password_confirmation = "password_for_preview"
      # end
    end

    # A raw token is needed for generating the invitation link in the email.
    raw_invitation_token = 'raw_preview_invitation_token_12345'

    # Devise Invitable typically sets the following attributes on the user object
    # when an invitation is created and sent. The mailer view relies on these.
    
    # The `invitation_token` attribute on the model usually stores the digested version of the raw token.
    user.invitation_token = Devise.token_generator.digest(user.class, :invitation_token, raw_invitation_token)
    
    user.invitation_created_at = Time.current
    
    # Setting `invitation_sent_at` is critical to fix the "undefined method '+' for nil:NilClass" error,
    # as this attribute is used to calculate the invitation's expiration date.
    user.invitation_sent_at = Time.current
    
    # Set up a mock inviter with profile for preview
    inviter = User.find_or_initialize_by(email: '<EMAIL>') do |u|
      u.password = 'password123' if u.new_record?
    end
    
    # Save the inviter if it's a new record so we can create associations
    inviter.save! if inviter.new_record?
    
    # Create or find associated user_profile for the inviter
    inviter_profile = inviter.user_profile || inviter.create_user_profile(
      first_name: 'John',
      last_name: 'Doe',
      email: inviter.email
    )
    
    # Update profile names if they're different
    if inviter_profile.first_name != 'John' || inviter_profile.last_name != 'Doe'
      inviter_profile.update(first_name: 'John', last_name: 'Doe')
    end
    
    # Set the invited_by association
    user.invited_by = inviter

    # If the user instance is new and not yet persisted, ensure it has any other attributes
    # that might be accessed by the mailer template (e.g., user.name).
    if user.new_record?
      # Example: if your mailer uses user.name:
      # user.name = "Preview Invitee" unless user.respond_to?(:name) && user.name.present?
    end

    # Call the custom Devise mailer method with the prepared user object and the raw token.
    CustomDeviseMailer.invitation_instructions(user, raw_invitation_token)
  end
end 