# TDD GUIDE: Secure Inline File Display

**Objective:** This document provides a comprehensive, step-by-step plan for a coding agent to create the complete test suite for the "Secure Inline File Display" feature using strict Test-Driven Development (TDD).

**Core TDD Principles for the Agent:**
1.  **Red -> Green -> Refactor:** Your primary task is to write a single failing test (Red), then wait for the instruction to write the minimal implementation code to make it pass (Green).
2.  **No Implementation First:** Do not write any application code (in `app/`) until a failing test that requires it exists.
3.  **One Step at a Time:** Follow the sequence outlined below. Do not skip ahead.

---

### **Part 1: Unit Tests - The Token Service (Core Logic)**

**Goal:** To ensure the `SecureFileTokenService` correctly generates and validates secure, time-limited, and session-bound JWTs. This is the cryptographic foundation of the feature.

**Target File:** `spec/services/secure_file_token_service_spec.rb`

**Instructions:**
Create the file and write a complete RSpec `describe` block for `SecureFileTokenService`. The tests should not require a full Rails environment (e.g., no HTTP requests).

**Test Checklist:**

*   **`.generate_token` method:**
    *   [ ] **Test Case:** Given a valid file attachment and a user, it generates a non-empty string (the token).
    *   [ ] **Test Case:** The generated token's payload, when decoded, contains the correct `file_id`, `user_id`, and `project_id`.
    *   [ ] **Test Case:** The token's `exp` (expiration) claim is set to approximately 5 minutes in the future.
    *   [ ] **Test Case (Security):** The payload contains a unique `nonce` to prevent token replay attacks between requests.
    *   [ ] **Test Case (Security - Session Binding):** The payload contains a `session_fp` (session fingerprint) when a request with a session is provided.
        *   **Why it's important:** This fingerprint cryptographically ties the token to a specific user session, which is the core defense against URL sharing.
    *   [ ] **Test Case (Security - IP Binding):** The payload contains the user's `ip` address from the request.
        *   **Why it's important:** This provides an optional, additional layer of validation to restrict token usage to the original IP address.

*   **`.decode_token` method:**
    *   [ ] **Test Case:** It correctly decodes a valid, unexpired token, returning the payload hash.
    *   [ ] **Test Case (Security):** It returns `nil` for a token that has been tampered with (invalid signature).
    *   [ ] **Test Case (Security):** It returns `nil` for a token that is malformed.
    *   [ ] **Test Case (Security):** It returns `nil` for a token that is expired.

*   **`.validate_token_with_session` method (Critical Security Logic):**
    *   [ ] **Test Case (Success):** It returns `true` for a valid, unexpired token where the request's session fingerprint matches the one in the token.
    *   [ ] **Test Case (Security - URL Sharing Attack):** It returns `false` if the token contains a `session_fp` but the request has no session.
        *   **Why it's important:** This is the primary test for the vulnerability fix. It ensures that a token cannot be used without a corresponding browser session, blocking access from incognito windows or different browsers.
    *   [ ] **Test Case (Security - Session Mismatch):** It returns `false` if the request's session ID does not match the `session_fp` in the token.
        *   **Why it's important:** This prevents a logged-in user from using a token generated for another user's session.
    *   [ ] **Test Case (Security - Timing Attack Prevention):** It uses `ActiveSupport::SecurityUtils.secure_compare` for comparing session fingerprints. (This can be tested with an RSpec `expect(...).to receive(...)` spy).
        *   **Why it's important:** A standard string comparison (`==`) can be vulnerable to timing attacks, where an attacker measures response times to guess the fingerprint character by character. `secure_compare` takes constant time, mitigating this risk.
    *   [ ] **Test Case (Security - IP Mismatch):** With IP validation enabled, it returns `false` if the request's IP does not match the IP in the token.
    *   [ ] **Test Case (Edge Case - IP Validation Disabled):** With IP validation disabled, it returns `true` even if the request's IP does not match the IP in the token.
    *   [ ] **Test Case (Security - Expired Token):** It returns `false` for an expired token, even if the session fingerprint is valid.
        *   **Why it's important:** Ensures that the expiration check takes precedence and session binding doesn't create a loophole for expired tokens.

*   **`.token_valid?` method:**
    *   [ ] **Test Case:** It returns `true` for a valid, unexpired token.
    *   [ ] **Test Case:** It returns `false` for an expired token.
    *   [ ] **Test Case:** It returns `false` for an invalid or malformed token.

**Expected Initial Failure:** `NameError: uninitialized constant SecureFileTokenService`. This is the correct starting point.

---

### **Part 2: Model Tests - The Secure Hash Concern**

**Goal:** To ensure the `SecureFileAccess` concern can reliably generate consistent, secure hashes for files and find files using those hashes.

**Target File:** `spec/models/project_spec.rb`

**Instructions:**
Modify the existing `project_spec.rb` to add a `describe` block for the `SecureFileAccess` concern's functionality. This assumes the `Project` model will be the one including the concern.

**Test Checklist:**

*   **`#generate_secure_file_hash` method:**
    *   [ ] **Test Case:** It generates a 32-character string.
    *   [ ] **Test Case:** It always generates the *same* hash for the *same* file, ensuring consistency.
    *   [ ] **Test Case:** It generates a *different* hash for a *different* file.
*   **`#find_file_by_secure_hash` method:**
    *   [ ] **Test Case:** Given a valid hash for an attached file, it returns the correct `ActiveStorage::Attachment` object.
    *   [ ] **Test Case (Edge Case):** Given a hash that does not correspond to any file, it returns `nil`.

**Expected Initial Failure:** `NoMethodError` for `generate_secure_file_hash` on a `Project` instance.

---

### **Part 3: Request Tests - The API Endpoints (Security Gateway)**

**Goal:** To test the controller logic, focusing on security, authorization, and the full request/response cycle.

**Target File:** `spec/requests/private_files_spec.rb`

**Instructions:**
Create a new request spec file. These tests will simulate HTTP requests to your application.

**Test Checklist for `POST /projects/:id/request_file_token`:**

*   **Authorization & Authentication:**
    *   [ ] **Test Case (Security):** A request from an unauthenticated user is rejected (e.g., `401 Unauthorized` or redirect).
    *   [ ] **Test Case (Security):** A request from an authenticated user who does *not* have access to the project is rejected (`403 Forbidden`).
*   **Input Validation:**
    *   [ ] **Test Case (Edge Case):** A request with a missing `file_hash` is rejected (`400 Bad Request`).
    *   [ ] **Test Case (Edge Case):** A request with a `file_hash` that doesn't belong to a file on that project is rejected (`404 Not Found`).
*   **Success Case:**
    *   [ ] **Test Case:** A valid request from an authorized user returns a `200 OK` and a JSON body containing the `token`, `content_type`, and `expires_in`.
*   **Security Hardening:**
    *   [ ] **Test Case (Security):** Rapid requests from the same IP are rate-limited (`429 Too Many Requests`).

**Test Checklist for `GET /secure/stream`:**

*   **Token Validation:**
    *   [ ] **Test Case (Security):** A request without a token (`?t=`) is rejected (`403 Forbidden`).
    *   [ ] **Test Case (Security):** A request with an invalid, malformed, or expired token is rejected (`403 Forbidden`).
*   **Session Binding & Context Scenarios (Critical Security Tests):**
    *   [ ] **Test Case (Security - URL Sharing Attack):** A valid token is generated for a logged-in user. The same token is then used in a request *without* a valid session (simulating a different browser or incognito mode). The request is rejected with `403 Forbidden`.
        *   **Why it's important:** This is the end-to-end test for the session binding fix. It proves that a shared URL is useless to an unauthorized party.
    *   [ ] **Test Case (Security - Session Expiry / Logout):** A user generates a token, then logs out (destroying their session). An attempt to use the token afterwards is rejected with `403 Forbidden`.
        *   **Why it's important:** This ensures that logging out correctly invalidates all access tokens tied to that session, preventing access even within the token's short lifetime.
    *   [ ] **Test Case (Security - Re-authorization Check):** A token is generated for a user with valid permissions. The user's permissions are then revoked. An attempt to use the token is rejected with `403 Forbidden`.
        *   **Why it's important:** This confirms that permissions are checked "just-in-time" when the file is requested, not just when the token is created. This is a key defense-in-depth principle.
*   **Content-Specific Security Headers (CSP):**
    *   [ ] **Test Case (Security - PDF CSP):** A request for a PDF file returns a `Content-Security-Policy` header allowing `object-src 'self'`.
        *   **Why it's important:** Verifies that PDFs receive the specific, relaxed CSP required for in-browser rendering, preventing them from breaking.
    *   [ ] **Test Case (Security - Non-PDF CSP):** A request for an image (e.g., `image/png`) or other file type returns a strict `Content-Security-Policy` header containing the `sandbox` directive.
        *   **Why it's important:** Ensures that potentially executable files (like SVGs with embedded scripts) are served with maximum restrictions, preventing XSS attacks.
*   **Success Case & General Security Headers:**
    *   [ ] **Test Case:** A valid request from a user with a matching session streams the file with a `200 OK` status.
    *   [ ] **Test Case:** The response `Content-Type` header matches the file's actual content type.
    *   [ ] **Test Case (Security):** The response includes `Cache-Control: no-cache, no-store...` and `X-Content-Type-Options: nosniff` headers.
        *   **Why it's important:** `Cache-Control` prevents sensitive files from being stored on shared proxies or in the browser cache. `nosniff` prevents browsers from misinterpreting the file's content type, which can lead to XSS.

**Secure Logging & Parameter Filtering:**

*   [ ] **Test Case (Security):** Accessing `request_file_token` or `stream_content` does **not** write sensitive parameters like `:t`, `:token`, or `:file_hash` to the standard log output. (This tests `config/initializers/filter_parameter_logging.rb`).
*   [ ] **Test Case (Security):** A failed access attempt due to an invalid session writes a specific `[SECURE_FILE] stream_invalid_session` log entry.
*   [ ] **Test Case (Security):** Accessing a secure endpoint **silences** the default Rails "Processing by..." log entry for that action.

**Expected Initial Failure:** `ActionController::RoutingError`

---

### **Part 4: Feature Tests - End-to-End Security Scenarios**

**Goal:** To test the complete user flow and security scenarios from the browser perspective.

**Target File:** `spec/features/secure_file_display_spec.rb`

**Instructions:**
Create feature specs that simulate real user interactions with the secure file display system.

**Test Checklist:**

*   **User Journey Tests:**
    *   [ ] **Test Case:** User can click on a file icon and see the file displayed in a lightbox.
    *   [ ] **Test Case:** User can download a file using the secure download button.
    *   [ ] **Test Case:** Lightbox properly displays different file types (images, PDFs).

*   **Security Scenarios:**
    *   [ ] **Test Case (Security - URL Sharing):** User copies a secure file URL and opens it in a private/incognito window. The file should not be accessible.
    *   [ ] **Test Case (Security - Session Timeout):** User views a file, waits for session to expire, then tries to refresh the file URL. Access should be denied.
    *   [ ] **Test Case (Security - Permission Revocation):** Admin revokes user's access to a project while user has file open. Refreshing the file URL should deny access.

---

### **Security Testing Best Practices**

1. **Always Test the Attack Vector:** Don't just test that legitimate users can access files. Test that unauthorized users cannot.

2. **Test Edge Cases:** What happens with expired tokens? Missing parameters? Malformed data?

3. **Test Timing Attacks:** Ensure cryptographic comparisons use constant-time algorithms.

4. **Test Defense in Depth:** Even if one security layer fails, others should still protect the system.

5. **Document Security Rationale:** Each security test should explain WHY it's important, not just WHAT it tests.

---

**Next Steps:** Start with Part 1, implementing one test at a time following TDD principles.