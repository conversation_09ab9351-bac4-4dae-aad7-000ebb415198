# Lambda Thumbnail Integration Guide

This document describes the integration between AWS Lambda thumbnail generation service and the Rails application. The Lambda function processes PDF thumbnails while the Rails app continues to handle image thumbnails locally.

## Architecture Overview

### Thumbnail Processing Flow

1. **PDF Files** (Processed by Lambda):
   - User uploads PDF → Active Storage saves to S3 `uploads/` folder
   - S3 event triggers Lambda function 
   - Lambda generates PNG thumbnail → saves to S3 `thumbnails/` folder
   - Lambda sends webhook to Rails app with thumbnail metadata
   - <PERSON><PERSON> creates Active Storage blob and attaches thumbnail

2. **Image Files** (Processed by Rails):
   - User uploads image → Active Storage saves to S3 `uploads/` folder  
   - Rails PdfThumbnailGenerationJob processes images locally
   - Thumbnail saved to S3 `thumbnails/` folder via Active Storage

### Key Components

- **Lambda Function**: External service handling PDF thumbnail generation
- **Webhook Endpoint**: `/wh/thumb_ready` - receives thumbnail notifications
- **PdfThumbnailGenerationJob**: Modified to skip PDFs, process images only
- **S3 Structure**:
  - `uploads/` - Original files (PDFs and images)
  - `thumbnails/` - Generated thumbnails (from both Lambda and Rails)

## Implementation Status

### Completed
- [x] Lambda function deployed and processing PDFs
- [x] S3 event notifications configured for `uploads/*.pdf`
- [x] Webhook instructions documented

### To Implement
- [ ] Webhook route in Rails
- [ ] Webhook controller with security
- [ ] Rails credentials configuration
- [ ] Modify PdfThumbnailGenerationJob to skip PDFs
- [ ] Test end-to-end integration

## Rails Implementation Guide

### 1. Add Webhook Route

Add to `config/routes.rb`:

```ruby
Rails.application.routes.draw do
  # ... existing routes ...
  
  # Webhook endpoint for Lambda thumbnail notifications
  post '/wh/thumb_ready', to: 'webhooks/thumbnails#create'
end
```

### 2. Create Webhook Controller

Create `app/controllers/webhooks/thumbnails_controller.rb` with full security features:

```ruby
class Webhooks::ThumbnailsController < ApplicationController
  # Disable CSRF protection for webhook endpoint
  skip_before_action :verify_authenticity_token
  before_action :verify_webhook_signature!
  before_action :check_request_size
  before_action :rate_limit_webhook

  def create
    # Find the original blob by its S3 key
    original_blob = ActiveStorage::Blob.find_by(key: params.require(:original_blob_key))
    
    unless original_blob
      Rails.logger.warn "Webhook received for unknown original blob key: #{params[:original_blob_key]}"
      head :not_found
      return
    end

    # Find the parent record that has this blob attached
    parent_record = original_blob.attachments.first&.record
    
    unless parent_record
      Rails.logger.warn "Could not find parent record for blob key: #{params[:original_blob_key]}"
      head :unprocessable_entity
      return
    end

    # SECURITY: Check if thumbnail already exists (idempotency)
    if parent_record.thumbnail.attached?
      Rails.logger.info "Thumbnail already exists for #{parent_record.class.name} ##{parent_record.id}"
      head :ok
      return
    end

    # Create the thumbnail blob and attach it
    ActiveRecord::Base.transaction do
      thumbnail_params = params.require(:thumbnail).permit(:key, :filename, :content_type, :byte_size, :checksum)
      thumbnail_blob = ActiveStorage::Blob.create!(thumbnail_params)
      parent_record.thumbnail.attach(thumbnail_blob)
      Rails.logger.info "Successfully attached thumbnail to #{parent_record.class.name} ##{parent_record.id}"
    end

    head :ok
    
  rescue ActionController::ParameterMissing => e
    Rails.logger.error "Invalid webhook payload: #{e.message}"
    render json: { error: e.message }, status: :bad_request
    
  rescue ActiveRecord::RecordInvalid => e
    Rails.logger.error "Failed to create thumbnail blob: #{e.record.errors.full_messages}"
    render json: { error: e.record.errors.full_messages }, status: :unprocessable_entity
  end

  private

  def check_request_size
    if request.content_length > 5.kilobytes
      Rails.logger.warn "Webhook request too large: #{request.content_length} bytes"
      head :payload_too_large
      return
    end
  end

  def rate_limit_webhook
    # Simple rate limiting - uses rack-attack in production
    key = "webhook:#{request.remote_ip}"
    if Rails.cache.read(key).to_i >= 10 # Max 10 requests per minute
      Rails.logger.warn "Rate limit exceeded for webhook from #{request.remote_ip}"
      head :too_many_requests
      return
    end
    Rails.cache.write(key, Rails.cache.read(key).to_i + 1, expires_in: 1.minute)
  end

  def verify_webhook_signature!
    timestamp = request.headers['X-Signature-Timestamp']
    received_signature = request.headers['X-Signature-Hmac-Sha256']
    
    # Check if headers are present
    unless timestamp && received_signature
      Rails.logger.warn "Webhook missing signature headers"
      head :unauthorized
      return
    end

    # Check timestamp (prevent replay attacks)
    request_time = Time.at(timestamp.to_i)
    if request_time < 5.minutes.ago || request_time > 1.minute.from_now
      Rails.logger.warn "Webhook timestamp outside acceptable range: #{request_time}"
      head :unauthorized
      return
    end

    # Verify HMAC signature
    request_body = request.raw_post
    string_to_sign = "#{timestamp}.#{request_body}"
    
    expected_signature = OpenSSL::HMAC.hexdigest(
      'sha256',
      Rails.application.credentials.thumbnail_webhook_secret,
      string_to_sign
    )

    unless ActiveSupport::SecurityUtils.secure_compare(expected_signature, received_signature)
      Rails.logger.warn "Invalid webhook signature"
      head :unauthorized
      return
    end
  end
end
```

### 3. Configure Rails Credentials

Add webhook secret to credentials:

```bash
EDITOR=nano rails credentials:edit
```

Add:
```yaml
thumbnail_webhook_secret: [webhook_secret_from_lambda_deployment]
```

### 4. Modify PdfThumbnailGenerationJob

The existing `app/jobs/pdf_thumbnail_generation_job.rb` needs to be modified to skip PDF processing (handled by Lambda) while continuing to process images:

```ruby
class PdfThumbnailGenerationJob < ApplicationJob
  queue_as :default

  def perform(project)
    project.private_files.each do |file|
      next if file.blob.nil?
      
      # Skip if thumbnail already exists
      next if project.private_file_thumbnails.any? { |t| t.blob.filename.to_s == "#{file.blob.filename.base}_thumb.png" }
      
      # LAMBDA INTEGRATION: Skip PDF files - they are processed by Lambda
      if file.blob.content_type == "application/pdf"
        Rails.logger.info "Skipping PDF thumbnail generation for #{file.blob.filename} - handled by Lambda"
        next
      end
      
      # Continue processing image files locally
      if file.blob.content_type.start_with?("image/")
        process_image_thumbnail(project, file)
      end
    end
  end

  private

  def process_image_thumbnail(project, file)
    # Existing image thumbnail generation code
    # This continues to work as before for image files
  end
end
```

## Security Considerations

### Webhook Security Features
1. **HMAC Signature Verification**: SHA256 HMAC with shared secret
2. **Timestamp Validation**: 5-minute window to prevent replay attacks
3. **Request Size Limit**: Maximum 5KB payload
4. **Rate Limiting**: 10 requests per minute per IP
5. **Idempotency**: Prevents duplicate thumbnail creation

### S3 Permissions
Lambda has restricted permissions:
- **Read**: `uploads/*` (original PDFs only)
- **Write**: `thumbnails/*` (generated thumbnails)
- **HeadObject**: `thumbnails/*` (metadata collection)

## Testing Guide

### 1. Test Webhook Endpoint
```bash
# Test with curl (replace with actual values)
curl -X POST https://unlisters.com/wh/thumb_ready \
  -H "Content-Type: application/json" \
  -H "X-Signature-Timestamp: $(date +%s)" \
  -H "X-Signature-Hmac-Sha256: [calculated_signature]" \
  -d '{
    "original_blob_key": "uploads/test.pdf",
    "thumbnail": {
      "key": "thumbnails/test.png",
      "filename": "test.png",
      "content_type": "image/png",
      "byte_size": 12345,
      "checksum": "base64hash"
    }
  }'
```

### 2. Verify S3 Configuration
```bash
# Check Lambda triggers
aws s3api get-bucket-notification-configuration \
  --bucket floating-sierra-56086fd6-d570-470e-82e6-80e348975de7-dev
```

### 3. Test End-to-End Flow
1. Upload a PDF file through the Rails app
2. Monitor Lambda logs for processing
3. Check Rails logs for webhook receipt
4. Verify thumbnail attachment in Rails console

## Monitoring and Debugging

### Rails Logs
Monitor for:
- Webhook receipt: `"Webhook received for..."`
- Success: `"Successfully attached thumbnail..."`
- Errors: `"Invalid webhook payload..."`, `"Rate limit exceeded..."`

### Lambda Logs
Check CloudWatch for:
- PDF processing events
- Webhook delivery status
- Error messages

### Common Issues
1. **Missing thumbnail**: Check Lambda logs and S3 permissions
2. **Webhook failures**: Verify secret and signature calculation
3. **Rate limiting**: Check for excessive webhook calls
4. **Duplicate thumbnails**: Ensure idempotency check is working

## Rollback Plan

If Lambda integration fails:
1. Remove webhook route
2. Uncomment PDF processing in PdfThumbnailGenerationJob
3. Continue with local thumbnail generation

## Future Enhancements

1. **Webhook retry mechanism**: Handle temporary failures
2. **Dead letter queue**: For failed thumbnail notifications
3. **Metrics and monitoring**: Track success/failure rates
4. **Async webhook processing**: Move to background job for better performance