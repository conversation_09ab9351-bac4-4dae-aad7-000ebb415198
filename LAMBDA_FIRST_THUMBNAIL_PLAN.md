# Lambda-First Thumbnail Architecture Plan

**Created**: June 18, 2025  
**Status**: Planning Phase  
**Goal**: Cost-effective, reliable thumbnail generation using Lambda for ALL image types

## Current State Analysis ✅

### What's Working
- **Image thumbnails**: Work with `:async` mode in production (confirmed 2025-06-18)
- **PDF thumbnails**: Successfully handled by Lambda + webhook system
- **Email delivery**: <PERSON><PERSON><PERSON> working reliably for transactional emails
- **File uploads**: Active Storage + S3 working correctly
- **Development environment**: All thumbnail generation working locally

### Identified Issues
- **Production worker isolation**: `:external` mode causes thumbnail jobs to hang
- **Cost concerns**: Redis/Sidekiq adds $7/month for minimal job volume
- **Architectural complexity**: Mixed local + Lambda processing adds confusion

### Root Cause Confirmed
**Issue**: Worker process execution environment differs from web process  
**Evidence**: Images work with `:async` mode, fail with `:external` mode  
**Impact**: Thumbnails never complete, infinite job retries

## Proposed Lambda-First Architecture 🎯

### Design Principles
1. **Single Responsibility**: Lambda handles ALL thumbnail generation
2. **Cost Efficiency**: Avoid Redis, minimize background job complexity  
3. **Operational Simplicity**: One system for all thumbnails
4. **Reliability**: Proven Lambda system extended vs. debugging Rails worker issues

### Target Architecture

```
File Upload Flow:
┌─────────────────┐    ┌──────────────┐    ┌─────────────────┐
│   User Upload   │───▶│  S3 Bucket   │───▶│ Lambda Trigger  │
│   (Web Form)    │    │  (uploads)   │    │ (Any File Type) │
└─────────────────┘    └──────────────┘    └─────────────────┘
                                                     │
                                                     ▼
┌─────────────────┐    ┌──────────────┐    ┌─────────────────┐
│  Rails Webhook  │◀───│  S3 Bucket   │◀───│    Lambda       │
│  (Thumbnail     │    │ (thumbnails) │    │ - PDF: poppler  │
│   Attachment)   │    │              │    │ - IMG: sharp    │
└─────────────────┘    └──────────────┘    └─────────────────┘
```

### Benefits
- ✅ **Cost**: No Redis needed (~$84/year saved)
- ✅ **Reliability**: Eliminates worker execution environment issues
- ✅ **Consistency**: Same processing pipeline for all file types
- ✅ **Performance**: Lambda scales automatically, faster than Rails image processing
- ✅ **Maintenance**: Single Lambda function to maintain vs. Rails + Lambda

## Implementation Plan 📋

### Phase 1: Clean Current System (Day 1)
**Goal**: Stabilize current working state, document findings

**Tasks**:
1. **Revert async mode change**
   - [ ] Update `config/initializers/good_job.rb` back to conditional execution mode
   - [ ] Add clear documentation of execution mode issue
   - [ ] Test that PDFs still work via Lambda

2. **Document current working solutions** 
   - [ ] Update `CLAUDE.md` with execution mode findings
   - [ ] Document that images work with `:async` mode as emergency fallback
   - [ ] Update architecture decision records

3. **Clean up thumbnail job logic**
   - [ ] Add clear logging to identify when job runs vs. skips
   - [ ] Ensure job gracefully handles Lambda-processed files
   - [ ] Remove confusing temporary code/comments

### Phase 2: Extend Lambda Function (Days 2-3)
**Goal**: Single Lambda handles both PDF and image thumbnails

**Tasks**:
1. **Update Lambda function** 
   - [ ] Add image processing capability (sharp library)
   - [ ] Handle multiple file extensions (jpg, png, gif, pdf)
   - [ ] Maintain same webhook callback for all types
   - [ ] Test with sample images

2. **File type detection logic**
   ```javascript
   // Pseudo-code
   if (file.endsWith('.pdf')) {
     // Use existing poppler logic
   } else if (imageExtensions.includes(ext)) {
     // Use sharp for images  
   }
   ```

3. **Update webhook endpoint**
   - [ ] Verify `/wh/thumb_ready` handles both PDF and image callbacks
   - [ ] Ensure security (HMAC) works for both types
   - [ ] Test idempotency for image thumbnails

### Phase 3: Rails Integration (Day 4)
**Goal**: Rails reliably delegates to Lambda, fallback to local only if needed

**Tasks**:
1. **Update ThumbnailGenerationJob**
   - [ ] Skip processing for file types handled by Lambda
   - [ ] Add clear logging: "Delegating to Lambda" vs "Processing locally"
   - [ ] Keep local processing as emergency fallback only

2. **Update file upload workflow**
   - [ ] Ensure S3 upload triggers Lambda for all supported types
   - [ ] Verify thumbnail association works via webhook
   - [ ] Test end-to-end: upload → Lambda → webhook → thumbnail display

3. **Configuration management**
   - [ ] Environment variables for Lambda delegation (enable/disable)
   - [ ] Graceful degradation if Lambda unavailable

### Phase 4: Testing & Validation (Day 5)
**Goal**: Verify system works reliably across all scenarios

**Tasks**:
1. **Comprehensive testing**
   - [ ] Upload various image types (jpg, png, gif)
   - [ ] Upload PDF files  
   - [ ] Test in development environment
   - [ ] Test in production environment
   - [ ] Test thumbnail display in UI

2. **Performance validation**
   - [ ] Measure Lambda processing time vs. Rails processing
   - [ ] Verify S3 upload costs remain reasonable
   - [ ] Monitor webhook response times

3. **Fallback testing**
   - [ ] Disable Lambda, verify Rails fallback works
   - [ ] Test error scenarios (corrupted files, timeouts)
   - [ ] Verify job retry logic

### Phase 5: Cleanup & Documentation (Day 6)
**Goal**: Clean, maintainable, well-documented system

**Tasks**:
1. **Code cleanup**
   - [ ] Remove unnecessary thumbnail job complexity
   - [ ] Clean up unused configuration options
   - [ ] Update comments and documentation

2. **Documentation updates**
   - [ ] Update `LAMBDA_THUMBNAIL_INTEGRATION.md`
   - [ ] Update `CLAUDE.md` with final architecture
   - [ ] Create troubleshooting guide for common issues

3. **Monitoring setup**
   - [ ] CloudWatch alerts for Lambda errors
   - [ ] Rails logging for thumbnail workflow
   - [ ] Health check endpoints

## Architecture Decisions 📚

### Key Decisions Made
1. **Lambda-First**: All thumbnails processed by Lambda, not Rails workers
2. **Cost-Conscious**: Avoid Redis/Sidekiq for low-volume background jobs  
3. **Execution Mode**: Keep `:external` for production emails, `:async` for development
4. **Fallback Strategy**: Rails can process locally if Lambda unavailable

### Technology Choices
- **Lambda Runtime**: Node.js (existing, working)
- **Image Processing**: Sharp library (fast, reliable)
- **PDF Processing**: Poppler (existing, working)
- **Communication**: S3 → Lambda → Webhook (existing pattern)
- **Background Jobs**: GoodJob for emails only

### Risk Mitigation
- **Lambda Cold Starts**: Pre-warm with CloudWatch scheduled events if needed
- **Cost Control**: Monitor S3 API calls and Lambda execution time
- **Reliability**: Rails fallback processing for critical situations
- **Debugging**: Comprehensive logging at each step

## Success Metrics 🎯

### Immediate Success (Week 1)
- [ ] All thumbnails generate reliably in production
- [ ] No hanging background jobs
- [ ] Image and PDF thumbnails work consistently

### Long-term Success (Month 1) 
- [ ] Zero manual intervention needed for thumbnail issues
- [ ] Clear monitoring shows successful processing rates >99%
- [ ] Cost remains under $10/month for thumbnail generation

### Performance Targets
- **Lambda Processing**: <10 seconds for typical images
- **Webhook Response**: <30 seconds from upload to thumbnail availability
- **UI Loading**: Thumbnails display immediately when available

## Current Status: Planning Complete ✅

**Next Action**: Begin Phase 1 implementation
**Owner**: Development team  
**Timeline**: 6 days estimated
**Dependencies**: None - builds on existing working Lambda system

---

*This plan prioritizes working solutions over theoretical optimizations, maintains cost efficiency, and builds incrementally on proven components.*