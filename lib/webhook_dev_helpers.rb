# 🚀 WEBHOOK DEVELOPMENT HELPERS
# Load in Rails console with: require_relative 'lib/webhook_dev_helpers'
# Then use: WebhookHelpers.quick_test

module WebhookHelpers
  extend self
  
  # 🎯 ZERO-INPUT QUICK TEST
  def quick_test
    puts "🚀 Quick Webhook Test - Finding first available file..."
    
    project = Project.joins(:private_files_attachments).includes(private_files_attachments: :blob).first
    unless project
      puts "❌ No projects with files found"
      return false
    end
    
    file = project.private_files_attachments.first
    blob_key = file.blob.key
    
    puts "📄 Testing: #{file.blob.filename} from Project ##{project.id}"
    
    # Clean existing thumbnail
    existing = project.thumbnail_for_file(file)
    if existing
      puts "🗑️  Removing existing thumbnail..."
      existing.purge
    end
    
    # Test webhook
    success = test_webhook(blob_key)
    
    if success
      puts "✅ SUCCESS! Thumbnail should be attached now."
      puts "🔍 Verify: Project.find(#{project.id}).pdf_thumbnails.count"
    else
      puts "❌ FAILED! Check server logs."
    end
    
    success
  end
  
  # 🎯 TEST SPECIFIC PROJECT
  def test_project(project_id)
    project = Project.find(project_id)
    
    unless project.private_files.any?
      puts "❌ Project ##{project_id} has no files"
      return false
    end
    
    file = project.private_files_attachments.first
    blob_key = file.blob.key
    
    puts "📄 Testing: #{file.blob.filename}"
    
    success = test_webhook(blob_key)
    puts success ? "✅ SUCCESS!" : "❌ FAILED!"
    success
  end
  
  # 🎭 CREATE FAKE THUMBNAIL FOR UI TESTING
  def fake_thumbnail(project_id)
    project = Project.find(project_id)
    
    unless project.private_files.any?
      puts "❌ Project has no files to create thumbnail for"
      return false
    end
    
    file = project.private_files_attachments.first
    
    puts "🎭 Creating fake thumbnail for: #{file.blob.filename}"
    
    fake_blob = ActiveStorage::Blob.create!(
      key: "console-fake-#{Time.now.to_i}-#{SecureRandom.hex(6)}",
      filename: "thumb_#{file.blob.filename.base}.png",
      content_type: 'image/png',
      byte_size: 35000,
      checksum: Base64.strict_encode64(SecureRandom.bytes(16)),
      service_name: :amazon_thumbnails
    )
    
    project.pdf_thumbnails.attach(fake_blob)
    puts "✅ Fake thumbnail created! Check UI now."
    true
  end
  
  # 🔍 SHOW ALL PROJECTS WITH FILES
  def show_available
    projects = Project.joins(:private_files_attachments)
                     .includes(private_files_attachments: :blob, :pdf_thumbnails_attachments)
    
    if projects.empty?
      puts "❌ No projects with files found"
      return
    end
    
    puts "📊 AVAILABLE PROJECTS FOR TESTING:"
    puts "=" * 50
    
    projects.each do |project|
      files_count = project.private_files.count
      thumbs_count = project.pdf_thumbnails.count
      
      first_file = project.private_files_attachments.first
      
      puts "Project ##{project.id}: #{project.title.presence || 'Untitled'}"
      puts "  📄 Files: #{files_count} | 🖼️  Thumbnails: #{thumbs_count}"
      puts "  📎 First file: #{first_file.blob.filename}"
      puts "  🔑 Blob key: #{first_file.blob.key}"
      puts ""
    end
    
    puts "💡 Use: WebhookHelpers.test_project(PROJECT_ID)"
  end
  
  # 🧪 GENERATE CURL COMMAND
  def curl_command(blob_key = nil)
    unless blob_key
      project = Project.joins(:private_files_attachments).first
      unless project
        puts "❌ No files found and no blob_key provided"
        return
      end
      blob_key = project.private_files_attachments.first.blob.key
    end
    
    payload = {
      original_blob_key: blob_key,
      thumbnail: {
        key: "console-test-#{Time.now.to_i}.png",
        filename: "test_thumb.png",
        content_type: "image/png",
        byte_size: 35000,
        checksum: Base64.strict_encode64(SecureRandom.bytes(16))
      }
    }.to_json
    
    secret = Rails.application.credentials.thumbnail_webhook_secret
    timestamp = Time.now.to_i
    signature = OpenSSL::HMAC.hexdigest('sha256', secret, "#{timestamp}.#{payload}")
    
    puts "🔧 CURL COMMAND:"
    puts "=" * 40
    puts "curl -X POST http://localhost:5000/wh/thumb_ready \\"
    puts "  -H 'Content-Type: application/json' \\"
    puts "  -H 'X-Signature-Timestamp: #{timestamp}' \\"
    puts "  -H 'X-Signature-HMAC-SHA256: #{signature}' \\"
    puts "  -d '#{payload}'"
  end
  
  # 🔄 CLEAN ALL THUMBNAILS (for testing)
  def clean_all_thumbnails
    puts "🗑️  Removing all thumbnails for clean testing..."
    
    ActiveStorage::Attachment.joins(:blob)
                            .where(name: 'pdf_thumbnails')
                            .includes(:blob)
                            .find_each do |attachment|
      attachment.purge
    end
    
    puts "✅ All thumbnails removed!"
  end
  
  # 📊 STATS
  def stats
    projects_total = Project.count
    projects_with_files = Project.joins(:private_files_attachments).count
    projects_with_thumbs = Project.joins(:pdf_thumbnails_attachments).count
    total_files = ActiveStorage::Attachment.where(name: 'private_files').count
    total_thumbs = ActiveStorage::Attachment.where(name: 'pdf_thumbnails').count
    
    puts "📊 WEBHOOK TESTING STATS:"
    puts "=" * 30
    puts "Projects total: #{projects_total}"
    puts "Projects with files: #{projects_with_files}"
    puts "Projects with thumbnails: #{projects_with_thumbs}"
    puts "Total files: #{total_files}"
    puts "Total thumbnails: #{total_thumbs}"
    puts ""
    puts "💡 Coverage: #{projects_with_thumbs}/#{projects_with_files} projects have thumbnails"
  end
  
  private
  
  def test_webhook(blob_key)
    require 'net/http'
    require 'openssl'
    
    # Build payload
    payload = {
      original_blob_key: blob_key,
      thumbnail: {
        key: "console-test-#{Time.now.to_i}-#{SecureRandom.hex(6)}.png",
        filename: "console_test_thumb.png",
        content_type: "image/png",
        byte_size: rand(25000..45000),
        checksum: Base64.strict_encode64(SecureRandom.bytes(16))
      }
    }.to_json
    
    # Generate signature
    secret = Rails.application.credentials.thumbnail_webhook_secret
    unless secret
      puts "❌ thumbnail_webhook_secret not found"
      return false
    end
    
    timestamp = Time.now.to_i
    signature = OpenSSL::HMAC.hexdigest('sha256', secret, "#{timestamp}.#{payload}")
    
    # Send request
    begin
      uri = URI.parse("http://localhost:5000/wh/thumb_ready")
      http = Net::HTTP.new(uri.host, uri.port)
      http.read_timeout = 10
      
      request = Net::HTTP::Post.new(uri.request_uri)
      request['Content-Type'] = 'application/json'
      request['X-Signature-Timestamp'] = timestamp
      request['X-Signature-HMAC-SHA256'] = signature
      request.body = payload
      
      puts "🚀 Sending webhook..."
      response = http.request(request)
      
      puts "📊 Response: #{response.code} #{response.message}"
      response.code == '200'
    rescue => e
      puts "❌ Request failed: #{e.message}"
      puts "💡 Make sure server is running: bin/dev"
      false
    end
  end
end

# Auto-load message
puts "🚀 Webhook Helpers Loaded!"
puts "=" * 30
puts "Quick commands:"
puts "WebhookHelpers.quick_test        # Zero-input test"
puts "WebhookHelpers.show_available    # List all projects"  
puts "WebhookHelpers.test_project(id)  # Test specific project"
puts "WebhookHelpers.fake_thumbnail(id) # Create fake thumbnail"
puts "WebhookHelpers.stats             # Show stats"
puts "WebhookHelpers.curl_command      # Generate curl"