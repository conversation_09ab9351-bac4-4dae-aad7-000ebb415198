namespace :storage do
  
  # rake storage:check_orphaned_files
  desc "Check for orphaned Active Storage files"
  
  task check_orphaned_files: :environment do
    # This is a simple check that looks for blob records not attached to anything
    orphaned_blobs = ActiveStorage::Blob.left_joins(:attachments)
                                        .where(active_storage_attachments: { id: nil })
    
    if orphaned_blobs.any?
      puts "Found #{orphaned_blobs.count} orphaned blobs"
      # Clean them up or log them
      orphaned_blobs.each do |blob|
        puts "Orphaned blob ID: #{blob.id}, filename: #{blob.filename}"
        blob.purge
      end
    end
  end
end