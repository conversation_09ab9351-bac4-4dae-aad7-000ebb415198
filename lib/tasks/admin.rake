namespace :admin do

  desc "Create or update an admin user with specified role"
  
  # Create superadmin: rake admin:create[<EMAIL>,super_boss]
  # Create admin: rake admin:create[<EMAIL>,admin]
   
  task :create, [:email, :role] => :environment do |t, args|
    email = args[:email]
    role = args[:role] || 'super_boss' 
    
    unless email
      puts "Error: Email required. Usage: rake admin:create[<EMAIL>,super_boss]"
      exit
    end
    
    unless User.roles.keys.include?(role)
      puts "Error: Invalid role. Valid roles are: #{User.roles.keys.join(', ')}"
      exit
    end
    
    ActiveRecord::Base.transaction do
      # Find or create User with specified role
      user = User.find_by(email: email)
      if user
        user.update!(role: role)
        puts "User #{email} updated to #{role} role"
      else
        # Generate a secure password
        password = SecureRandom.hex(12)
        user = User.new(
          email: email,
          password: password,
          password_confirmation: password,
          role: role
        )
        
        # Skip confirmation if the User model requires it
        user.skip_confirmation! if user.respond_to?(:skip_confirmation!)
        user.save!
        
        puts "User #{email} created with role #{role} and password: #{password}"
        puts "NOTE: This password is only shown once! Please change it immediately after first login."
      end
      
      # Find or create AdminUser with same email for ActiveAdmin access
      admin = AdminUser.find_by(email: email)
      if admin
        puts "AdminUser #{email} already exists"
      else
        # Use the same password as the user if we just created one
        # password = defined?(password) ? password : SecureRandom.hex(12)
        password = SecureRandom.hex(12)
        puts "defined password? #{defined?(password)}"
        puts SecureRandom.hex(12)
        puts "----- password: #{password}"
        AdminUser.create!(
          email: email,
          password: password,
          password_confirmation: password
        )
        puts "AdminUser #{email} created with admin access"
      end
      
      puts "Admin setup complete for #{email} with role: #{role}"
    end
  end
end