require 'net/http'
require 'openssl'
require 'uri'

namespace :webhook do
  desc "🚀 Auto-test webhook with first available file (ZERO INPUT REQUIRED)"
  task :auto_test => :environment do
    puts "🔍 Finding first available file for webhook testing..."
    
    # Find first project with private files
    project_with_files = Project.joins(:private_files_attachments)
                               .includes(private_files_attachments: :blob)
                               .first
    
    unless project_with_files
      puts "❌ No projects with private files found. Upload a file first."
      exit 1
    end
    
    first_file = project_with_files.private_files_attachments.first
    blob_key = first_file.blob.key
    
    puts "📄 Auto-selected: #{first_file.blob.filename} from Project ##{project_with_files.id}"
    puts "🔑 Blob key: #{blob_key}"
    
    # Check if thumbnail already exists
    existing_thumbnail = project_with_files.thumbnail_for_file(first_file)
    if existing_thumbnail
      puts "⚠️  Thumbnail already exists - removing for clean test"
      existing_thumbnail.purge
      puts "🗑️  Existing thumbnail removed"
    end
    
    # Run the webhook test
    success = WebhookAutoTester.new.test_webhook(blob_key)
    
    if success
      puts "✅ AUTO-TEST COMPLETED SUCCESSFULLY!"
      puts "🔍 Verify: Project.find(#{project_with_files.id}).pdf_thumbnails.count should be > 0"
    else
      puts "❌ AUTO-TEST FAILED!"
    end
  end
  
  desc "🎯 Test webhook for specific project (just provide project ID)"
  task :test_project, [:project_id] => :environment do |_, args|
    project_id = args[:project_id] || Project.joins(:private_files_attachments).first&.id
    
    unless project_id
      puts "❌ No project ID provided and no projects with files found"
      exit 1
    end
    
    project = Project.find(project_id)
    unless project.private_files.any?
      puts "❌ Project ##{project_id} has no private files"
      exit 1
    end
    
    first_file = project.private_files_attachments.first
    blob_key = first_file.blob.key
    
    puts "📄 Testing with: #{first_file.blob.filename} from Project ##{project.id}"
    
    success = WebhookAutoTester.new.test_webhook(blob_key)
    
    if success
      puts "✅ PROJECT TEST COMPLETED!"
    else
      puts "❌ PROJECT TEST FAILED!"
    end
  end
  
  desc "🔄 Test ALL files (batch test all projects with files)"
  task :test_all => :environment do
    puts "🔍 Finding all projects with private files..."
    
    projects_with_files = Project.joins(:private_files_attachments)
                                .includes(private_files_attachments: :blob)
                                .limit(10) # Safety limit
    
    if projects_with_files.empty?
      puts "❌ No projects with files found"
      exit 1
    end
    
    puts "📊 Found #{projects_with_files.count} projects with files"
    
    tester = WebhookAutoTester.new
    success_count = 0
    
    projects_with_files.each_with_index do |project, index|
      puts "\n#{index + 1}/#{projects_with_files.count} Testing Project ##{project.id}..."
      
      first_file = project.private_files_attachments.first
      blob_key = first_file.blob.key
      
      puts "  📄 File: #{first_file.blob.filename}"
      
      success = tester.test_webhook(blob_key)
      success_count += 1 if success
      
      # Small delay between requests
      sleep 0.5
    end
    
    puts "\n📊 BATCH TEST RESULTS:"
    puts "✅ Successful: #{success_count}/#{projects_with_files.count}"
    puts "❌ Failed: #{projects_with_files.count - success_count}/#{projects_with_files.count}"
  end
  
  desc "💾 Create fake thumbnails for ALL projects (UI testing)"
  task :fake_all_thumbs => :environment do
    puts "🎭 Creating fake thumbnails for all projects with files..."
    
    projects = Project.joins(:private_files_attachments).includes(:pdf_thumbnails_attachments)
    
    projects.each do |project|
      # Skip if already has thumbnails
      if project.pdf_thumbnails.any?
        puts "⏭️  Project ##{project.id} already has thumbnails, skipping"
        next
      end
      
      first_file = project.private_files_attachments.first
      puts "📄 Creating fake thumbnail for Project ##{project.id}: #{first_file.blob.filename}"
      
      fake_blob = ActiveStorage::Blob.create!(
        key: "auto-fake-thumb-#{SecureRandom.hex(12)}",
        filename: "thumb_#{first_file.blob.filename.base}.png",
        content_type: 'image/png',
        byte_size: rand(25000..45000),
        checksum: Base64.strict_encode64(SecureRandom.bytes(16)),
        service_name: :amazon_thumbnails
      )
      
      project.pdf_thumbnails.attach(fake_blob)
      puts "✅ Fake thumbnail attached to Project ##{project.id}"
    end
    
    puts "🎭 Fake thumbnail creation completed!"
  end
  
  desc "🎯 Process webhooks for latest uploads WITHOUT thumbnails"
  task :process_latest => :environment do
    puts "🔍 Finding latest uploads without thumbnails..."
    
    # Find recent uploads (last 7 days) that don't have thumbnails
    recent_projects = Project.joins(:private_files_attachments)
                            .includes(:private_files_attachments => :blob, 
                                    :pdf_thumbnails_attachments => :blob)
                            .where('active_storage_attachments.created_at > ?', 7.days.ago)
                            .order('active_storage_attachments.created_at DESC')
    
    files_needing_thumbnails = []
    
    recent_projects.each do |project|
      project.private_files_attachments.each do |file_attachment|
        # Check if this specific file has a thumbnail
        has_thumbnail = project.thumbnail_for_file(file_attachment).present?
        
        unless has_thumbnail
          files_needing_thumbnails << {
            project: project,
            attachment: file_attachment,
            blob_key: file_attachment.blob.key,
            filename: file_attachment.blob.filename.to_s,
            uploaded_at: file_attachment.created_at
          }
        end
      end
    end
    
    files_needing_thumbnails = files_needing_thumbnails.sort_by { |f| f[:uploaded_at] }.reverse.first(20)
    
    if files_needing_thumbnails.empty?
      puts "✅ All recent uploads already have thumbnails!"
      exit 0
    end
    
    puts "📊 Found #{files_needing_thumbnails.count} recent uploads needing thumbnails"
    
    tester = WebhookAutoTester.new
    success_count = 0
    
    files_needing_thumbnails.each_with_index do |file_data, index|
      puts "\n#{index + 1}/#{files_needing_thumbnails.count} Processing: #{file_data[:filename]}"
      puts "  🏠 Project ##{file_data[:project].id}"
      puts "  📅 Uploaded: #{file_data[:uploaded_at].strftime('%Y-%m-%d %H:%M')}"
      
      success = tester.test_webhook(file_data[:blob_key])
      success_count += 1 if success
      
      sleep 0.3 # Rate limiting
    end
    
    puts "\n📊 RESULTS: #{success_count}/#{files_needing_thumbnails.count} thumbnails created!"
    puts "🎉 #{success_count} new thumbnails saved to your local database!"
  end
  
  desc "🔧 Create thumbnails DIRECTLY in database (bypasses S3 file verification)"
  task :create_direct => :environment do
    puts "🔧 Creating thumbnails directly in database (bypassing S3 verification)..."
    
    # Find files without thumbnails
    projects_needing_thumbs = Project.joins(:private_files_attachments)
                                    .includes(:private_files_attachments => :blob, :pdf_thumbnails_attachments => :blob)
                                    .where.not(id: Project.joins(:pdf_thumbnails_attachments).select(:id))
                                    .limit(10)
    
    if projects_needing_thumbs.empty?
      puts "✅ All projects already have thumbnails!"
      exit 0
    end
    
    puts "📊 Found #{projects_needing_thumbs.count} projects needing thumbnails"
    
    success_count = 0
    
    projects_needing_thumbs.each do |project|
      first_file = project.private_files_attachments.first
      
      puts "📄 Creating thumbnail for: #{first_file.blob.filename} (Project ##{project.id})"
      
      begin
        ActiveRecord::Base.transaction do
          # Create blob without triggering S3 identification
          thumbnail_blob = ActiveStorage::Blob.new(
            key: "direct-dev-thumb-#{Time.now.to_i}-#{SecureRandom.hex(8)}.png",
            filename: "thumb_#{first_file.blob.filename.base}.png",
            content_type: 'image/png',
            byte_size: rand(25000..45000),
            checksum: Base64.strict_encode64(SecureRandom.bytes(16)),
            service_name: :amazon_thumbnails
          )
          
          # Mark as identified to prevent S3 calls
          # Mark as identified AND analyzed to prevent S3 calls
          thumbnail_blob.identified = true
          thumbnail_blob.metadata = {
            "identified" => true,
            "analyzed" => true,
            "width" => 200,
            "height" => 200
          }
          thumbnail_blob.save!
          
          # Create attachment directly
          ActiveStorage::Attachment.create!(
            name: 'pdf_thumbnails',
            record: project,
            blob: thumbnail_blob
          )
          
          puts "  ✅ Direct thumbnail created successfully"
          success_count += 1
        end
      rescue => e
        puts "  ❌ Failed: #{e.message}"
      end
    end
    
    puts "\n📊 DIRECT CREATION RESULTS:"
    puts "✅ Success: #{success_count}/#{projects_needing_thumbs.count}"
    puts "🎉 #{success_count} thumbnails created directly in database!"
    puts "💡 These thumbnails work for UI testing but don't have real S3 files"
  end
  
  desc "🎯 SIMPLE: Create thumbnails for latest uploads (RECOMMENDED)"
  task :simple_create => :environment do
    puts "🎯 Simple thumbnail creation for uploaded files..."
    
    # Find files that need thumbnails using the same logic as the script
    recent_uploads = Project.joins(:private_files_attachments)
                           .includes(private_files_attachments: :blob, pdf_thumbnails_attachments: :blob)
                           .where('active_storage_attachments.created_at > ?', 7.days.ago)
                           .order('active_storage_attachments.created_at DESC')
                           .limit(20)
    
    files_needing_thumbnails = []
    
    recent_uploads.each do |project|
      project.private_files_attachments.each do |file_attachment|
        has_thumbnail = project.thumbnail_for_file(file_attachment).present?
        
        unless has_thumbnail
          blob = file_attachment.blob
          supported_types = ['application/pdf', 'image/jpeg', 'image/png', 'image/webp', 'image/gif']
          
          if blob.content_type && supported_types.any? { |type| blob.content_type.include?(type) }
            files_needing_thumbnails << {
              project: project,
              attachment: file_attachment,
              blob_key: blob.key,
              filename: blob.filename.to_s,
              content_type: blob.content_type
            }
          end
        end
      end
    end
    
    files_needing_thumbnails = files_needing_thumbnails.uniq { |f| f[:blob_key] }
    
    if files_needing_thumbnails.empty?
      puts "✅ All files already have thumbnails!"
      exit 0
    end
    
    puts "📊 Found #{files_needing_thumbnails.count} files needing thumbnails"
    
    success_count = 0
    
    files_needing_thumbnails.each do |file_info|
      puts "📄 Processing: #{file_info[:filename]}"
      
      # Generate thumbnail data matching Lambda pattern
      # Lambda pattern: original blob key with .pdf/.jpg replaced by .png
      original_key = file_info[:blob_key]
      thumbnail_key = original_key.gsub(/\.(pdf|jpg|jpeg|png|gif|webp)$/i, '.png')
      thumbnail_filename = File.basename(thumbnail_key)
      
      # Get actual thumbnail size from S3 if it exists
      thumbnail_byte_size = nil
      begin
        s3_service = ActiveStorage::Blob.services.fetch(:amazon_thumbnails)
        s3_object = s3_service.send(:object_for, thumbnail_key)
        if s3_object.exists?
          thumbnail_byte_size = s3_object.content_length
          puts "  Found existing thumbnail in S3: #{thumbnail_byte_size} bytes"
        end
      rescue => e
        puts "  Could not check S3: #{e.message}"
      end
      
      # If no S3 file found, use realistic fake size
      if thumbnail_byte_size.nil?
        if file_info[:content_type] == 'application/pdf'
          thumbnail_byte_size = rand(20_000..50_000)
        else
          thumbnail_byte_size = rand(10_000..40_000)
        end
      end
      
      fake_content = "dev-thumbnail-#{Time.now.to_f}-#{rand(10000)}"
      md5_hash = Digest::MD5.digest(fake_content)
      thumbnail_checksum = Base64.encode64(md5_hash).strip
      
      begin
        ActiveRecord::Base.transaction do
          thumbnail_blob = ActiveStorage::Blob.new(
            key: thumbnail_key,
            filename: thumbnail_filename,
            content_type: 'image/png',
            byte_size: thumbnail_byte_size,
            checksum: thumbnail_checksum,
            service_name: :amazon_thumbnails
          )
          
          # Mark as identified AND analyzed to prevent S3 calls
          thumbnail_blob.identified = true
          thumbnail_blob.metadata = {
            "identified" => true,
            "analyzed" => true,
            "width" => 200,
            "height" => 200
          }
          thumbnail_blob.save!
          
          file_info[:project].pdf_thumbnails.attach(thumbnail_blob)
          
          puts "  ✅ Thumbnail created for Project ##{file_info[:project].id}"
          success_count += 1
        end
      rescue => e
        puts "  ❌ Failed: #{e.message}"
      end
    end
    
    puts "\n🎉 SIMPLE CREATION COMPLETED!"
    puts "✅ Success: #{success_count}/#{files_needing_thumbnails.count}"
    puts "💡 Check your Rails app - thumbnails should now appear!"
  end
end

# Helper class for webhook testing
class WebhookAutoTester
  def test_webhook(original_blob_key)
    # Validate
    blob = ActiveStorage::Blob.find_by(key: original_blob_key)
    unless blob
      puts "❌ Blob not found: #{original_blob_key}"
      return false
    end
    
    # Build payload
    payload = build_payload(original_blob_key, blob.filename)
    
    # Generate signature
    signature_data = generate_signature(payload)
    unless signature_data
      puts "❌ Could not generate signature (check credentials)"
      return false
    end
    
    # Send request
    success = send_webhook_request(payload, signature_data)
    
    if success
      puts "✅ Webhook sent successfully"
      verify_attachment(blob)
    else
      puts "❌ Webhook failed"
    end
    
    success
  end
  
  private
  
  def build_payload(original_blob_key, original_filename)
    {
      original_blob_key: original_blob_key,
      thumbnail: {
        key: "auto-test-thumb-#{Time.now.to_i}-#{SecureRandom.hex(8)}.png",
        filename: "thumb_#{original_filename.base}.png",
        content_type: "image/png",
        byte_size: rand(25000..45000),
        checksum: Base64.strict_encode64(SecureRandom.bytes(16))
      }
    }.to_json
  end
  
  def generate_signature(payload)
    secret = Rails.application.credentials.thumbnail_webhook_secret
    unless secret
      puts "❌ thumbnail_webhook_secret not found in credentials"
      return nil
    end
    
    timestamp = Time.now.to_i
    signature = OpenSSL::HMAC.hexdigest('sha256', secret, "#{timestamp}.#{payload}")
    
    { timestamp: timestamp, signature: signature }
  end
  
  def send_webhook_request(payload, signature_data)
    begin
      uri = URI.parse("http://localhost:5000/wh/thumb_ready")
      
      http = Net::HTTP.new(uri.host, uri.port)
      http.read_timeout = 10
      
      request = Net::HTTP::Post.new(uri.request_uri)
      request['Content-Type'] = 'application/json'
      request['X-Signature-Timestamp'] = signature_data[:timestamp]
      request['X-Signature-HMAC-SHA256'] = signature_data[:signature]
      request.body = payload
      
      puts "🚀 Sending webhook request..."
      response = http.request(request)
      
      puts "📊 Response: #{response.code} #{response.message}"
      puts "📝 Body: #{response.body}" if response.body.present? && response.code != '200'
      
      response.code == '200'
    rescue => e
      puts "❌ Request failed: #{e.message}"
      puts "💡 Make sure Rails server is running on localhost:5000"
      false
    end
  end
  
  def verify_attachment(original_blob)
    parent = original_blob.attachments.first&.record
    if parent&.respond_to?(:pdf_thumbnails)
      thumbnail_count = parent.pdf_thumbnails.count
      if thumbnail_count > 0
        puts "✅ Thumbnail attached! (#{thumbnail_count} total thumbnails)"
        return true
      end
    end
    
    puts "⚠️ No thumbnail found after webhook"
    false
  end
end