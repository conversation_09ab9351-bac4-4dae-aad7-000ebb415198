# ABOUTME: Rake tasks for cleaning up stuck uploads, orphaned files, and temp files
# ABOUTME: Provides safe manual cleanup with detailed reporting before automating with cron

namespace :cleanup do
  desc "Comprehensive cleanup of stuck uploads and orphaned files"
  task uploads: :environment do
    puts "🧹 Starting upload cleanup at #{Time.current}"
    puts "=" * 60
    
    # Initialize counters
    stats = {
      stuck_uploads_cleaned: 0,
      orphaned_temp_files: 0,
      orphaned_s3_files: 0,
      failed_uploads_cleaned: 0,
      total_space_freed: 0
    }
    
    # 1. Clean stuck uploads
    puts "\n1️⃣ Cleaning stuck uploads..."
    stuck_uploads = Upload.where(status: [:transferred, :processing])
                          .where('updated_at < ?', 1.hour.ago)
    
    puts "Found #{stuck_uploads.count} stuck uploads"
    
    stuck_uploads.find_each do |upload|
      begin
        puts "  Cleaning stuck upload #{upload.id}: #{upload.original_filename} (stuck since #{upload.updated_at})"
        upload.cleanup_stuck_upload!
        stats[:stuck_uploads_cleaned] += 1
      rescue => e
        puts "  ❌ Failed to clean upload #{upload.id}: #{e.message}"
      end
    end
    
    # 2. Clean old failed uploads
    puts "\n2️⃣ Cleaning old failed uploads..."
    old_failed_uploads = Upload.where(status: [:failed, :cancelled, :aborted])
                               .where('created_at < ?', 7.days.ago)
    
    puts "Found #{old_failed_uploads.count} old failed uploads"
    
    old_failed_uploads.find_each do |upload|
      begin
        puts "  Removing failed upload #{upload.id}: #{upload.original_filename} (failed #{upload.created_at})"
        
        # Clean temp file if exists
        if upload.temp_file_path.present? && File.exist?(upload.temp_file_path)
          file_size = File.size(upload.temp_file_path)
          File.delete(upload.temp_file_path)
          stats[:total_space_freed] += file_size
          puts "    Removed temp file: #{upload.temp_file_path}"
        end
        
        upload.destroy!
        stats[:failed_uploads_cleaned] += 1
      rescue => e
        puts "  ❌ Failed to clean failed upload #{upload.id}: #{e.message}"
      end
    end
    
    # 3. Find orphaned temp files
    puts "\n3️⃣ Scanning for orphaned temp files..."
    temp_dir = Rails.root.join('tmp', 'uploads')
    
    if Dir.exist?(temp_dir)
      Dir.glob(File.join(temp_dir, '**', '*')).select { |f| File.file?(f) }.each do |temp_file|
        begin
          # Check if file is referenced by any upload
          upload_exists = Upload.where(temp_file_path: temp_file).exists?
          
          # Check if file is older than 24 hours
          file_age = Time.current - File.mtime(temp_file)
          
          if !upload_exists && file_age > 24.hours
            file_size = File.size(temp_file)
            File.delete(temp_file)
            stats[:orphaned_temp_files] += 1
            stats[:total_space_freed] += file_size
            puts "  Removed orphaned temp file: #{temp_file} (#{file_age.to_i / 3600}h old)"
          end
        rescue => e
          puts "  ❌ Failed to process temp file #{temp_file}: #{e.message}"
        end
      end
    else
      puts "  No temp directory found"
    end
    
    # 4. Report orphaned S3 files (analysis only, no deletion)
    puts "\n4️⃣ Analyzing orphaned S3 files..."
    puts "  (Analysis only - manual review required for S3 cleanup)"
    
    begin
      orphaned_s3_analysis
      stats[:orphaned_s3_files] = "See analysis above"
    rescue => e
      puts "  ❌ S3 analysis failed: #{e.message}"
    end
    
    # Final report
    puts "\n" + "=" * 60
    puts "🎯 CLEANUP SUMMARY"
    puts "=" * 60
    puts "Stuck uploads cleaned: #{stats[:stuck_uploads_cleaned]}"
    puts "Failed uploads removed: #{stats[:failed_uploads_cleaned]}"
    puts "Orphaned temp files removed: #{stats[:orphaned_temp_files]}"
    puts "Total disk space freed: #{ActiveSupport::NumberHelper.number_to_human_size(stats[:total_space_freed])}"
    puts "Orphaned S3 files: #{stats[:orphaned_s3_files]}"
    puts "\n✅ Cleanup completed at #{Time.current}"
  end
  
  desc "Analyze orphaned S3 files (safe read-only analysis)"
  task analyze_s3_orphans: :environment do
    puts "🔍 Analyzing orphaned S3 files..."
    orphaned_s3_analysis
  end
  
  desc "Clean only stuck uploads (safer option)"
  task stuck_only: :environment do
    puts "🧹 Cleaning stuck uploads only..."
    
    stuck_uploads = Upload.where(status: [:transferred, :processing])
                          .where('updated_at < ?', 1.hour.ago)
    
    puts "Found #{stuck_uploads.count} stuck uploads"
    
    if stuck_uploads.count == 0
      puts "✅ No stuck uploads found!"
    else
    
      stuck_uploads.find_each do |upload|
        puts "Cleaning: #{upload.id} - #{upload.original_filename} (stuck since #{upload.updated_at})"
        begin
          upload.cleanup_stuck_upload!
          puts "  ✅ Cleaned successfully"
        rescue => e
          puts "  ❌ Failed: #{e.message}"
        end
      end
    end
    
    puts "✅ Stuck uploads cleanup completed"
  end
  
  desc "Show detailed upload statistics"
  task stats: :environment do
    puts "📊 UPLOAD STATISTICS"
    puts "=" * 50
    
    # Upload status breakdown
    Upload.group(:status).count.each do |status, count|
      puts "#{status.capitalize.ljust(15)}: #{count}"
    end
    
    puts "\nTime-based analysis:"
    puts "Last 24 hours: #{Upload.where('created_at > ?', 24.hours.ago).count}"
    puts "Last 7 days:   #{Upload.where('created_at > ?', 7.days.ago).count}"
    puts "Last 30 days:  #{Upload.where('created_at > ?', 30.days.ago).count}"
    
    puts "\nStuck uploads:"
    stuck_count = Upload.where(status: [:transferred, :processing])
                        .where('updated_at < ?', 1.hour.ago).count
    puts "Currently stuck: #{stuck_count}"
    
    puts "\nTemp files:"
    temp_dir = Rails.root.join('tmp', 'uploads')
    if Dir.exist?(temp_dir)
      temp_files = Dir.glob(File.join(temp_dir, '**', '*')).select { |f| File.file?(f) }
      total_temp_size = temp_files.sum { |f| File.size(f) rescue 0 }
      puts "Temp files: #{temp_files.count}"
      puts "Temp space used: #{ActiveSupport::NumberHelper.number_to_human_size(total_temp_size)}"
    else
      puts "No temp directory found"
    end
    
    puts "\nActive Storage:"
    puts "Total blobs: #{ActiveStorage::Blob.count}"
    puts "Total attachments: #{ActiveStorage::Attachment.count}"
    
    # Project file statistics
    projects_with_files = Project.joins(:private_files_attachments).distinct.count
    total_projects = Project.count
    puts "\nProjects with files: #{projects_with_files}/#{total_projects}"
  end
  
  private
  
  def orphaned_s3_analysis
    puts "  Connecting to S3..."
    
    # Use same S3 configuration as FileUploadJob
    aws_key = Rails.env.development? ? :aws_dev : :aws_prod
    bucket_name = Rails.env.development? ? 
      'app-sierra-a7b3c9e2-f8d1-4e56-9c2a-dev-uploads' : 
      'app-sierra-b1c5e9a3-d7f2-4a68-9b4e-prod-uploads'
    
    s3_client = Aws::S3::Client.new(
      region: Rails.application.credentials.dig(aws_key, :region) || ENV['AWS_REGION'],
      credentials: Aws::Credentials.new(
        Rails.application.credentials.dig(aws_key, :access_key_id) || ENV['AWS_ACCESS_KEY_ID'],
        Rails.application.credentials.dig(aws_key, :secret_access_key) || ENV['AWS_SECRET_ACCESS_KEY']
      )
    )
    
    puts "  Listing S3 objects..."
    
    # Get objects older than 7 days in uploads/ prefix
    cutoff_date = 7.days.ago
    orphaned_objects = []
    total_objects = 0
    
    s3_client.list_objects_v2(
      bucket: bucket_name,
      prefix: 'uploads/'
    ).each do |response|
      response.contents.each do |object|
        total_objects += 1
        
        # Skip recent files
        next if object.last_modified > cutoff_date
        
        # Check if this S3 key exists in ActiveStorage blobs
        blob_exists = ActiveStorage::Blob.exists?(key: object.key)
        
        unless blob_exists
          orphaned_objects << {
            key: object.key,
            size: object.size,
            last_modified: object.last_modified
          }
        end
      end
    end
    
    puts "  Total S3 objects analyzed: #{total_objects}"
    puts "  Potentially orphaned objects: #{orphaned_objects.count}"
    
    if orphaned_objects.any?
      total_orphaned_size = orphaned_objects.sum { |obj| obj[:size] }
      puts "  Total orphaned size: #{ActiveSupport::NumberHelper.number_to_human_size(total_orphaned_size)}"
      
      puts "\n  Sample orphaned files (first 10):"
      orphaned_objects.first(10).each do |obj|
        puts "    #{obj[:key]} (#{ActiveSupport::NumberHelper.number_to_human_size(obj[:size])}, #{obj[:last_modified]})"
      end
      
      if orphaned_objects.count > 10
        puts "    ... and #{orphaned_objects.count - 10} more"
      end
      
      puts "\n  ⚠️  Manual S3 cleanup required - review orphaned files before deletion"
    else
      puts "  ✅ No orphaned S3 files found!"
    end
    
  rescue => e
    puts "  ❌ S3 analysis error: #{e.message}"
    puts "  Check AWS credentials and permissions"
  end
end