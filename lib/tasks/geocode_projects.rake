namespace :projects do
  desc "Geocode existing projects that don't have coordinates"
  task geocode_missing: :environment do
    projects_without_coords = Project.where(latitude: nil).or(Project.where(longitude: nil))
    total = projects_without_coords.count
    
    puts "Found #{total} projects without coordinates"
    
    projects_without_coords.find_each.with_index do |project, index|
      print "Geocoding project #{index + 1}/#{total}: #{project.summary[0..50]}... "
      
      if project.location.present?
        begin
          project.geocode
          if project.save
            puts "✓ (#{project.latitude}, #{project.longitude})"
          else
            puts "✗ Save failed: #{project.errors.full_messages.join(', ')}"
          end
        rescue => e
          puts "✗ Geocoding failed: #{e.message}"
        end
      else
        puts "✗ No location set"
      end
      
      # Rate limiting to avoid hitting geocoding service limits
      sleep(0.2)
    end
    
    puts "\nGeocoding complete!"
  end
end