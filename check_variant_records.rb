#!/usr/bin/env ruby

puts "=== CHECKING VARIANT RECORDS ==="

project = Project.find(1)

# Check image files
image_files = project.private_files.joins(:blob).where('active_storage_blobs.content_type LIKE ?', 'image/%')
puts "\nImage files count: #{image_files.count}"

image_files.first(3).each do |file|
  puts "Image: #{file.filename}"
  variant_records = ActiveStorage::VariantRecord.where(blob: file.blob)
  puts "  Variant records: #{variant_records.count}"
  variant_records.each do |vr|
    puts "    - #{vr.variation_digest}"
  end
end

# Check PDF files  
pdf_files = project.private_files.joins(:blob).where('active_storage_blobs.content_type = ?', 'application/pdf')
puts "\nPDF files count: #{pdf_files.count}"

pdf_files.first(3).each do |file|
  puts "PDF: #{file.filename}"
  variant_records = ActiveStorage::VariantRecord.where(blob: file.blob)
  puts "  Variant records: #{variant_records.count}"
  variant_records.each do |vr|
    puts "    - #{vr.variation_digest}"
  end
end

puts "\n=== TOTAL VARIANT RECORDS IN DB: #{ActiveStorage::VariantRecord.count} ==="