#!/usr/bin/env ruby
require_relative 'config/environment'

include Rails.application.routes.url_helpers

p = Project.find(1)

puts "Testing thumbnail URL generation..."
puts ""

# Test working file (has thumbnail)
working_file = p.private_files.find { |f| f.id == 360 } # 4MB_test_picture.jpg that works
if working_file
  puts "=== Working file: #{working_file.filename} (ID: #{working_file.id}) ==="
  thumbnail = p.thumbnail_for_file(working_file)
  puts "Has thumbnail: #{thumbnail ? 'YES' : 'NO'}"
  if thumbnail
    puts "Thumbnail: #{thumbnail.filename} (Key: #{thumbnail.blob.key})"
  end
  
  # Test the helper method
  helper = Object.new
  helper.extend(ProjectsHelper)
  
  thumbnail_url = helper.safe_thumbnail_url(p, working_file)
  puts "Helper returns: #{thumbnail_url}"
  
  # Generate the path manually
  manual_path = file_thumbnail_path(project_id: p.id, file_id: working_file.id)
  puts "Manual path: #{manual_path}"
  
  puts ""
end

# Test broken file (no thumbnail)
broken_file = p.private_files.find { |f| f.id == 363 } # 4MB_test_picture.jpg that doesn't work
if broken_file
  puts "=== Broken file: #{broken_file.filename} (ID: #{broken_file.id}) ==="
  thumbnail = p.thumbnail_for_file(broken_file)
  puts "Has thumbnail: #{thumbnail ? 'YES' : 'NO'}"
  
  # Test the helper method
  helper = Object.new
  helper.extend(ProjectsHelper)
  
  thumbnail_url = helper.safe_thumbnail_url(p, broken_file)
  puts "Helper returns: #{thumbnail_url}"
  
  # What would the path be if we forced it?
  manual_path = file_thumbnail_path(project_id: p.id, file_id: broken_file.id)
  puts "Manual path (if forced): #{manual_path}"
  
  puts ""
end

puts "Testing supports_thumbnail? method..."
helper = Object.new
helper.extend(ProjectsHelper)

[working_file, broken_file].compact.each do |file|
  supports = helper.supports_thumbnail?(file)
  puts "File #{file.filename} supports thumbnail: #{supports}"
  puts "  Content type: #{file.content_type}"
  puts "  Image check: #{file.content_type.start_with?('image/')}"
  puts "  PDF check: #{file.content_type == 'application/pdf'}"
end