#!/usr/bin/env ruby

# Test actual HTTP request to reproduce the publish issue
require 'net/http'
require 'uri'

puts "=== TESTING ACTUAL PUBLISH HTTP REQUEST ==="

# First get current project state
p = Project.find(110)
puts "Project 110 before request:"
puts "  summary: '#{p.summary}'"
puts "  location: '#{p.location}'" 
puts "  project_status: #{p.project_status}"

# Create a minimal publish request
# This will help us see if there's middleware interfering
uri = URI.parse("http://localhost:5000/sk/projects/110")
puts "\nWould make PATCH request to: #{uri}"
puts "With publish=true parameter"

# Check project state after (without actually making HTTP request)
p.reload
puts "\nProject 110 after (should be unchanged):"
puts "  summary: '#{p.summary}'"
puts "  location: '#{p.location}'"
puts "  project_status: #{p.project_status}"

puts "\nTo actually test this, we would need:"
puts "1. Valid session authentication"
puts "2. CSRF token"
puts "3. Proper form parameters"
puts "4. Check response code and location header"