#!/usr/bin/env ruby
# Test the ActionView template memory leak fix
# Usage: ruby test_actionview_fix.rb

require 'net/http'
require 'uri'
require 'json'

class ActionViewFixTester
  BASE_URL = 'http://localhost:3000'
  
  def initialize
    puts "🧪 Testing ActionView Template Memory Leak Fix"
    puts "=" * 60
  end
  
  def run_test
    # Get baseline memory
    baseline = get_memory_stats
    return unless baseline
    puts "📊 Baseline: #{baseline['process_memory_mb'].round(2)} MB"
    
    # Create a heap dump before
    puts "\n🔄 Creating heap dump (before)..."
    heap_before = create_heap_dump
    
    # Simulate intensive template rendering
    puts "\n⚡ Testing intensive template rendering (simulating ActionView leak)..."
    test_template_rendering(30) # More requests to trigger template caching issues
    
    # Create a heap dump after
    puts "\n🔄 Creating heap dump (after)..."
    heap_after = create_heap_dump
    
    # Final memory measurement
    final = get_memory_stats
    return unless final
    
    memory_growth = final['process_memory_mb'] - baseline['process_memory_mb']
    object_growth = final['object_counts']['TOTAL'] - baseline['object_counts']['TOTAL']
    
    puts "\n" + "=" * 60
    puts "🎯 ACTIONVIEW TEMPLATE TEST RESULTS"
    puts "=" * 60
    puts "Memory Growth: #{memory_growth.round(2)} MB"
    puts "Object Growth: #{object_growth} objects"
    puts "Baseline Memory: #{baseline['process_memory_mb'].round(2)} MB"
    puts "Final Memory: #{final['process_memory_mb'].round(2)} MB"
    
    if memory_growth > 20
      puts "🚨 ACTIONVIEW LEAK STILL PRESENT (>10MB growth)"
    elsif memory_growth > 10
      puts "⚠️  MODERATE TEMPLATE MEMORY GROWTH (>10MB)"
    elsif memory_growth > 0
      puts "✅ SMALL MEMORY GROWTH (#{memory_growth.round(2)}MB) - Likely normal"
    else
      puts "✅ MEMORY DECREASED OR STABLE - Fix working!"
    end
    
    # Analyze heap dumps if available
    if heap_before && heap_after
      puts "\n🔍 Analyzing heap dumps for ActionView objects..."
      puts "Before heap: #{heap_before}"
      puts "After heap: #{heap_after}"
      puts "Run: cd tmp && heapy diff #{heap_before} #{heap_after} | grep -i 'actionview\\|string'"
    end
    
    # Compare suspect objects
    puts "\nSuspect Object Changes:"
    if final['suspect_objects'] && baseline['suspect_objects']
      final['suspect_objects'].each do |type, count|
        baseline_count = baseline['suspect_objects'][type] || 0
        growth = count - baseline_count
        puts "  #{type}: #{count} (+#{growth})" if growth != 0
      end
    end
  end
  
  private
  
  def test_template_rendering(count)
    count.times do |i|
      # Test the main controller that uses cached translations
      response = post_request('/memory_debug/test_projects_leak')
      if response
        print "✓"
      else
        print "✗"
      end
      
      print "\n" if (i + 1) % 10 == 0
      sleep(0.05) # Faster requests to stress template system
    end
    
    puts "\nTemplate rendering test completed!"
  end
  
  def get_memory_stats
    response = get_request('/memory_debug/stats')
    if response
      puts "Current memory: #{response['process_memory_mb'].round(2)} MB, Objects: #{response['object_counts']['TOTAL']}"
    end
    response
  end
  
  def create_heap_dump
    response = get_request('/memory_debug/heap_dump')
    if response
      filename = File.basename(response['filename'])
      puts "Heap dump created: #{filename}"
      return filename
    end
    nil
  end
  
  def get_request(path)
    uri = URI("#{BASE_URL}#{path}")
    
    begin
      response = Net::HTTP.get_response(uri)
      if response.code == '200'
        JSON.parse(response.body)
      else
        puts "❌ Error: #{response.code} - #{response.message}"
        nil
      end
    rescue => e
      puts "❌ Request failed: #{e.message}"
      nil
    end
  end
  
  def post_request(path)
    uri = URI("#{BASE_URL}#{path}")
    
    begin
      response = Net::HTTP.post(uri, '')
      if response.code == '200'
        JSON.parse(response.body)
      else
        puts "❌ Error: #{response.code} - #{response.message}"
        nil
      end
    rescue => e
      puts "❌ Request failed: #{e.message}"
      nil
    end
  end
end

# Run the test
if __FILE__ == $0
  tester = ActionViewFixTester.new
  tester.run_test
end