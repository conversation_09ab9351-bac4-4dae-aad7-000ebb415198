# Inline File Display Implementation Plan

## Goal
Extend the existing working file system to add exactly two core features:
1. **Thumbnails**: 300x200px previews using Active Storage's `.preview()` and `.variant()` with download links
2. **Inline Display**: Click thumbnails to show full files inline (no modal) with close icon

## Requirements (Strictly Enforced)
Based on original instruction - no deviations:
- Use Rails Active Storage built-in abilities: `.preview()` for PDFs, `.variant()` for images  
- Thumbnail size: 300x200px
- Below thumbnail: download link
- Click thumbnail: entire file opens inline with close icon
- Use existing ActionPolicy authorization (already working)
- File names visible to authenticated/authorized users
- Original S3 paths NOT visible to anyone
- Development files stored on S3 development bucket

## Dependencies to Check
- Active Storage configured with S3 (development and production buckets)
- ActionPolicy implemented and working for project file access
- Current download functionality working
- Image processing libraries (libvips) available for variants/previews

## Core Architecture Decision
**Security Constraint**: "Original S3 paths NOT visible" requires proxying all file access through Rails controllers, not direct S3 URLs.

---

## Implementation Chunks

### **Chunk 1: Thumbnail Display with Download Links**
**Goal**: Display 300x200px thumbnails using Active Storage with working download links

**Core Tasks**:
- Create file proxy controller to serve files without exposing S3 URLs
- Add route for secure file serving using signed blob IDs
- Implement authorization using existing ActionPolicy 
- Create helper method for safe thumbnail URL generation with error handling
- Update project view to show thumbnail grid instead of current file listing
- Add download links below each thumbnail
- Handle unsupported file types with fallback icons
- **NEW**: Configure Active Storage with Poppler for PDF previews
  - Enable `image_processing` gem for variants/previews
  - Configure Poppler as PDF previewer in Active Storage
  - Set libvips as variant processor for better performance
  - Handle ActiveStorage::Attachment objects properly in helpers
  - **CRITICAL FIX**: Use proper chaining for PDF thumbnails: `blob.preview.variant(resize_to_limit: [300, 200])`
    - PDF previewers don't accept resize options directly like video previewers
    - Must chain: first create full-size preview, then create resized variant of that preview

**Testing After This Chunk**:
- Upload image file → see 300x200 thumbnail
- Upload PDF file → see 300x200 first-page preview
- Upload unsupported file → see fallback icon
- All download links work
- Authorization respected (unauthorized users cannot access)
- No S3 URLs visible in browser

**Success Criteria**: Thumbnail grid replaces current file listing with working downloads

---

### **Chunk 2: Inline File Display with Close**
**Goal**: Click thumbnail shows full file inline on same page with close functionality

**Core Tasks**:
- Add JavaScript click handler for thumbnails
- Create inline viewing area in project page
- Implement full file display (images directly, PDFs in iframe)
- Add close icon/button to hide inline viewer
- Ensure inline display uses same secure proxy URLs
- Handle different file types appropriately for inline viewing

**Testing After This Chunk**:
- Click image thumbnail → full image displays inline below thumbnails
- Click PDF thumbnail → full PDF displays in iframe   
- Click close → inline viewer disappears, back to thumbnail grid
- All functionality from Chunk 1 still works
- No modals or overlays used

**Success Criteria**: Thumbnail click shows full file inline with working close function

---

## Critical Implementation Notes

**Authorization Integration**:
- Use existing ActionPolicy on project level
- File proxy controller finds parent project and authorizes access
- No new authorization logic needed

**Performance Considerations**:
- First thumbnail request may be slow (variant/preview generation)
- Consider background job generation for production use with high-volume sites
- Stream large files through controller to avoid memory issues

**Error Handling**:
- Corrupt/unsupported files should show fallback icons
- Failed thumbnail generation should not break page
- Invalid file access should return 403/404 appropriately

**File Types**:
- Images: Use `.variant(resize_to_limit: [300, 200])`
- PDFs: Use `.preview(resize_to_limit: [300, 200])` 
- Others: Show generic file icon

## Testing Strategy
**After Each Chunk**: Complete end-to-end testing to verify functionality works before proceeding to next chunk.

**Core Test Cases**:
1. Authorized user sees thumbnails and can download
2. Authorized user can view files inline
3. Unauthorized user cannot access files
4. Different file types handled correctly
5. Download functionality preserved
6. No S3 URLs exposed in browser

## Success Definition
- ✅ Thumbnails display at 300x200px using background job generation (COMPLETE)
- ✅ Download links work below thumbnails using secure token system (COMPLETE)
- ✅ Click thumbnail shows full file inline via InlineFileViewer (COMPLETE)
- ✅ Close button hides inline display (COMPLETE)
- ✅ Existing authorization respected via ActionPolicy (COMPLETE)
- ✅ No S3 URLs visible - all proxied through Rails (COMPLETE)
- ✅ No modals or complex UI - inline display only (COMPLETE)

**IMPLEMENTATION STATUS**: ✅ **COMPLETE** - Core functionality working (June 2025)

## CRITICAL BUG FIXES APPLIED (June 2025)

### 1. PDF Thumbnail Filename Bug
**Issue**: pdftoppm creates `-001.png` but job expected `-1.png`
**Fix**: Changed `output_file = "#{output_prefix}-1.png"` to `output_file = "#{output_prefix}-001.png"`
**File**: `app/jobs/pdf_thumbnail_generation_job.rb:99`

### 2. GoodJob Execution in Development
**Issue**: Jobs enqueued but never executed (no worker process)
**Fix**: Added async mode for development in `config/initializers/good_job.rb`
```ruby
config.good_job.execution_mode = Rails.env.development? ? :async : :external
```

### 3. S3 Upload Timing Race Condition
**Issue**: Thumbnail job ran before S3 upload completed
**Fix**: Added file existence check in job before processing
```ruby
unless file.blob.service.exist?(file.blob.key)
  Rails.logger.warn "Skipping thumbnail for missing file..."
  next
end
```

### 4. JavaScript Event Binding Confusion
**Issue**: Mixed modal system (`data-action="preview"`) with inline system (`data-action="inline"`)
**Fix**: Restored correct data attributes for InlineFileViewer
```erb
data-action="inline"
data-inline-url="<%= safe_inline_url(project, file) %>"
class="file-thumbnail"
```

## ARCHITECTURE DECISION: DUAL SYSTEM APPROACH

**Final Implementation**: Hybrid security model balancing UX and security
- **Inline Viewing**: Direct file proxy URLs (`/projects/:id/files/:file_id/inline`) for frictionless UX
- **Downloads**: Secure token system (`/secure/stream?t=TOKEN`) for enterprise security
- **Authorization**: Both paths use same ActionPolicy project-level authorization

**Rationale**: Inline viewing prioritizes user experience while downloads maintain enterprise security standards.

**SCOPE LIMIT**: Only these two core features. No styling polish, mobile responsiveness, animations, or performance optimizations unless they break core functionality.

---

## S3 Key Extensions Implementation (June 2025)

### Problem
AWS Lambda functions require file extensions in S3 object keys to properly identify file types and trigger appropriate processing.

### Solution
**File**: `config/initializers/active_storage_key_extension.rb`

Implemented `before_create` callback on `ActiveStorage::Blob` to append file extensions to Rails-generated keys.

### Implementation Details

**Upload Flow Discovery**:
- Form has `direct_upload: true` but **no Active Storage JavaScript loaded**
- Files upload via **normal form submission**, not direct to S3
- Rails creates blob during `project.save` process

**Code Implementation**:
```ruby
Rails.application.config.to_prepare do
  ActiveStorage::Blob.class_eval do
    before_create :append_file_extension_to_key

    private
    def append_file_extension_to_key
      return unless key.present? && filename.present?
      
      extension = filename.extension_with_delimiter
      if extension.present? && !key.end_with?(extension)
        self.key = "#{key}#{extension}"
      end
    end
  end
end
```

**Results**:
- **Before**: S3 key `raoujf7ozwo08g2gw3g0u7ejiejw`
- **After**: S3 key `raoujf7ozwo08g2gw3g0u7ejiejw.pdf`

**Benefits**:
- ✅ AWS Lambda can filter S3 events by file extension
- ✅ Better compatibility with AWS services
- ✅ Easier S3 bucket inspection and debugging
- ✅ No impact on existing files or security

**Testing**: Use `ruby test_s3_keys.rb` to verify new uploads have extensions.