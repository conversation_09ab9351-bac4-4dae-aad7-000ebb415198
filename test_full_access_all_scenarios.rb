#!/usr/bin/env ruby

require_relative 'config/environment'

puts "=== COMPREHENSIVE FULL ACCESS TEST ==="
puts "Rails Environment: #{Rails.env}"
puts "Testing all combinations of full_access, visibility, approval, and network connections"
puts "=" * 80
puts ""

class FullAccessTester
  attr_reader :owner, :connected_user, :unconnected_user, :test_results
  
  def initialize
    @test_results = []
    setup_test_users
  end
  
  def run_all_tests
    puts "Test Users Created:"
    puts "  - Owner: #{owner.email} (ID: #{owner.id})"
    puts "  - Connected User: #{connected_user.email} (ID: #{connected_user.id})"
    puts "  - Unconnected User: #{unconnected_user.email} (ID: #{unconnected_user.id})"
    puts ""
    
    # Test all combinations
    test_full_access_semi_public_approved
    test_full_access_semi_public_unapproved
    test_full_access_network_only_approved_connected
    test_full_access_network_only_approved_unconnected
    test_full_access_network_only_unapproved
    
    test_summary_only_semi_public_approved
    test_summary_only_network_only_approved
    test_summary_only_with_explicit_auth
    
    test_nil_user_protection
    test_validation_rules
    test_policy_consistency
    
    print_results_summary
  end
  
  private
  
  def setup_test_users
    # Clean up any existing test data
    User.where("email LIKE 'fullaccess_test_%'").destroy_all
    Project.where("summary LIKE 'FullAccess Test%'").destroy_all
    
    # Create test users
    @owner = User.create!(
      email: '<EMAIL>',
      password: 'password123',
      confirmed_at: Time.current
    )
    
    @connected_user = User.create!(
      email: '<EMAIL>', 
      password: 'password123',
      confirmed_at: Time.current
    )
    
    @unconnected_user = User.create!(
      email: '<EMAIL>',
      password: 'password123',
      confirmed_at: Time.current
    )
    
    # Create network connection between owner and connected_user
    NetworkConnection.create!(
      inviter: owner,
      invitee: connected_user
    )
  end
  
  def test_full_access_semi_public_approved
    puts "\n=== TEST 1: full_access + semi_public + approved ==="
    puts "Expected: All authenticated users get automatic access"
    
    project = create_project(
      summary: 'FullAccess Test 1',
      full_access: true,
      summary_only: false,
      semi_public: true,
      network_only: false,
      approved: true
    )
    
    test_access(project, {
      owner: true,
      connected_user: true,
      unconnected_user: true,
      nil_user: false
    })
  end
  
  def test_full_access_semi_public_unapproved
    puts "\n=== TEST 2: full_access + semi_public + UNAPPROVED ==="
    puts "Expected: Only owner has access (approval gate)"
    
    project = create_project(
      summary: 'FullAccess Test 2',
      full_access: true,
      summary_only: false,
      semi_public: true,
      network_only: false,
      approved: false
    )
    
    test_access(project, {
      owner: true,
      connected_user: false,
      unconnected_user: false,
      nil_user: false
    })
  end
  
  def test_full_access_network_only_approved_connected
    puts "\n=== TEST 3: full_access + network_only + approved (CONNECTED user) ==="
    puts "Expected: Owner and connected users have access"
    
    project = create_project(
      summary: 'FullAccess Test 3',
      full_access: true,
      summary_only: false,
      semi_public: false,
      network_only: true,
      approved: true
    )
    
    test_access(project, {
      owner: true,
      connected_user: true,
      unconnected_user: false,
      nil_user: false
    })
  end
  
  def test_full_access_network_only_approved_unconnected
    puts "\n=== TEST 4: full_access + network_only + approved (UNCONNECTED user) ==="
    puts "Expected: Only owner and connected users have access"
    
    project = create_project(
      summary: 'FullAccess Test 4',
      full_access: true,
      summary_only: false,
      semi_public: false,
      network_only: true,
      approved: true
    )
    
    # Already tested unconnected in previous test, just confirming
    result = project.user_has_access?(unconnected_user)
    puts "  - Unconnected user access: #{result} (expected: false)"
    record_result("Unconnected user denied for network_only", !result)
  end
  
  def test_full_access_network_only_unapproved
    puts "\n=== TEST 5: full_access + network_only + UNAPPROVED ==="
    puts "Expected: Only owner has access"
    
    project = create_project(
      summary: 'FullAccess Test 5',
      full_access: true,
      summary_only: false,
      semi_public: false,
      network_only: true,
      approved: false
    )
    
    test_access(project, {
      owner: true,
      connected_user: false,
      unconnected_user: false,
      nil_user: false
    })
  end
  
  def test_summary_only_semi_public_approved
    puts "\n=== TEST 6: summary_only + semi_public + approved ==="
    puts "Expected: Only owner has access (no automatic access)"
    
    project = create_project(
      summary: 'FullAccess Test 6',
      full_access: false,
      summary_only: true,
      semi_public: true,
      network_only: false,
      approved: true
    )
    
    test_access(project, {
      owner: true,
      connected_user: false,
      unconnected_user: false,
      nil_user: false
    })
  end
  
  def test_summary_only_network_only_approved
    puts "\n=== TEST 7: summary_only + network_only + approved ==="
    puts "Expected: Only owner has access (no automatic access)"
    
    project = create_project(
      summary: 'FullAccess Test 7',
      full_access: false,
      summary_only: true,
      semi_public: false,
      network_only: true,
      approved: true
    )
    
    test_access(project, {
      owner: true,
      connected_user: false,
      unconnected_user: false,
      nil_user: false
    })
  end
  
  def test_summary_only_with_explicit_auth
    puts "\n=== TEST 8: summary_only with explicit ProjectAuth ==="
    puts "Expected: Owner and explicitly authorized user have access"
    
    project = create_project(
      summary: 'FullAccess Test 8',
      full_access: false,
      summary_only: true,
      semi_public: true,
      network_only: false,
      approved: true
    )
    
    # Grant explicit access
    ProjectAuth.create!(
      project: project,
      user: connected_user,
      access_level: 'full_details'
    )
    
    test_access(project, {
      owner: true,
      connected_user: true,
      unconnected_user: false,
      nil_user: false
    })
  end
  
  def test_nil_user_protection
    puts "\n=== TEST 9: Nil user protection ==="
    puts "Expected: Nil users never have access"
    
    project = create_project(
      summary: 'FullAccess Test 9',
      full_access: true,
      summary_only: false,
      semi_public: true,
      network_only: false,
      approved: true
    )
    
    result = project.user_has_access?(nil)
    puts "  - Nil user access on full_access project: #{result} (expected: false)"
    record_result("Nil user protection", !result)
  end
  
  def test_validation_rules
    puts "\n=== TEST 10: Validation rules ==="
    puts "Expected: Invalid combinations are rejected"
    
    # Test both visibility options
    invalid1 = Project.new(
      user: owner,
      summary: 'Invalid Test 1',
      location: 'Test',
      project_type: 'business',
      category: 'business_acquisition',
      subcategory: 'asset_purchase',
      network_only: true,
      semi_public: true,
      summary_only: true,
      full_access: false
    )
    
    valid1 = invalid1.valid?
    puts "  - Both visibility options: #{valid1} (expected: false)"
    puts "    Errors: #{invalid1.errors.full_messages.join(', ')}" unless valid1
    record_result("Visibility validation", !valid1)
    
    # Test both detail options
    invalid2 = Project.new(
      user: owner,
      summary: 'Invalid Test 2',
      location: 'Test',
      project_type: 'business',
      category: 'business_acquisition',
      subcategory: 'asset_purchase',
      network_only: true,
      semi_public: false,
      summary_only: true,
      full_access: true
    )
    
    valid2 = invalid2.valid?
    puts "  - Both detail options: #{valid2} (expected: false)"
    puts "    Errors: #{invalid2.errors.full_messages.join(', ')}" unless valid2
    record_result("Detail level validation", !valid2)
  end
  
  def test_policy_consistency
    puts "\n=== TEST 11: Policy consistency ==="
    puts "Expected: Model and Policy return same results"
    
    project = create_project(
      summary: 'FullAccess Test 11',
      full_access: true,
      summary_only: false,
      semi_public: true,
      network_only: false,
      approved: true
    )
    
    # Test model access
    model_result = project.user_has_access?(connected_user)
    
    # Test policy logic directly (without ActionPolicy instantiation)
    # Replicate the policy logic manually
    policy_result = if project.user_id == connected_user.id
      true
    elsif !project.approved?
      false
    elsif project.project_auths.exists?(user_id: connected_user.id, access_level: 'full_details')
      true
    elsif project.full_access? && project.semi_public?
      true
    else
      false
    end
    
    puts "  - Model says: #{model_result}"
    puts "  - Policy logic says: #{policy_result}"
    puts "  - Consistent: #{model_result == policy_result}"
    record_result("Model/Policy consistency", model_result == policy_result)
  end
  
  def create_project(attrs)
    project = Project.new(
      user: owner,
      location: 'Test Location',
      project_type: 'business',
      category: 'business_acquisition',
      subcategory: 'asset_purchase',
      **attrs
    )
    
    # Save without validation to bypass approval workflow
    project.save(validate: false)
    
    # Update approved status if specified
    if attrs.key?(:approved)
      project.update_column(:approved, attrs[:approved])
    end
    
    project
  end
  
  def test_access(project, expectations)
    puts "Project settings:"
    puts "  - full_access: #{project.full_access?}"
    puts "  - summary_only: #{project.summary_only?}"
    puts "  - semi_public: #{project.semi_public?}"
    puts "  - network_only: #{project.network_only?}"
    puts "  - approved: #{project.approved?}"
    puts ""
    
    puts "Access results:"
    
    # Test owner
    result = project.user_has_access?(owner)
    expected = expectations[:owner]
    puts "  - Owner: #{result} (expected: #{expected}) #{result == expected ? '' : ''}"
    record_result("Owner access", result == expected)
    
    # Test connected user
    result = project.user_has_access?(connected_user)
    expected = expectations[:connected_user]
    puts "  - Connected user: #{result} (expected: #{expected}) #{result == expected ? '' : ''}"
    record_result("Connected user access", result == expected)
    
    # Test unconnected user
    result = project.user_has_access?(unconnected_user)
    expected = expectations[:unconnected_user]
    puts "  - Unconnected user: #{result} (expected: #{expected}) #{result == expected ? '' : ''}"
    record_result("Unconnected user access", result == expected)
    
    # Test nil user
    result = project.user_has_access?(nil)
    expected = expectations[:nil_user]
    puts "  - Nil user: #{result} (expected: #{expected}) #{result == expected ? '' : ''}"
    record_result("Nil user access", result == expected)
  end
  
  def record_result(test_name, passed)
    @test_results << { test: test_name, passed: passed }
  end
  
  def print_results_summary
    puts "\n" + "=" * 80
    puts "=== TEST RESULTS SUMMARY ==="
    puts "=" * 80
    
    total = test_results.count
    passed = test_results.count { |r| r[:passed] }
    failed = total - passed
    
    puts "\nTotal tests: #{total}"
    puts "Passed: #{passed} (#{(passed.to_f / total * 100).round(1)}%)"
    puts "Failed: #{failed}"
    
    if failed > 0
      puts "\nFailed tests:"
      test_results.select { |r| !r[:passed] }.each do |result|
        puts "   #{result[:test]}"
      end
    else
      puts "\n ALL TESTS PASSED!"
    end
    
    puts "\n" + "=" * 80
  end
end

# Run the tests
begin
  tester = FullAccessTester.new
  tester.run_all_tests
rescue => e
  puts "\nERROR: #{e.class}: #{e.message}"
  puts e.backtrace[0..5].join("\n")
ensure
  # Cleanup
  User.where("email LIKE 'fullaccess_test_%'").destroy_all
  Project.where("summary LIKE 'FullAccess Test%'").destroy_all
end