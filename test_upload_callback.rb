#!/usr/bin/env ruby

require_relative 'config/environment'

puts "Testing upload callback..."

user = User.first
project = Project.first

puts "Creating upload..."
upload = Upload.create!(
  user: user, 
  target: project, 
  original_filename: 'test.png', 
  content_type: 'image/png', 
  file_size: 1000, 
  status: :pending, 
  progress_percentage: 0
)

puts "Upload created with ID: #{upload.id}, status: #{upload.status}"

puts "Updating status to ready..."
upload.update!(status: :ready, temp_file_path: '/tmp/test.png')

puts "Upload status now: #{upload.status}"
puts "Callback test complete."