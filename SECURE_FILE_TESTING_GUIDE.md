# Secure File Testing Guide

**Last Updated**: January 2025  
**Status**: Implementation Complete, Tests 70% Passing

## Overview

This guide documents the complete testing strategy, implementation details, and troubleshooting for the Secure Inline File Display system. It serves as the central source of truth for all testing-related information.

## Test Architecture

### Test Layers

1. **Unit Tests** - Core components in isolation
   - `spec/services/secure_file_token_service_spec.rb` - JWT token generation/validation
   - `spec/models/project_spec.rb` - SecureFileAccess concern tests

2. **Request/Integration Tests** - API endpoints and controller logic
   - `spec/requests/secure_file_access_spec.rb` - Token request and streaming endpoints
   - `spec/requests/private_files_spec.rb` - Legacy file access endpoints

3. **Feature/System Tests** - End-to-end user flows
   - `spec/features/secure_file_display_spec.rb` - Lightbox UI and security scenarios

### Test Coverage Summary

As of the latest test run:
- **Total Tests**: 30
- **Passing**: 21 (70%)
- **Failing**: 9 (30%)

## Key Testing Patterns

### 1. Factory Configuration

**Critical**: The Project factory must use `summary` field, not `title`:

```ruby
# spec/factories/projects.rb
factory :project do
  association :user
  summary { "Secure File Test Project" }  # NOT title!
  # ... other fields
end
```

### 2. File Attachment Testing

**Issue**: Active Storage attachments require valid, persisted models to work correctly in tests.

**Solution**: Use the `:with_files` trait without `:minimal`:

```ruby
# ✅ CORRECT - Creates valid project with persisted files
let(:project) { create(:project, :with_files, user: user) }

# ❌ INCORRECT - Files won't persist with invalid models
let(:project) { create(:project, :minimal, :with_files, user: user) }
```

### 3. Transaction Isolation

**Issue**: Active Storage attachments created in test transactions may not be visible to controller actions due to transaction isolation.

**Symptoms**:
- Files exist in test: `project.private_files.count # => 2`
- Files missing in controller: `@project.private_files.count # => 0`

**Solution**: Ensure proper model validation and persistence (see Factory Configuration above).

## Common Test Scenarios

### Token Generation Tests

```ruby
it 'returns a valid token for PDF files' do
  file_hash = project.generate_secure_file_hash(pdf_file)
  
  post request_file_token_project_path(project),
       params: { file_hash: file_hash },
       as: :json

  expect(response).to have_http_status(:ok)
  expect(json_response['token']).to be_present
  expect(json_response['content_type']).to eq('application/pdf')
  expect(json_response['expires_in']).to eq(300)
end
```

### Security Tests

```ruby
it 'prevents token reuse after session destruction' do
  sign_in user
  token = get_valid_token_for(pdf_file)
  
  sign_out user  # Destroys session
  
  get secure_stream_path(t: token)
  expect(response).to have_http_status(:forbidden)
end
```

### Authorization Tests

```ruby
it 'denies access to unauthorized users' do
  sign_in other_user  # Not authorized for project
  file_hash = project.generate_secure_file_hash(pdf_file)
  
  post request_file_token_project_path(project),
       params: { file_hash: file_hash },
       as: :json
       
  expect(response).to have_http_status(:forbidden)
end
```

## Troubleshooting

### Problem: "Title can't be empty" validation error

**Cause**: The Project model validates `summary` field but error message says "Title".

**Fix**: Ensure factory uses `summary` field:
```ruby
factory :project do
  summary { "Test Project" }  # Required field
end
```

### Problem: Files not found in controller despite being attached in test

**Cause**: Active Storage attachments not persisting due to invalid model state.

**Diagnosis**:
```ruby
# Debug script to verify file persistence
project = create(:project, :with_files)
puts "After creation: #{project.private_files.count}"  # Should be 2
project.reload
puts "After reload: #{project.private_files.count}"   # Should still be 2
```

**Fix**: Don't use `:minimal` trait with file attachments.

### Problem: Authentication tests expect 401 but get 302

**Cause**: Devise redirects unauthenticated requests instead of returning 401.

**Fix**: Test for redirect instead:
```ruby
expect(response).to redirect_to(new_user_session_path)
# OR for API endpoints, configure Devise to return 401
```

## Security Testing Checklist

### ✅ Implemented Security Tests

- [x] Token generation with session binding
- [x] Token validation with expired tokens
- [x] Cross-project file access prevention
- [x] Rate limiting on token generation
- [x] Security headers validation
- [x] Parameter filtering in logs
- [x] Timing attack prevention with secure_compare

### 🔧 Pending Fixes

- [ ] Authentication response codes (302 vs 401)
- [ ] Cache-Control header completeness
- [ ] Referrer validation in streaming endpoint
- [ ] Session fingerprint validation edge cases

## Running Tests

### Full Test Suite
```bash
bundle exec rspec spec/requests/secure_file_access_spec.rb
```

### Specific Test
```bash
bundle exec rspec spec/requests/secure_file_access_spec.rb:31
```

### With Clean Database
```bash
RAILS_ENV=test bundle exec rails db:reset
RAILS_ENV=test bundle exec rails db:migrate
bundle exec rspec spec/requests/secure_file_access_spec.rb
```

### Debug Mode
```bash
bundle exec rspec spec/requests/secure_file_access_spec.rb --format documentation
```

## Test Data Setup

### Creating Test Files
Test files are located in `spec/fixtures/files/`:
- `test.pdf` - Sample PDF for testing
- `test_image.png` - Sample image for testing

### Factory Traits
```ruby
# Project with files attached
create(:project, :with_files)

# Project with custom user
create(:project, :with_files, user: specific_user)

# Invalid project (bypasses validations)
create(:project, :minimal)  # Don't use with :with_files!
```

## Integration with CI/CD

### Pre-commit Checks
```bash
# Run security-critical tests before committing
bundle exec rspec spec/requests/secure_file_access_spec.rb --tag security
```

### Performance Considerations
- File attachment tests are slower due to I/O
- Consider using `--tag ~slow` to skip file tests in rapid iterations
- Use transactional fixtures for faster cleanup

## References

- **TDD Guide**: [`TDD_GUIDE.md`](./TDD_GUIDE.md) - Original test-driven development plan
- **Security Model**: [`docs/security/THREAT_MODEL.md`](./docs/security/THREAT_MODEL.md) - Security architecture
- **Implementation**: [`SECURE_INLINE_FILE_DISPLAY_IMPLEMENTATION_PLAN.md`](./SECURE_INLINE_FILE_DISPLAY_IMPLEMENTATION_PLAN.md)

## Appendix: Test Implementation Status

### Token Service Tests ✅
- Token generation: PASS
- Token validation: PASS
- Session binding: PASS
- Expiration handling: PASS

### Model Tests ✅
- Secure hash generation: PASS
- File lookup by hash: PASS
- Cross-project isolation: PASS

### Request Tests ⚠️
- Token generation endpoint: PASS
- Authorization checks: PARTIAL (status code mismatches)
- Streaming endpoint: PARTIAL (session validation issues)
- Security headers: PARTIAL (cache-control incomplete)

### Feature Tests ✅
- Lightbox display: PASS
- File downloads: PASS
- Security scenarios: PASS

---

**Note**: This guide supersedes individual test documentation scattered across multiple files. For implementation-specific details, refer to the actual test files in `spec/`.