#!/usr/bin/env python3
"""
Production-ready webhook testing script for thumbnail generator
Tests the webhook endpoint with HMAC authentication matching the Lambda implementation

Usage:
  ./test_webhook_local.py --secret "your-webhook-secret"
  ./test_webhook_local.py --production --secret "your-webhook-secret"
  ./test_webhook_local.py --url http://localhost:5000/wh/thumb_ready --secret "your-webhook-secret"

This script matches the exact HMAC signature calculation used by the Lambda function.
"""

import requests
import json
import sys
import argparse
import time
import hmac
import hashlib

def test_webhook(url, secret, thumbnail_url=None, original_key=None, custom_timestamp=None, custom_payload=None):
    """Send a test webhook request to the Rails application"""
    
    # Default test data
    if not thumbnail_url:
        thumbnail_url = "https://app-sierra-d4e8f2a6-c3b7-4d91-8e5a-dev-thumbnails.s3.eu-central-1.amazonaws.com/test-thumbnail.png"
    
    if not original_key:
        original_key = "test-document.pdf"
    
    # Webhook payload matching Rails controller expectations and Lambda function format
    payload = {
        "original_blob_key": original_key,
        "thumbnail": {
            "key": f"thumbnails/{original_key.replace('.pdf', '.png')}",
            "filename": original_key.replace('.pdf', '.png'),
            "content_type": "image/png",
            "byte_size": 12345,
            "checksum": "base64encodedmd5hash"
        }
    }
    
    # Generate timestamp
    if custom_timestamp:
        timestamp = custom_timestamp
    else:
        timestamp = str(int(time.time()))
    
    # Convert payload to JSON string
    if custom_payload:
        payload_json = custom_payload
    else:
        payload_json = json.dumps(payload, separators=(',', ':'))
    
    # Debug output
    print(f"DEBUG: Actual JSON being signed: {payload_json}")
    print(f"DEBUG: JSON length: {len(payload_json)}")
    
    # Create string to sign: timestamp.payload
    string_to_sign = f"{timestamp}.{payload_json}"
    
    # Generate HMAC-SHA256 signature
    signature = hmac.new(
        secret.encode('utf-8'),
        string_to_sign.encode('utf-8'),
        hashlib.sha256
    ).hexdigest()
    
    # Headers including HMAC authentication
    headers = {
        "Content-Type": "application/json",
        "X-Signature-Timestamp": timestamp,
        "X-Signature-Hmac-Sha256": signature,
        "User-Agent": "ThumbnailGenerator/1.0"
    }
    
    print(f"\n🔧 Testing webhook endpoint: {url}")
    print(f"📦 Payload: {json.dumps(payload, indent=2) if not custom_payload else 'Using custom payload'}")
    print(f"🔑 Using secret: {secret[:10]}...")
    print(f"🕒 Timestamp: {timestamp}")
    print(f"🔐 HMAC Signature: {signature[:20]}...")
    print(f"🔐 Full HMAC Signature: {signature}")
    
    try:
        # Send the webhook request
        response = requests.post(url, data=payload_json, headers=headers, timeout=10)
        
        print(f"\n✅ Response Status: {response.status_code}")
        print(f"📄 Response Headers: {dict(response.headers)}")
        
        if response.text:
            print(f"📝 Response Body: {response.text}")
        
        if response.status_code == 200:
            print("\n✅ Webhook test successful!")
            return True
        else:
            print(f"\n❌ Webhook test failed with status: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("\n❌ Connection failed - is your Rails server running?")
        print("💡 Make sure Rails is running on the specified URL")
        return False
    except requests.exceptions.Timeout:
        print("\n❌ Request timed out after 10 seconds")
        return False
    except Exception as e:
        print(f"\n❌ Error: {str(e)}")
        return False

def main():
    parser = argparse.ArgumentParser(description='Test thumbnail webhook locally')
    parser.add_argument('--url', default='http://localhost:3000/wh/thumb_ready',
                        help='Webhook URL (default: http://localhost:3000/wh/thumb_ready)')
    parser.add_argument('--secret', required=True,
                        help='Webhook secret for authentication')
    parser.add_argument('--thumbnail-url', 
                        help='Custom thumbnail URL to send')
    parser.add_argument('--original-key',
                        help='Custom original file key')
    parser.add_argument('--production', action='store_true',
                        help='Use production URL (https://unlisters.com/wh/thumb_ready)')
    parser.add_argument('--custom-timestamp', 
                        help='Use custom timestamp instead of current time')
    parser.add_argument('--custom-payload',
                        help='Use custom JSON payload instead of default')
    
    args = parser.parse_args()
    
    # Use production URL if specified
    if args.production:
        url = 'https://unlisters.com/wh/thumb_ready'
    else:
        url = args.url
    
    # Run the test
    success = test_webhook(
        url=url,
        secret=args.secret,
        thumbnail_url=args.thumbnail_url,
        original_key=args.original_key,
        custom_timestamp=args.custom_timestamp,
        custom_payload=args.custom_payload
    )
    
    sys.exit(0 if success else 1)

if __name__ == '__main__':
    main()