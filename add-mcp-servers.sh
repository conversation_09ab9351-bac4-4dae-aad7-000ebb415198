#!/bin/bash

# <PERSON><PERSON>t to add MCP servers to Claude Code

echo "To add MCP servers to your current Claude Code session, you need to restart <PERSON> with the additional MCP servers."
echo ""
echo "Exit this session and run:"
echo ""
echo "claude \\"
echo "  --mcp gmail:\"npx -y @modelcontextprotocol/server-gmail\" \\"
echo "  --mcp puppeteer:\"npx -y @modelcontextprotocol/server-puppeteer\" \\"
echo "  --mcp filesystem:\"npx -y @modelcontextprotocol/server-filesystem /home/<USER>/Projects/unlisters_app\" \\"
echo "  --mcp postgres:\"npx -y @modelcontextprotocol/server-postgres postgresql://localhost/unlisters_development\""
echo ""
echo "Or use the helper script:"
echo "./scripts/claude-with-mcp.sh"
echo ""
echo "Note: MCP servers must be configured when starting Claude CLI. They cannot be added to an existing session."