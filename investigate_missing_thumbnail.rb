#!/usr/bin/env ruby
require_relative 'config/environment'

p = Project.find(1)
missing_file = p.private_files.find { |f| f.id == 363 }

if missing_file
  puts "=== Investigating missing thumbnail for file ID: #{missing_file.id} ==="
  puts "Filename: #{missing_file.filename}"
  puts "Content Type: #{missing_file.content_type}"
  puts "S3 Key: #{missing_file.blob.key}"
  puts "Created: #{missing_file.created_at}"
  puts "File size: #{missing_file.blob.byte_size} bytes"
  puts "Service: #{missing_file.blob.service_name}"
  puts ""
  
  # Check if file should trigger thumbnail generation
  puts "Should support thumbnails: #{missing_file.content_type.start_with?('image/') || missing_file.content_type == 'application/pdf'}"
  puts "Is image: #{missing_file.content_type.start_with?('image/')}"
  puts "Is variable (Rails check): #{missing_file.blob.variable?}"
  puts ""
  
  # Check what the webhook would expect
  puts "=== Expected Lambda thumbnail details ==="
  expected_thumbnail_key = missing_file.blob.key.gsub(/\.[^.]+$/, '.png')
  puts "Expected thumbnail S3 key: #{expected_thumbnail_key}"
  
  # Check if Lambda was supposed to process this
  puts ""
  puts "=== Lambda Processing Check ==="
  puts "File was uploaded to: #{missing_file.blob.service_name} service"
  puts "Should trigger Lambda: #{missing_file.blob.service_name == 'amazon_uploads'}"
  
  # Check if there are any orphaned thumbnail blobs
  puts ""
  puts "=== Checking for orphaned thumbnails ==="
  base_key = File.basename(missing_file.blob.key, '.*')
  orphaned_thumbnails = ActiveStorage::Blob
    .where(service_name: 'amazon_thumbnails')
    .where('key LIKE ? OR filename LIKE ?', "%#{base_key}%", "%#{base_key}%")
  
  puts "Found #{orphaned_thumbnails.count} potential orphaned thumbnails with similar key"
  orphaned_thumbnails.each do |blob|
    puts "  Blob ID: #{blob.id}, Key: #{blob.key}, Filename: #{blob.filename}"
    puts "  Attachments: #{blob.attachments.count}"
    blob.attachments.each do |att|
      puts "    -> #{att.record_type} ##{att.record_id} as #{att.name}"
    end
  end
  
  # Check upload table for this file
  puts ""
  puts "=== Upload tracking ==="
  # Search by S3 key in s3_key field
  uploads = Upload.where('s3_key LIKE ?', "%#{missing_file.blob.key}%")
  puts "Found #{uploads.count} Upload records with this S3 key"
  uploads.each do |upload|
    puts "  Upload ID: #{upload.id}, Status: #{upload.status}, S3 Key: #{upload.s3_key}"
    puts "  Target: #{upload.target_type} ##{upload.target_id}"
    puts "  Created: #{upload.created_at}"
    puts "  Filename: #{upload.original_filename}"
  end
  
  # Also search by filename
  uploads_by_name = Upload.where(original_filename: missing_file.filename.to_s)
  puts ""
  puts "Found #{uploads_by_name.count} Upload records with matching filename"
  uploads_by_name.each do |upload|
    puts "  Upload ID: #{upload.id}, Status: #{upload.status}, S3 Key: #{upload.s3_key}"
    puts "  Target: #{upload.target_type} ##{upload.target_id}"
    puts "  Created: #{upload.created_at}"
  end
  
else
  puts "File not found"
end