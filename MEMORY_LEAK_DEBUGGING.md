# Memory Leak Debugging Guide

## Problem Description
- Production app reaches 360MB RAM when a user logs in
- Memory continues climbing to 512MB limit even with no user activity
- This is a **memory leak** (not bloat) - objects not being garbage collected
- App has only ~4 users, so this isn't a scale issue

## Suspected Culprits (In Order of Likelihood)

### 1. **Geocoder Gem (HIGH PRIORITY)**
**Location**: `app/controllers/projects_controller.rb:47`
```ruby
.near(params[:location], radius, units: :km)
```

**Why Suspected**:
- Geocoding libraries often cache results internally
- External API calls may retain connection objects
- Coordinate calculations can create retained mathematical objects

**Test Strategy**: Comment out geocoder usage temporarily and test

### 2. **Complex Database Query with JOINs**
**Location**: `app/models/project.rb:184-217` - `Project.full_list_for_user`

**Why Suspected**:
- Complex query with multiple LEFT JOINs
- Raw SQL injection that might not be properly cleaned up
- ActiveRecord relation objects may not be fully garbage collected

**Evidence**:
```ruby
# This complex query builds massive relation objects
joins(<<~SQL)
  LEFT JOIN network_connections ON ...
  LEFT JOIN project_auths ON ...
  LEFT JOIN users ON users.id = projects.user_id
SQL
```

### 3. **Mass Email Sending**
**Location**: `app/controllers/projects_controller.rb:140-144, 171-174`

**Why Suspected**:
- Immediate email delivery with `deliver_now`
- Each email may retain mailer objects
- Loops through all admin/active users without batching

### 4. **Instance Variables Not Being Released**
**Location**: Various controller actions

**Why Suspected**:
- Complex instance variables like `@projects`, `@project_auths`
- Relations with includes that might retain associations

## Systematic Testing Approach

### Phase 1: Setup Memory Monitoring Tools

#### 1.1 Enhanced Memory Debugging Controller
Create `/memory_debug/stats` endpoint to track:
- Object counts by class
- Memory usage over time
- Heap dumps with allocation tracking

#### 1.2 Object Allocation Tracing
Enable with `MEMORY_PROFILING=1` environment variable

#### 1.3 Process Memory Monitoring
Monitor RSS (Resident Set Size) to track actual memory consumption

### Phase 2: Reproduce the Leak Locally

#### 2.1 Baseline Measurement
```bash
# Start server with memory profiling
MEMORY_PROFILING=1 bin/rails server

# In another terminal, get PID and initial memory
ps -eo pid,rss,command | grep "rails server"
# Note the RSS value (in KB)
```

#### 2.2 Take Initial Heap Dump
```bash
curl http://localhost:3000/memory_debug/heap_dump
# Creates tmp/heap-[timestamp].json
```

#### 2.3 Simulate Load
```bash
# Automated requests to trigger the leak
for i in {1..100}; do
  curl -b cookies.txt "http://localhost:3000/projects"
  sleep 0.1
done
```

#### 2.4 Monitor Memory Growth
```bash
# Check RSS again - has it grown significantly?
ps -eo pid,rss,command | grep "rails server"
```

#### 2.5 Take Final Heap Dump
```bash
curl http://localhost:3000/memory_debug/heap_dump
```

### Phase 3: Analyze with Heapy

#### 3.1 Compare Heap Dumps
```bash
cd tmp
heapy diff heap-before.json heap-after.json
```

#### 3.2 Interpret Results
Look for:
- **Large object counts** that grew significantly
- **File/line references** pointing to our suspected areas
- **Geocoder** related objects
- **ActiveRecord** relation objects
- **ActionMailer** objects

### Phase 4: Hypothesis Testing

#### 4.1 Test Geocoder (Most Likely Culprit)
```ruby
# In projects_controller.rb, temporarily comment out:
# .near(params[:location], radius, units: :km)

# Replace with simple location text search:
.where("unaccent(location) ILIKE unaccent(?) OR unaccent(country) ILIKE unaccent(?)", search_term, search_term)
```

Rerun the test. If memory leak significantly reduces = **GEOCODER IS THE CULPRIT**

#### 4.2 Test Complex Query
If geocoder isn't the issue, simplify the `full_list_for_user` query:
```ruby
# Replace with simpler version temporarily
Project.where(user: current_user).includes(user: :user_profile)
```

#### 4.3 Test Email Sending
Comment out the email loops in create/update actions.

#### 4.4 Test Instance Variables
Add explicit nil assignments at end of controller actions:
```ruby
def index
  # ... existing code ...
  
  # Explicit cleanup for testing
  @projects = nil
  @pagy = nil
  GC.start
end
```

## Expected Fixes by Culprit

### If Geocoder is the Issue:
1. **Update geocoder gem** to latest version
2. **Clear geocoder cache** manually: `Geocoder.cache.clear` after each request
3. **Use geocoder with timeout** and proper error handling
4. **Cache coordinates** in database instead of repeated geocoding

### If Database Query is the Issue:
1. **Simplify the query** - break into smaller queries
2. **Use select() to limit columns** returned
3. **Add explicit connection cleanup**
4. **Paginate more aggressively**

### If Email Sending is the Issue:
1. **Switch to background jobs** (`deliver_later`)
2. **Batch email sending**
3. **Add rate limiting**

### If Instance Variables are the Issue:
1. **Use local variables** instead of instance variables where possible
2. **Explicitly nil out** large instance variables
3. **Use streaming** for large datasets

## Monitoring in Production

### Quick Checks
```bash
# Check memory usage
ps aux | grep puma

# Force garbage collection (temporary relief)
# Add to a controller or rake task:
GC.start
ObjectSpace.garbage_collect
```

### Long-term Monitoring
- Enable Scout APM memory tracking
- Set up alerts for memory usage > 300MB
- Consider switching to Sidekiq for background jobs

## Expected Timeline
- **Phase 1 (Setup)**: 30 minutes
- **Phase 2 (Reproduce)**: 15 minutes
- **Phase 3 (Analyze)**: 15 minutes
- **Phase 4 (Test Fix)**: 30 minutes per hypothesis

**Total**: 2-3 hours to identify and fix the root cause.

## Success Criteria
1. **Memory baseline**: App stays under 200MB with normal usage
2. **No memory growth**: RSS doesn't continuously climb during idle periods
3. **Heap stability**: Object counts remain stable between requests
4. **Production stability**: No more 512MB limit hits

## Emergency Workaround
If testing takes too long, add this to `application_controller.rb`:
```ruby
after_action :force_garbage_collection, if: -> { Rails.env.production? }

private

def force_garbage_collection
  GC.start if rand < 0.1 # Only 10% of requests to avoid performance hit
end
```

This is a **temporary bandage** - the real leak still needs to be fixed.