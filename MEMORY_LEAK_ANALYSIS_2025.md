# Memory Leak Analysis & Resolution Plan - January 2025

## Current Situation Summary

- **Production Environment**: 512MB RAM limit on Render
- **Observed Behavior**: Memory climbs from ~200MB to 360MB with single user login, then continues to 512MB without activity
- **User Load**: Only 4 users occasionally visiting
- **Conclusion**: This is a **memory leak**, not normal memory bloat

## Analysis of Existing Fixes

### 1. Geocoder Cache Fix (Already Implemented)
**Status**: ✅ Implemented but needs verification

**Current Implementation**:
- Cache enabled in `config/initializers/geocoder.rb` with `cache: {}`
- `after_action :clear_geocoder_cache` in ProjectsController
- Comprehensive cache clearing in `clear_geocoder_cache_safe` method
- Uses official geocoder API: `lookup.cache.expire(:all)`
- Falls back to `Geocoder.config.cache.clear`
- Forces GC.start after cache clearing

**Assessment**: The implementation looks correct and comprehensive. However, we need to verify it's working as expected.

### 2. Suspected New Issue: `Project.where.not(latitude: nil, longitude: nil)`

You mentioned this query causes significant RAM bloat. This suggests the geocoder `.near()` scope might be loading too much data into memory.

## Systematic Testing Plan

### Phase 1: Verify Geocoder Fix Effectiveness

#### 1.1 Test Current Geocoder Memory Usage
```bash
# Start server with memory profiling
MEMORY_PROFILING=1 bin/rails server

# In another terminal, run the automated test
ruby test_memory_leak.rb
# Choose option 2 (Quick geocoder test)
```

#### 1.2 Analyze Geocoder Objects with Heapy
```bash
# After running the test, analyze the heap dumps
cd tmp
heapy diff heap-before.json heap-after.json | grep -i geocoder
```

#### 1.3 Check if Cache is Actually Being Cleared
Add temporary logging to verify cache clearing:

```ruby
# In app/controllers/projects_controller.rb, modify clear_geocoder_cache_safe:
def clear_geocoder_cache_safe
  begin
    Rails.logger.info "GEOCODER CACHE BEFORE: #{Geocoder.config.cache.size if Geocoder.config.cache.is_a?(Hash)}"
    
    # ... existing code ...
    
    Rails.logger.info "GEOCODER CACHE AFTER: #{Geocoder.config.cache.size if Geocoder.config.cache.is_a?(Hash)}"
  rescue => e
    Rails.logger.warn("Failed to clear geocoder cache: #{e.message}")
    GC.start
  end
end
```

### Phase 2: Investigate the `.near()` Scope Issue

#### 2.1 Understanding the Problem
The geocoder `.near()` scope likely:
1. Loads ALL projects with coordinates into memory
2. Performs distance calculations in Ruby (not database)
3. Creates large intermediate objects

#### 2.2 Test Memory Impact
Create a targeted test in Rails console:

```ruby
# rails console
require 'objspace'

# Baseline
GC.start
before_mb = `ps -o rss= -p #{Process.pid}`.to_i / 1024.0
before_objects = ObjectSpace.count_objects[:TOTAL]

# Run the problematic query
10.times do
  projects = Project.where.not(latitude: nil, longitude: nil)
  projects = projects.near('Prague', 20, units: :km)
  projects.load # Force query execution
end

# Measure after
GC.start
after_mb = `ps -o rss= -p #{Process.pid}`.to_i / 1024.0
after_objects = ObjectSpace.count_objects[:TOTAL]

puts "Memory growth: #{(after_mb - before_mb).round(2)} MB"
puts "Object growth: #{after_objects - before_objects}"
```

#### 2.3 Alternative Implementation Test
Test if using database-level filtering helps:

```ruby
# Instead of loading all geocoded projects:
projects = Project.where.not(latitude: nil, longitude: nil).near(location, radius)

# Try limiting the initial dataset:
projects = Project.where(
  latitude: lat_min..lat_max,
  longitude: lon_min..lon_max
).near(location, radius)
```

### Phase 3: Deep Analysis of `Project.full_list_for_user`

#### 3.1 Query Complexity Analysis
The query uses:
- Raw SQL with interpolation
- Multiple LEFT JOINs
- Complex WHERE conditions
- Custom SELECT with aliases

#### 3.2 Test Query Memory Usage
```ruby
# In rails console
user = User.first

# Test 1: Original query
GC.start
before = `ps -o rss= -p #{Process.pid}`.to_i / 1024.0
100.times { Project.full_list_for_user(user).load }
after = `ps -o rss= -p #{Process.pid}`.to_i / 1024.0
puts "Complex query memory growth: #{(after - before).round(2)} MB"

# Test 2: Simplified query
GC.start
before = `ps -o rss= -p #{Process.pid}`.to_i / 1024.0
100.times { Project.includes(user: :user_profile).where(user: user).load }
after = `ps -o rss= -p #{Process.pid}`.to_i / 1024.0
puts "Simple query memory growth: #{(after - before).round(2)} MB"
```

### Phase 4: Root Cause Solutions

#### Solution 1: Optimize Geocoder Usage

**Option A: Limit Initial Dataset**
```ruby
# In projects_controller.rb index action
if params[:location].present?
  # First, get a bounding box to limit the dataset
  coordinates = Geocoder.search(params[:location]).first&.coordinates
  if coordinates
    lat, lon = coordinates
    # Create a rough bounding box (adjust multiplier as needed)
    lat_range = (lat - 0.5)..(lat + 0.5)
    lon_range = (lon - 0.5)..(lon + 0.5)
    
    base_query = base_query.where(latitude: lat_range, longitude: lon_range)
                          .near(params[:location], radius, units: :km)
  end
end
```

**Option B: Use PostGIS for Spatial Queries**
```ruby
# This would require PostGIS extension but is much more efficient
# Projects within radius using database calculation
Project.where(
  "ST_DWithin(geography(ST_MakePoint(longitude, latitude)), geography(ST_MakePoint(?, ?)), ?)",
  search_lon, search_lat, radius * 1000 # meters
)
```

**Option C: Cache Geocoding Results**
```ruby
# Add to Project model
def self.cached_near(location, radius, options = {})
  cache_key = "projects_near_#{location}_#{radius}_#{options.to_s}"
  
  Rails.cache.fetch(cache_key, expires_in: 1.hour) do
    near(location, radius, options).to_a # Convert to array to cache results
  end
end
```

#### Solution 2: Optimize Complex Queries

**Refactor `full_list_for_user` to use associations:**
```ruby
scope :full_list_for_user, ->(user) {
  # Split into smaller, optimized queries
  user_projects = where(user: user)
  network_projects = joins(:user)
    .joins("INNER JOIN network_connections nc ON 
            (nc.invitee_id = #{user.id} AND nc.inviter_id = users.id) OR
            (nc.inviter_id = #{user.id} AND nc.invitee_id = users.id)")
    .where(network_only: true)
  semi_public_projects = where(semi_public: true)
  
  # Use UNION instead of complex OR conditions
  from("(#{user_projects.to_sql} UNION #{network_projects.to_sql} UNION #{semi_public_projects.to_sql}) AS projects")
}
```

#### Solution 3: Add Query Result Caching

```ruby
# In projects_controller.rb
def index
  cache_key = "projects_index_#{current_user&.id}_#{params.to_s}"
  
  @projects = Rails.cache.fetch(cache_key, expires_in: 5.minutes) do
    # ... existing query logic ...
    base_query.to_a # Convert to array for caching
  end
  
  @pagy, @projects = pagy_array(@projects, limit: 10)
end
```

### Phase 5: Emergency Fixes

#### 5.1 Immediate Production Relief
```ruby
# Add to config/initializers/memory_management.rb
if Rails.env.production?
  # Force periodic GC
  Thread.new do
    loop do
      sleep 300 # Every 5 minutes
      GC.start
      Rails.logger.info "Forced GC: Memory at #{`ps -o rss= -p #{Process.pid}`.to_i / 1024.0} MB"
    end
  end
  
  # Clear caches periodically
  ActiveSupport::Notifications.subscribe('process_action.action_controller') do |*args|
    if rand < 0.1 # 10% of requests
      Rails.cache.clear
      Geocoder.config.cache.clear if Geocoder.config.cache.is_a?(Hash)
    end
  end
end
```

#### 5.2 Request-based Memory Limit
```ruby
# Add to application_controller.rb
after_action :check_memory_usage

private

def check_memory_usage
  current_mb = `ps -o rss= -p #{Process.pid}`.to_i / 1024.0
  
  if current_mb > 400 # Getting close to limit
    Rails.logger.warn "High memory usage: #{current_mb}MB - forcing cleanup"
    
    # Clear all caches
    Rails.cache.clear
    Geocoder.config.cache.clear if Geocoder.config.cache.is_a?(Hash)
    ActiveRecord::Base.connection.clear_query_cache
    
    # Force GC
    GC.start
    ObjectSpace.garbage_collect
  end
end
```

## Testing Protocol

### Local Testing Steps

1. **Baseline Test**
   ```bash
   # Terminal 1
   MEMORY_PROFILING=1 bin/rails server
   
   # Terminal 2
   ruby test_memory_leak.rb
   # Choose option 1 (Full test)
   ```

2. **Specific Geocoder Test**
   ```bash
   # Test the geocoder with location searches
   curl "http://localhost:3000/memory_debug/test_projects_leak"
   ```

3. **Heap Analysis**
   ```bash
   cd tmp
   latest_before=$(ls -t heap-*.json | head -2 | tail -1)
   latest_after=$(ls -t heap-*.json | head -1)
   heapy diff $latest_before $latest_after
   ```

4. **Monitor Suspect Objects**
   ```bash
   # Check for growing object counts
   curl http://localhost:3000/memory_debug/stats | jq '.suspect_objects'
   ```

### Production Verification

After deploying fixes:

1. **Monitor memory via Render dashboard**
2. **Set up alerts for memory > 400MB**
3. **Log memory usage on each request**
4. **Track geocoder cache size**

## Success Metrics

1. **Memory stays under 300MB** during normal operation
2. **No continuous memory growth** when idle
3. **Geocoder cache size remains bounded**
4. **Response times remain under 500ms**

## Rollback Plan

If memory issues persist after fixes:

1. **Disable geocoding temporarily**:
   ```ruby
   # In projects_controller.rb
   # Comment out: .near(params[:location], radius, units: :km)
   ```

2. **Simplify complex queries**:
   ```ruby
   # Replace full_list_for_user with simpler version
   ```

3. **Enable aggressive GC**:
   ```ruby
   # Force GC after every request (performance impact!)
   after_action { GC.start }
   ```

## Next Steps Priority

1. **HIGH**: Verify geocoder cache is being cleared properly
2. **HIGH**: Test and optimize the `.near()` scope usage
3. **MEDIUM**: Refactor `full_list_for_user` query
4. **LOW**: Implement query result caching
5. **LOW**: Consider upgrading to 2GB RAM as safety margin

## Monitoring Commands

```bash
# Quick memory check
ps aux | grep puma | awk '{print $6/1024 " MB"}'

# Detailed memory stats
curl http://localhost:3000/memory_debug/stats | jq

# Force garbage collection
curl -X POST http://localhost:3000/memory_debug/force_gc

# Test geocoder leak
curl -X POST http://localhost:3000/memory_debug/test_projects_leak
```

## Conclusion

The memory leak appears to be a combination of:
1. Geocoder gem caching (partially fixed)
2. Inefficient spatial queries loading too much data
3. Complex ActiveRecord queries creating retained objects

Focus on optimizing the geocoder usage first, as it's the most likely culprit given the symptoms.