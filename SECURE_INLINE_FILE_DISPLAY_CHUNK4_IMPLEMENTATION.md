# Secure Inline File Display - Chunk 4 Implementation Documentation

## Overview
This document details the implementation of Chunk 4: Token Request API Endpoint for the secure inline file display system. This chunk creates an authenticated API endpoint that generates secure, time-limited tokens for specific files using cryptographic hashes instead of file IDs.

## Implementation Date
- Date: January 11, 2025
- Implementer: <PERSON> (AI Assistant)
- Status: Complete

## Chunk 4 Objectives
1. Create POST route `/projects/:id/request_file_token` 
2. Add token generation endpoint with proper authorization
3. Implement secure file lookup using hashes (not IDs)
4. Add rate limiting protection
5. Include comprehensive error handling and logging

## Files Modified

### 1. `/config/routes.rb`
Added new member route to projects resource:
```ruby
member do
  # ... existing routes ...
  post :request_file_token
end
```

### 2. `/app/controllers/projects_controller.rb`
Added new action `request_file_token` with:
- Rate limit checking
- Authorization using ActionPolicy (`authorize! @project, to: :view_full_details?`)
- Parameter validation
- Secure file lookup by hash
- Token generation scoped to user and file
- Comprehensive error handling
- Security logging

Key implementation details:
```ruby
def request_file_token
  # Rate limit check
  # Authorization check
  # Parameter validation
  # File lookup by secure hash
  # Token generation
  # JSON response with token, content_type, and expires_in
end
```

### 3. `/config/initializers/rack_attack.rb`
Added two new throttling rules:
- `secure_file_tokens/ip`: 30 requests per minute per IP
- `secure_file_tokens/user`: 50 requests per minute per authenticated user

## Security Features Implemented

### 1. Multi-Layer Authorization
- **Authentication**: Inherited from `before_action :authenticate_user!`
- **Project Authorization**: Uses `authorize! @project, to: :view_full_details?`
- **File Validation**: Ensures file belongs to the project via secure hash

### 2. Rate Limiting
- IP-based throttling (30 req/min)
- User-based throttling (50 req/min)
- Rate limit response with retry_after header

### 3. Secure File Identification
- Uses cryptographic hashes instead of sequential IDs
- Prevents enumeration attacks
- No file metadata exposed in responses

### 4. User-Scoped Tokens
- Tokens are generated with both user_id and file_id
- Token validation in streaming endpoint checks user context
- Prevents token sharing between users

### 5. Comprehensive Error Handling
- 400 Bad Request for missing parameters
- 403 Forbidden for unauthorized access
- 404 Not Found for invalid file hashes
- 429 Too Many Requests for rate limiting
- 500 Internal Server Error with safe error messages

### 6. Security Logging
- Logs successful token requests without exposing sensitive data
- Logs failures with error context
- Uses [SECURE_FILE] prefix for easy log filtering

## API Endpoint Specification

### Request
```
POST /projects/:id/request_file_token
Content-Type: application/json
X-CSRF-Token: [csrf_token]

{
  "file_hash": "cryptographic_hash_32_chars"
}
```

### Successful Response (200 OK)
```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "content_type": "application/pdf",
  "expires_in": 300
}
```

### Error Responses

#### 400 Bad Request
```json
{
  "error": "Invalid request - file_hash parameter required"
}
```

#### 403 Forbidden
- No response body (head :forbidden)
- Returned when user lacks authorization

#### 404 Not Found
```json
{
  "error": "File not found"
}
```

#### 429 Too Many Requests
```json
{
  "error": "Rate limit exceeded. Please try again later.",
  "retry_after": 60
}
```

#### 500 Internal Server Error
```json
{
  "error": "Internal server error"
}
```

## Dependencies
- **Chunk 2**: SecureFileTokenService for token generation
- **Chunk 2**: SecureFileAccess concern for hash-based file lookup
- **Chunk 3**: Streaming endpoint that consumes the generated tokens

## Integration Points

### 1. Token Service Integration
The endpoint uses `SecureFileTokenService.generate_token(file_attachment, current_user)` which:
- Embeds user_id in the token payload
- Sets 5-minute expiration
- Includes cryptographic nonce
- Returns JWT token string

### 2. File Hash System
Uses `@project.find_file_by_secure_hash(file_hash)` which:
- Performs secure comparison to prevent timing attacks
- Returns nil for non-existent hashes
- Works with HMAC-SHA256 hashes (32 chars)

### 3. Authorization System
Leverages existing ActionPolicy setup:
- `view_full_details?` policy method
- Checks project ownership or ProjectAuth with full_details access

## Testing Recommendations

### 1. Authorization Tests
- Test with project owner
- Test with user having full_details access
- Test with user having summary_only access (should fail)
- Test with non-connected user (should fail)

### 2. Parameter Validation Tests
- Test with valid file_hash
- Test with missing file_hash
- Test with invalid/non-existent file_hash

### 3. Rate Limiting Tests
- Test rapid requests from same IP
- Test rapid requests from same authenticated user
- Verify retry_after header in 429 responses

### 4. Token Validation Tests
- Verify token can be decoded
- Verify token contains correct user_id, file_id, project_id
- Verify token expires after 5 minutes

### 5. Security Tests
- Attempt to access files from other projects
- Attempt to reuse tokens after expiration
- Verify no file IDs or paths in responses

## Verification Steps

### 1. Manual Testing via Rails Console
```ruby
# Find a project with files
project = Project.joins(:private_files_attachments).first
user = project.user
file = project.private_files.first
file_hash = project.generate_secure_file_hash(file)

# Test token generation
token = SecureFileTokenService.generate_token(file, user)
payload = SecureFileTokenService.decode_token(token)
payload['user_id'] == user.id # Should be true
```

### 2. API Testing via cURL
```bash
# Get CSRF token first
csrf_token=$(curl -s http://localhost:3000 | grep csrf-token | sed -n 's/.*content="\([^"]*\)".*/\1/p')

# Request file token
curl -X POST http://localhost:3000/projects/1/request_file_token \
  -H "Content-Type: application/json" \
  -H "X-CSRF-Token: $csrf_token" \
  -H "Cookie: [session_cookie]" \
  -d '{"file_hash": "valid_hash_here"}'
```

### 3. Browser Console Testing
```javascript
// Assuming user is logged in and viewing a project
const fileHash = document.querySelector('.file-item').dataset.fileHash;
const projectId = document.querySelector('.file-item').dataset.projectId;

fetch(`/projects/${projectId}/request_file_token`, {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
  },
  body: JSON.stringify({ file_hash: fileHash })
})
.then(response => response.json())
.then(data => console.log(data));
```

## Best Practices Followed

1. **Principle of Least Privilege**: Only users with full_details access can request tokens
2. **Defense in Depth**: Multiple security layers (auth, authz, rate limiting, validation)
3. **Secure by Default**: No sensitive data in error messages or logs
4. **Performance Conscious**: Efficient queries, proper indexing assumed
5. **Maintainable Code**: Clear comments, consistent naming, follows Rails conventions

## Future Enhancements

1. **Token Revocation**: Implement token blacklisting for immediate revocation
2. **Audit Trail**: More detailed audit logging for compliance
3. **Dynamic Rate Limits**: Adjust limits based on user trust level
4. **Token Scope**: Add file-specific permissions (read-only vs download)
5. **Caching**: Cache authorization results for performance (with proper invalidation)

## Rollback Plan

If issues arise, rollback involves:
1. Remove the route from `config/routes.rb`
2. Remove the `request_file_token` action from ProjectsController
3. Remove rate limiting rules from rack_attack.rb
4. Deploy previous version

No database changes were made, so rollback is straightforward.

## Conclusion

Chunk 4 successfully implements a secure, rate-limited API endpoint for generating file access tokens. The implementation follows security best practices, integrates seamlessly with existing authorization systems, and provides a robust foundation for the secure inline file display feature.

The endpoint is production-ready with comprehensive error handling, security logging, and protection against common attack vectors including enumeration, brute force, and unauthorized access.