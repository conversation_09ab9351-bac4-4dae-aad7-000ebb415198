# Test script for full_access implementation

Rails.application.eager_load!

puts "Testing full_access implementation..."
puts "=" * 50

# Find or create test users
user1 = User.first
user2 = User.second

if !user1 || !user2
  puts "ERROR: Need at least 2 users in the database"
  exit 1
end

puts "User 1: #{user1.email}"
puts "User 2: #{user2.email}"

# Check if they are connected
connection = NetworkConnection.where(
  "(inviter_id = ? AND invitee_id = ?) OR (inviter_id = ? AND invitee_id = ?)",
  user1.id, user2.id, user2.id, user1.id
).first

puts "\nNetwork connection exists: #{connection.present?}"

# Find a project with full_access and network_only
project = Project.where(full_access: true, network_only: true).first

if project
  puts "\nFound project with full_access + network_only:"
  puts "- Project ID: #{project.id}"
  puts "- Owner: #{project.user.email}"
  puts "- Title: #{project.summary}"
  
  # Test the new user_has_access? method
  puts "\nTesting user_has_access? method:"
  puts "- Owner has access: #{project.user_has_access?(project.user)}"
  puts "- User1 has access: #{project.user_has_access?(user1)}"
  puts "- User2 has access: #{project.user_has_access?(user2)}"
  
  # Test by simulating a controller authorization check
  puts "\nSimulating controller authorization:"
  # Create a simple test class that includes ActionPolicy
  test_controller = Class.new do
    include ActionPolicy::Behaviour
    
    attr_reader :current_user
    
    def initialize(user)
      @current_user = user
    end
    
    authorize :user, through: :current_user
    
    def can_view_full_details?(project)
      allowed_to?(:view_full_details?, project)
    end
  end
  
  controller1 = test_controller.new(user1)
  controller2 = test_controller.new(user2)
  
  puts "- User1 can view full details: #{controller1.can_view_full_details?(project)}"
  puts "- User2 can view full details: #{controller2.can_view_full_details?(project)}"
else
  puts "\nNo project found with full_access + network_only. Creating one..."
  project = Project.new(
    user: user1,
    summary: "Test Project with Full Access",
    full_description: "This is a test project to verify full_access works",
    full_access: true,
    network_only: true,
    summary_only: false,
    semi_public: false,
    project_type: "other",
    category: "uncategorized",
    subcategory: "unspecified",
    approved: true
  )
  
  if project.save
    puts "Created test project successfully!"
  else
    puts "Failed to create project: #{project.errors.full_messages.join(', ')}"
  end
end

puts "\n" + "=" * 50
puts "Test complete!"