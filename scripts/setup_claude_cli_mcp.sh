#!/bin/bash

# Claude CLI MCP Server Configuration Script

echo "Setting up MCP servers for Claude CLI..."

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${BLUE}Current MCP servers installed globally:${NC}"
npm list -g | grep "@modelcontextprotocol" || echo "No MCP servers found"

echo -e "\n${BLUE}Installing additional MCP servers...${NC}"

# Check if servers are installed
if ! npm list -g @modelcontextprotocol/server-filesystem > /dev/null 2>&1; then
    echo "Installing filesystem server..."
    npm install -g @modelcontextprotocol/server-filesystem
fi

if ! npm list -g @modelcontextprotocol/server-postgres > /dev/null 2>&1; then
    echo "Installing postgres server..."
    npm install -g @modelcontextprotocol/server-postgres
fi

echo -e "\n${GREEN}MCP servers installed successfully!${NC}"

echo -e "\n${YELLOW}To use MCP servers with Claude CLI, you need to:${NC}"
echo "1. Start Claude CLI with MCP servers:"
echo "   claude --mcp filesystem:\"npx -y @modelcontextprotocol/server-filesystem /home/<USER>/Projects/unlisters_app\""
echo ""
echo "2. Or create an alias in your ~/.bashrc or ~/.zshrc:"
echo "   alias claude-app='claude --mcp filesystem:\"npx -y @modelcontextprotocol/server-filesystem /home/<USER>/Projects/unlisters_app\" --mcp postgres:\"npx -y @modelcontextprotocol/server-postgres postgresql://localhost/unlisters_development\"'"
echo ""
echo "3. For debugging MCP issues:"
echo "   claude --mcp-debug"
echo ""
echo -e "${BLUE}Available MCP servers:${NC}"
echo "- filesystem: Browse and read project files"
echo "- postgres: Database inspection and queries"
echo "- gmail: Email operations (already connected)"
echo "- puppeteer: Browser automation (already connected)"

# Create example usage script
cat > /home/<USER>/Projects/unlisters_app/scripts/claude-with-mcp.sh << 'EOF'
#!/bin/bash
# Start Claude CLI with Unlisters MCP servers

claude \
  --mcp filesystem:"npx -y @modelcontextprotocol/server-filesystem /home/<USER>/Projects/unlisters_app" \
  --mcp postgres:"npx -y @modelcontextprotocol/server-postgres postgresql://localhost/unlisters_development" \
  "$@"
EOF

chmod +x /home/<USER>/Projects/unlisters_app/scripts/claude-with-mcp.sh

echo -e "\n${GREEN}Created helper script: scripts/claude-with-mcp.sh${NC}"
echo "Use it to start Claude with all MCP servers configured for Unlisters"