# Security Policy

## Reporting Security Vulnerabilities

If you discover a security vulnerability in this application, please report it responsibly:

**Contact**: <EMAIL>  
**Response Time**: We aim to acknowledge reports within 48 hours

Please include:
- Description of the vulnerability
- Steps to reproduce
- Potential impact
- Any suggested remediation

## Scope

### In Scope
- The Unlisters web application (all endpoints)
- Authentication and authorization mechanisms
- File upload/download functionality
- API endpoints

### Out of Scope
- Third-party services (AWS S3, Resend, etc.)
- Denial of Service testing without prior approval
- Social engineering attacks
- Physical security testing

## Internal Security Documentation

For detailed security architecture and procedures:

- **Security Architecture & Controls**: See [`docs/security/THREAT_MODEL.md`](./docs/security/THREAT_MODEL.md)
- **Incident Response Procedures**: See [`docs/security/PLAYBOOK.md`](./docs/security/PLAYBOOK.md)
- **Security Testing Guide**: See [`SECURE_FILE_TESTING_GUIDE.md`](./SECURE_FILE_TESTING_GUIDE.md)

## Security Philosophy

This application implements defense-in-depth with multiple security layers:
1. Zero-trust architecture for file access
2. Cryptographic token-based authentication
3. Comprehensive input validation
4. Security headers and CSP enforcement
5. Rate limiting and DoS protection

Last Updated: January 11, 2025