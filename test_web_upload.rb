require 'net/http'
require 'uri'
require 'mime/types'

# Test file upload to running server
uri = URI('http://localhost:5000/uploads')

# Create form data
form_data = [
  ['files[]', File.open('README.md'), { filename: 'test.md', content_type: 'text/markdown' }],
  ['target_type', 'Project'],
  ['target_id', '69']
]

http = Net::HTTP.new(uri.host, uri.port)
request = Net::HTTP::Post.new(uri)

# Add authentication headers (you may need to adjust this)
request['User-Agent'] = 'Test Script'
request['Accept'] = '*/*'

# Create multipart form data
boundary = "----TestBoundary#{Time.now.to_i}"
request['Content-Type'] = "multipart/form-data; boundary=#{boundary}"

body = form_data.map do |key, value, options|
  if value.is_a?(File)
    filename = options[:filename] || File.basename(value.path)
    content_type = options[:content_type] || 'application/octet-stream'
    content = value.read
    value.rewind
    
    "--#{boundary}\r\n" +
    "Content-Disposition: form-data; name=\"#{key}\"; filename=\"#{filename}\"\r\n" +
    "Content-Type: #{content_type}\r\n\r\n" +
    "#{content}\r\n"
  else
    "--#{boundary}\r\n" +
    "Content-Disposition: form-data; name=\"#{key}\"\r\n\r\n" +
    "#{value}\r\n"
  end
end.join + "--#{boundary}--\r\n"

request.body = body

puts "Sending upload request..."
response = http.request(request)
puts "Response: #{response.code} #{response.message}"
puts "Body: #{response.body}"