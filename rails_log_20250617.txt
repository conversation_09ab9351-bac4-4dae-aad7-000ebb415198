16:23:49 web.1  | Started PATCH "/sk/projects/1" for 192.168.1.56 at 2025-06-17 16:23:49 +0200
16:23:49 web.1  |   User Load (0.9ms)  SELECT "users".* FROM "users" WHERE "users"."id" = $1 ORDER BY "users"."id" ASC LIMIT $2  [["id", 1], ["LIMIT", "[FILTERED]"]]
16:23:49 web.1  | Processing by ProjectsController#update as HTML
16:23:49 web.1  |   Parameters: {"authenticity_token"=>"[FILTERED]", "project"=>"[FILTERED]", "locale"=>"sk", "id"=>"1"}
16:23:49 web.1  |   UserProfile Load (0.5ms)  SELECT "user_profiles".* FROM "user_profiles" WHERE "user_profiles"."user_id" = $1 LIMIT $2  [["user_id", 1], ["LIMIT", "[FILTERED]"]]
16:23:49 web.1  |   ↳ app/controllers/application_controller.rb:74:in `set_locale'
16:23:49 web.1  |   Project Load (0.6ms)  SELECT "projects".* FROM "projects" WHERE "projects"."id" = $1 LIMIT $2  [["id", 1], ["LIMIT", "[FILTERED]"]]
16:23:49 web.1  |   ↳ app/controllers/projects_controller.rb:327:in `set_project'
16:23:49 web.1  |   ActiveStorage::Attachment Load (0.5ms)  SELECT "active_storage_attachments".* FROM "active_storage_attachments" WHERE "active_storage_attachments"."record_type" = $1 AND "active_storage_attachments"."name" = $2 AND "active_storage_attachments"."record_id" = $3  [["record_type", "[FILTERED]"], ["name", "private_files"], ["record_id", 1]]
16:23:49 web.1  |   ↳ app/controllers/projects_controller.rb:327:in `set_project'
16:23:49 web.1  | Unpermitted parameter: :units. Context: { controller: ProjectsController, action: update, request: #<ActionDispatch::Request:0x000075a4c8cda2e8>, params: {"_method"=>"[FILTERED]", "authenticity_token"=>"[FILTERED]", "project"=>"[FILTERED]", "controller"=>"[FILTERED]", "action"=>"[FILTERED]", "locale"=>"sk", "id"=>"1"} }
16:23:49 web.1  |   TRANSACTION (0.3ms)  BEGIN
16:23:49 web.1  |   ↳ app/controllers/projects_controller.rb:183:in `update'
16:23:50 web.1  |   User Load (0.8ms)  SELECT "users".* FROM "users" WHERE "users"."id" = $1 LIMIT $2  [["id", 1], ["LIMIT", "[FILTERED]"]]
16:23:50 web.1  |   ↳ app/controllers/projects_controller.rb:183:in `update'
16:23:50 web.1  |   ActiveStorage::Blob Create (1.3ms)  INSERT INTO "active_storage_blobs" ("key", "filename", "content_type", "metadata", "service_name", "byte_size", "checksum", "created_at") VALUES ($1, $2, $3, $4, $5, $6, $7, $8) RETURNING "id"  [["key", "qw7c0p55phxm65a9nirgyd69dtln"], ["filename", "1mb-dummy-png-download.png"], ["content_type", "[FILTERED]"], ["metadata", "[FILTERED]"], ["service_name", "amazon_uploads"], ["byte_size", "[FILTERED]"], ["checksum", "JOm9KSnmVuSEbzu7QLN4bA=="], ["created_at", "[FILTERED]"]]
16:23:50 web.1  |   ↳ app/controllers/projects_controller.rb:183:in `update'
16:23:50 web.1  |   ActiveStorage::Attachment Create (1.3ms)  INSERT INTO "active_storage_attachments" ("name", "record_type", "record_id", "blob_id", "created_at") VALUES ($1, $2, $3, $4, $5) RETURNING "id"  [["name", "private_files"], ["record_type", "[FILTERED]"], ["record_id", 1], ["blob_id", 197], ["created_at", "[FILTERED]"]]
16:23:50 web.1  |   ↳ app/controllers/projects_controller.rb:183:in `update'
16:23:50 web.1  |   Project Update (1.2ms)  UPDATE "projects" SET "updated_at" = $1 WHERE "projects"."id" = $2  [["updated_at", "[FILTERED]"], ["id", 1]]
16:23:50 web.1  |   ↳ app/controllers/projects_controller.rb:183:in `update'
16:23:50 web.1  |   TRANSACTION (2.0ms)  COMMIT
16:23:50 web.1  |   ↳ app/controllers/projects_controller.rb:183:in `update'
16:23:50 web.1  |   ActiveStorage::Attachment Load (0.7ms)  SELECT "active_storage_attachments".* FROM "active_storage_attachments" WHERE "active_storage_attachments"."record_id" = $1 AND "active_storage_attachments"."record_type" = $2 AND "active_storage_attachments"."name" = $3  [["record_id", 1], ["record_type", "[FILTERED]"], ["name", "pdf_thumbnails"]]
16:23:50 web.1  |   ↳ app/models/project.rb:261:in `thumbnail_for_file'
16:23:50 web.1  |   ActiveStorage::Blob Load (0.7ms)  SELECT "active_storage_blobs".* FROM "active_storage_blobs" WHERE "active_storage_blobs"."id" = $1 LIMIT $2  [["id", 170], ["LIMIT", "[FILTERED]"]]
16:23:50 web.1  |   ↳ app/models/project.rb:261:in `block in thumbnail_for_file'
16:23:50 web.1  |   ActiveStorage::Blob Load (0.6ms)  SELECT "active_storage_blobs".* FROM "active_storage_blobs" WHERE "active_storage_blobs"."id" = $1 LIMIT $2  [["id", 77], ["LIMIT", "[FILTERED]"]]
16:23:50 web.1  |   ↳ app/models/project.rb:261:in `block in thumbnail_for_file'
16:23:50 web.1  |   ActiveStorage::Blob Load (0.5ms)  SELECT "active_storage_blobs".* FROM "active_storage_blobs" WHERE "active_storage_blobs"."id" = $1 LIMIT $2  [["id", 81], ["LIMIT", "[FILTERED]"]]
16:23:50 web.1  |   ↳ app/models/project.rb:261:in `block in thumbnail_for_file'
16:23:50 web.1  |   ActiveStorage::Blob Load (0.4ms)  SELECT "active_storage_blobs".* FROM "active_storage_blobs" WHERE "active_storage_blobs"."id" = $1 LIMIT $2  [["id", 121], ["LIMIT", "[FILTERED]"]]
16:23:50 web.1  |   ↳ app/models/project.rb:261:in `block in thumbnail_for_file'
16:23:50 web.1  |   ActiveStorage::Blob Load (0.3ms)  SELECT "active_storage_blobs".* FROM "active_storage_blobs" WHERE "active_storage_blobs"."id" = $1 LIMIT $2  [["id", 180], ["LIMIT", "[FILTERED]"]]
16:23:50 web.1  |   ↳ app/models/project.rb:261:in `block in thumbnail_for_file'
16:23:50 web.1  |   ActiveStorage::Blob Load (0.5ms)  SELECT "active_storage_blobs".* FROM "active_storage_blobs" WHERE "active_storage_blobs"."id" = $1 LIMIT $2  [["id", 125], ["LIMIT", "[FILTERED]"]]
16:23:50 web.1  |   ↳ app/models/project.rb:261:in `block in thumbnail_for_file'
16:23:50 web.1  |   ActiveStorage::Blob Load (0.4ms)  SELECT "active_storage_blobs".* FROM "active_storage_blobs" WHERE "active_storage_blobs"."id" = $1 LIMIT $2  [["id", 184], ["LIMIT", "[FILTERED]"]]
16:23:50 web.1  |   ↳ app/models/project.rb:261:in `block in thumbnail_for_file'
16:23:50 web.1  |   ActiveStorage::Blob Load (0.2ms)  SELECT "active_storage_blobs".* FROM "active_storage_blobs" WHERE "active_storage_blobs"."id" = $1 LIMIT $2  [["id", 187], ["LIMIT", "[FILTERED]"]]
16:23:50 web.1  |   ↳ app/models/project.rb:261:in `block in thumbnail_for_file'
16:23:50 web.1  |   ActiveStorage::Blob Load (0.2ms)  SELECT "active_storage_blobs".* FROM "active_storage_blobs" WHERE "active_storage_blobs"."id" = $1 LIMIT $2  [["id", 134], ["LIMIT", "[FILTERED]"]]
16:23:50 web.1  |   ↳ app/models/project.rb:261:in `block in thumbnail_for_file'
16:23:50 web.1  |   ActiveStorage::Blob Load (0.2ms)  SELECT "active_storage_blobs".* FROM "active_storage_blobs" WHERE "active_storage_blobs"."id" = $1 LIMIT $2  [["id", 192], ["LIMIT", "[FILTERED]"]]
16:23:50 web.1  |   ↳ app/models/project.rb:261:in `block in thumbnail_for_file'
16:23:50 web.1  |   ActiveStorage::Blob Load (0.3ms)  SELECT "active_storage_blobs".* FROM "active_storage_blobs" WHERE "active_storage_blobs"."id" = $1 LIMIT $2  [["id", 138], ["LIMIT", "[FILTERED]"]]
16:23:50 web.1  |   ↳ app/models/project.rb:261:in `block in thumbnail_for_file'
16:23:50 web.1  |   ActiveStorage::Blob Load (0.3ms)  SELECT "active_storage_blobs".* FROM "active_storage_blobs" WHERE "active_storage_blobs"."id" = $1 LIMIT $2  [["id", 139], ["LIMIT", "[FILTERED]"]]
16:23:50 web.1  |   ↳ app/models/project.rb:261:in `block in thumbnail_for_file'
16:23:50 web.1  |   ActiveStorage::Blob Load (0.3ms)  SELECT "active_storage_blobs".* FROM "active_storage_blobs" WHERE "active_storage_blobs"."id" = $1 LIMIT $2  [["id", 195], ["LIMIT", "[FILTERED]"]]
16:23:50 web.1  |   ↳ app/models/project.rb:261:in `block in thumbnail_for_file'
16:23:50 web.1  |   ActiveStorage::Blob Load (0.3ms)  SELECT "active_storage_blobs".* FROM "active_storage_blobs" WHERE "active_storage_blobs"."id" = $1 LIMIT $2  [["id", 143], ["LIMIT", "[FILTERED]"]]
16:23:50 web.1  |   ↳ app/models/project.rb:261:in `block in thumbnail_for_file'
16:23:50 web.1  |   ActiveStorage::Blob Load (0.2ms)  SELECT "active_storage_blobs".* FROM "active_storage_blobs" WHERE "active_storage_blobs"."id" = $1 LIMIT $2  [["id", 144], ["LIMIT", "[FILTERED]"]]
16:23:50 web.1  |   ↳ app/models/project.rb:261:in `block in thumbnail_for_file'
16:23:50 web.1  | [ActiveJob]   TRANSACTION (0.3ms)  BEGIN
16:23:50 web.1  | [ActiveJob]   ↳ app/models/project.rb:308:in `generate_pdf_thumbnails'
16:23:50 web.1  | [ActiveJob]   GoodJob::Job Create (1.0ms)  INSERT INTO "good_jobs" ("id", "queue_name", "priority", "serialized_params", "scheduled_at", "performed_at", "finished_at", "error", "created_at", "updated_at", "active_job_id", "concurrency_key", "cron_key", "cron_at", "batch_id", "batch_callback_id", "executions_count", "job_class", "error_event", "labels", "locked_by_id", "locked_at") VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22) RETURNING "id"  [["id", "a2cab720-8804-4144-ad3d-82b97fbc859f"], ["queue_name", "default"], ["priority", "[FILTERED]"], ["serialized_params", "{\"job_class\":\"PdfThumbnailGenerationJob\",\"job_id\":\"a2cab720-8804-4144-ad3d-82b97fbc859f\",\"provider_job_id\":null,\"queue_name\":\"default\",\"priority\":null,\"arguments\":[{\"_aj_globalid\":\"gid://unlisters-app/Project/1\"}],\"executions\":0,\"exception_executions\":{},\"locale\":\"sk\",\"timezone\":\"CET\",\"enqueued_at\":\"2025-06-17T14:23:50Z\"}"], ["scheduled_at", "[FILTERED]"], ["performed_at", "[FILTERED]"], ["finished_at", "[FILTERED]"], ["error", nil], ["created_at", "[FILTERED]"], ["updated_at", "[FILTERED]"], ["active_job_id", "[FILTERED]"], ["concurrency_key", "[FILTERED]"], ["cron_key", "[FILTERED]"], ["cron_at", "[FILTERED]"], ["batch_id", "[FILTERED]"], ["batch_callback_id", "[FILTERED]"], ["executions_count", "[FILTERED]"], ["job_class", "PdfThumbnailGenerationJob"], ["error_event", "[FILTERED]"], ["labels", nil], ["locked_by_id", nil], ["locked_at", "[FILTERED]"]]
16:23:50 web.1  | [ActiveJob]   ↳ app/models/project.rb:308:in `generate_pdf_thumbnails'
16:23:50 web.1  | [ActiveJob]   TRANSACTION (3.7ms)  COMMIT
16:23:50 web.1  | [ActiveJob]   ↳ app/models/project.rb:308:in `generate_pdf_thumbnails'
16:23:50 web.1  | [ActiveJob] Enqueued PdfThumbnailGenerationJob (Job ID: a2cab720-8804-4144-ad3d-82b97fbc859f) to GoodJob(default) with arguments: #<GlobalID:0x000075a47df74878 @uri=#<URI::GID gid://unlisters-app/Project/1>>
16:23:50 web.1  |   GoodJob::Job Load (2.3ms)  SELECT "good_jobs"."id", "good_jobs"."queue_name", "good_jobs"."priority", "good_jobs"."serialized_params", "good_jobs"."scheduled_at", "good_jobs"."performed_at", "good_jobs"."finished_at", "good_jobs"."error", "good_jobs"."created_at", "good_jobs"."updated_at", "good_jobs"."active_job_id", "good_jobs"."concurrency_key", "good_jobs"."cron_key", "good_jobs"."cron_at", "good_jobs"."batch_id", "good_jobs"."batch_callback_id", "good_jobs"."executions_count", "good_jobs"."job_class", "good_jobs"."error_event", "good_jobs"."labels", "good_jobs"."locked_by_id", "good_jobs"."locked_at" FROM "good_jobs" WHERE "good_jobs"."id" IN (WITH "rows" AS  MATERIALIZED (SELECT "good_jobs"."id" FROM "good_jobs" WHERE "good_jobs"."queue_name" = $1 AND "good_jobs"."finished_at" IS NULL AND "good_jobs"."scheduled_at" <= $2 ORDER BY priority ASC NULLS LAST, "good_jobs"."created_at" ASC) SELECT "rows"."id" FROM "rows" WHERE pg_try_advisory_lock(('x' || substr(md5('good_jobs' || '-' || "rows"."id"::text), 1, 16))::bit(64)::bigint) LIMIT $3) ORDER BY priority ASC NULLS LAST, "good_jobs"."created_at" ASC  [["queue_name", "default"], ["scheduled_at", "[FILTERED]"], ["LIMIT", "[FILTERED]"]]
16:23:50 web.1  |   GoodJob::Job Load (0.6ms)  SELECT "good_jobs"."id", "good_jobs"."queue_name", "good_jobs"."priority", "good_jobs"."serialized_params", "good_jobs"."scheduled_at", "good_jobs"."performed_at", "good_jobs"."finished_at", "good_jobs"."error", "good_jobs"."created_at", "good_jobs"."updated_at", "good_jobs"."active_job_id", "good_jobs"."concurrency_key", "good_jobs"."cron_key", "good_jobs"."cron_at", "good_jobs"."batch_id", "good_jobs"."batch_callback_id", "good_jobs"."executions_count", "good_jobs"."job_class", "good_jobs"."error_event", "good_jobs"."labels", "good_jobs"."locked_by_id", "good_jobs"."locked_at" FROM "good_jobs" WHERE "good_jobs"."id" = $1 LIMIT $2  [["id", "a2cab720-8804-4144-ad3d-82b97fbc859f"], ["LIMIT", "[FILTERED]"]]
16:23:50 web.1  |   TRANSACTION (0.4ms)  BEGIN
16:23:50 web.1  |   GoodJob::Execution Create (1.5ms)  INSERT INTO "good_job_executions" ("created_at", "updated_at", "active_job_id", "job_class", "queue_name", "serialized_params", "scheduled_at", "finished_at", "error", "error_event", "error_backtrace", "process_id", "duration") VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13) RETURNING "id"  [["created_at", "[FILTERED]"], ["updated_at", "[FILTERED]"], ["active_job_id", "[FILTERED]"], ["job_class", "PdfThumbnailGenerationJob"], ["queue_name", "default"], ["serialized_params", "{\"job_id\":\"a2cab720-8804-4144-ad3d-82b97fbc859f\",\"locale\":\"sk\",\"priority\":null,\"timezone\":\"CET\",\"arguments\":[{\"_aj_globalid\":\"gid://unlisters-app/Project/1\"}],\"job_class\":\"PdfThumbnailGenerationJob\",\"executions\":0,\"queue_name\":\"default\",\"enqueued_at\":\"2025-06-17T14:23:50Z\",\"provider_job_id\":null,\"exception_executions\":{}}"], ["scheduled_at", "[FILTERED]"], ["finished_at", "[FILTERED]"], ["error", nil], ["error_event", "[FILTERED]"], ["error_backtrace", "[FILTERED]"], ["process_id", "85865815-aa48-40d5-b94a-15ce5da73e9c"], ["duration", "[FILTERED]"]]
16:23:50 web.1  |   GoodJob::Job Update (1.4ms)  UPDATE "good_jobs" SET "performed_at" = $1, "updated_at" = $2, "executions_count" = $3, "locked_by_id" = $4, "locked_at" = $5 WHERE "good_jobs"."id" = $6  [["performed_at", "[FILTERED]"], ["updated_at", "[FILTERED]"], ["executions_count", "[FILTERED]"], ["locked_by_id", "85865815-aa48-40d5-b94a-15ce5da73e9c"], ["locked_at", "[FILTERED]"], ["id", "a2cab720-8804-4144-ad3d-82b97fbc859f"]]
16:23:50 web.1  |   TRANSACTION (5.2ms)  COMMIT
16:23:50 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   Project Load (1.2ms)  SELECT "projects".* FROM "projects" WHERE "projects"."id" = $1 LIMIT $2  [["id", 1], ["LIMIT", "[FILTERED]"]]
16:23:50 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f] Performing PdfThumbnailGenerationJob (Job ID: a2cab720-8804-4144-ad3d-82b97fbc859f) from GoodJob(default) enqueued at 2025-06-17T14:23:50Z with arguments: #<GlobalID:0x000075a47de811c8 @uri=#<URI::GID gid://unlisters-app/Project/1>>
16:23:50 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   ActiveStorage::Attachment Load (0.7ms)  SELECT "active_storage_attachments".* FROM "active_storage_attachments" WHERE "active_storage_attachments"."record_id" = $1 AND "active_storage_attachments"."record_type" = $2 AND "active_storage_attachments"."name" = $3  [["record_id", 1], ["record_type", "[FILTERED]"], ["name", "private_files"]]
16:23:50 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   ↳ app/jobs/pdf_thumbnail_generation_job.rb:18:in `perform'
16:23:50 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   ActiveStorage::Blob Load (0.6ms)  SELECT "active_storage_blobs".* FROM "active_storage_blobs" WHERE "active_storage_blobs"."id" = $1 LIMIT $2  [["id", 197], ["LIMIT", "[FILTERED]"]]
16:23:50 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   ↳ app/models/project.rb:255:in `thumbnail_for_file'
16:23:50 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   ActiveStorage::Attachment Load (0.5ms)  SELECT "active_storage_attachments".* FROM "active_storage_attachments" WHERE "active_storage_attachments"."record_id" = $1 AND "active_storage_attachments"."record_type" = $2 AND "active_storage_attachments"."name" = $3  [["record_id", 1], ["record_type", "[FILTERED]"], ["name", "pdf_thumbnails"]]
16:23:50 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   ↳ app/models/project.rb:261:in `thumbnail_for_file'
16:23:50 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   ActiveStorage::Blob Load (0.3ms)  SELECT "active_storage_blobs".* FROM "active_storage_blobs" WHERE "active_storage_blobs"."id" = $1 LIMIT $2  [["id", 170], ["LIMIT", "[FILTERED]"]]
16:23:50 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   ↳ app/models/project.rb:261:in `block in thumbnail_for_file'
16:23:50 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   ActiveStorage::Blob Load (0.4ms)  SELECT "active_storage_blobs".* FROM "active_storage_blobs" WHERE "active_storage_blobs"."id" = $1 LIMIT $2  [["id", 77], ["LIMIT", "[FILTERED]"]]
16:23:50 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   ↳ app/models/project.rb:261:in `block in thumbnail_for_file'
16:23:50 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   ActiveStorage::Blob Load (0.4ms)  SELECT "active_storage_blobs".* FROM "active_storage_blobs" WHERE "active_storage_blobs"."id" = $1 LIMIT $2  [["id", 81], ["LIMIT", "[FILTERED]"]]
16:23:50 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   ↳ app/models/project.rb:261:in `block in thumbnail_for_file'
16:23:50 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   ActiveStorage::Blob Load (0.3ms)  SELECT "active_storage_blobs".* FROM "active_storage_blobs" WHERE "active_storage_blobs"."id" = $1 LIMIT $2  [["id", 121], ["LIMIT", "[FILTERED]"]]
16:23:50 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   ↳ app/models/project.rb:261:in `block in thumbnail_for_file'
16:23:50 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   ActiveStorage::Blob Load (0.3ms)  SELECT "active_storage_blobs".* FROM "active_storage_blobs" WHERE "active_storage_blobs"."id" = $1 LIMIT $2  [["id", 180], ["LIMIT", "[FILTERED]"]]
16:23:50 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   ↳ app/models/project.rb:261:in `block in thumbnail_for_file'
16:23:50 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   ActiveStorage::Blob Load (0.3ms)  SELECT "active_storage_blobs".* FROM "active_storage_blobs" WHERE "active_storage_blobs"."id" = $1 LIMIT $2  [["id", 125], ["LIMIT", "[FILTERED]"]]
16:23:50 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   ↳ app/models/project.rb:261:in `block in thumbnail_for_file'
16:23:50 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   ActiveStorage::Blob Load (0.2ms)  SELECT "active_storage_blobs".* FROM "active_storage_blobs" WHERE "active_storage_blobs"."id" = $1 LIMIT $2  [["id", 184], ["LIMIT", "[FILTERED]"]]
16:23:50 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   ↳ app/models/project.rb:261:in `block in thumbnail_for_file'
16:23:50 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   ActiveStorage::Blob Load (0.2ms)  SELECT "active_storage_blobs".* FROM "active_storage_blobs" WHERE "active_storage_blobs"."id" = $1 LIMIT $2  [["id", 187], ["LIMIT", "[FILTERED]"]]
16:23:50 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   ↳ app/models/project.rb:261:in `block in thumbnail_for_file'
16:23:50 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   ActiveStorage::Blob Load (0.2ms)  SELECT "active_storage_blobs".* FROM "active_storage_blobs" WHERE "active_storage_blobs"."id" = $1 LIMIT $2  [["id", 134], ["LIMIT", "[FILTERED]"]]
16:23:50 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   ↳ app/models/project.rb:261:in `block in thumbnail_for_file'
16:23:50 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   ActiveStorage::Blob Load (0.3ms)  SELECT "active_storage_blobs".* FROM "active_storage_blobs" WHERE "active_storage_blobs"."id" = $1 LIMIT $2  [["id", 192], ["LIMIT", "[FILTERED]"]]
16:23:50 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   ↳ app/models/project.rb:261:in `block in thumbnail_for_file'
16:23:50 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   ActiveStorage::Blob Load (0.3ms)  SELECT "active_storage_blobs".* FROM "active_storage_blobs" WHERE "active_storage_blobs"."id" = $1 LIMIT $2  [["id", 138], ["LIMIT", "[FILTERED]"]]
16:23:50 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   ↳ app/models/project.rb:261:in `block in thumbnail_for_file'
16:23:50 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   ActiveStorage::Blob Load (0.2ms)  SELECT "active_storage_blobs".* FROM "active_storage_blobs" WHERE "active_storage_blobs"."id" = $1 LIMIT $2  [["id", 139], ["LIMIT", "[FILTERED]"]]
16:23:50 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   ↳ app/models/project.rb:261:in `block in thumbnail_for_file'
16:23:50 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   ActiveStorage::Blob Load (0.2ms)  SELECT "active_storage_blobs".* FROM "active_storage_blobs" WHERE "active_storage_blobs"."id" = $1 LIMIT $2  [["id", 195], ["LIMIT", "[FILTERED]"]]
16:23:50 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   ↳ app/models/project.rb:261:in `block in thumbnail_for_file'
16:23:50 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   ActiveStorage::Blob Load (0.2ms)  SELECT "active_storage_blobs".* FROM "active_storage_blobs" WHERE "active_storage_blobs"."id" = $1 LIMIT $2  [["id", 143], ["LIMIT", "[FILTERED]"]]
16:23:50 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   ↳ app/models/project.rb:261:in `block in thumbnail_for_file'
16:23:50 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   ActiveStorage::Blob Load (0.3ms)  SELECT "active_storage_blobs".* FROM "active_storage_blobs" WHERE "active_storage_blobs"."id" = $1 LIMIT $2  [["id", 144], ["LIMIT", "[FILTERED]"]]
16:23:50 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   ↳ app/models/project.rb:261:in `block in thumbnail_for_file'
16:23:50 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   S3 Storage (393.1ms) Checked if file exists at key: qw7c0p55phxm65a9nirgyd69dtln (no)
16:23:50 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   TRANSACTION (0.3ms)  BEGIN
16:23:50 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   GoodJob::Job Update (0.7ms)  UPDATE "good_jobs" SET "performed_at" = $1, "serialized_params" = $2, "scheduled_at" = $3, "created_at" = $4, "updated_at" = $5 WHERE "good_jobs"."id" = $6  [["performed_at", "[FILTERED]"], ["serialized_params", "{\"job_class\":\"PdfThumbnailGenerationJob\",\"job_id\":\"a2cab720-8804-4144-ad3d-82b97fbc859f\",\"provider_job_id\":\"a2cab720-8804-4144-ad3d-82b97fbc859f\",\"queue_name\":\"default\",\"priority\":null,\"arguments\":[{\"_aj_globalid\":\"gid://unlisters-app/Project/1\"}],\"executions\":1,\"exception_executions\":{\"[PdfThumbnailGenerationJob::FileNotReadyError]\":1},\"locale\":\"sk\",\"timezone\":\"CET\",\"enqueued_at\":\"2025-06-17T14:23:50Z\"}"], ["scheduled_at", "[FILTERED]"], ["created_at", "[FILTERED]"], ["updated_at", "[FILTERED]"], ["id", "a2cab720-8804-4144-ad3d-82b97fbc859f"]]
16:23:50 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   TRANSACTION (5.0ms)  COMMIT
16:23:50 web.1  | [GoodJob] Notifier received payload: {"queue_name":"default","scheduled_at":"2025-06-17T16:23:53.809+02:00"}
16:23:50 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   SQL (2.7ms)  NOTIFY good_job, '{"queue_name":"default","scheduled_at":"2025-06-17T16:23:53.809+02:00"}'
16:23:50 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f] Enqueued PdfThumbnailGenerationJob (Job ID: a2cab720-8804-4144-ad3d-82b97fbc859f) to GoodJob(default) at 2025-06-17 14:23:53 UTC with arguments: #<GlobalID:0x000075a47dd8c218 @uri=#<URI::GID gid://unlisters-app/Project/1>>
16:23:50 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f] Retrying PdfThumbnailGenerationJob in 3 seconds, due to a PdfThumbnailGenerationJob::FileNotReadyError.
16:23:50 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f] Performed PdfThumbnailGenerationJob (Job ID: a2cab720-8804-4144-ad3d-82b97fbc859f) from GoodJob(default) in 481.62ms
16:23:50 web.1  | [GoodJob] [2548303] [GoodJob::Scheduler(queues=default max_threads=3)-thread-2] Executed GoodJob a2cab720-8804-4144-ad3d-82b97fbc859f
16:23:50 web.1  |   TRANSACTION (0.2ms)  BEGIN
16:23:50 web.1  |   GoodJob::Execution Update (2.1ms)  UPDATE "good_job_executions" SET "updated_at" = $1, "finished_at" = $2, "error" = $3, "error_event" = $4, "error_backtrace" = $5, "duration" = $6 WHERE "good_job_executions"."id" = $7  [["updated_at", "[FILTERED]"], ["finished_at", "[FILTERED]"], ["error", "PdfThumbnailGenerationJob::FileNotReadyError: Files not yet available on S3 for Project 1: qw7c0p55phxm65a9nirgyd69dtln. Will retry with exponential backoff."], ["error_event", "[FILTERED]"], ["error_backtrace", "[FILTERED]"], ["duration", "[FILTERED]"], ["id", "b3451765-3355-429f-bf57-8949d3afe93d"]]
16:23:50 web.1  |   GoodJob::Job Update (0.7ms)  UPDATE "good_jobs" SET "locked_by_id" = $1, "locked_at" = $2, "error" = $3, "updated_at" = $4, "error_event" = $5 WHERE "good_jobs"."id" = $6  [["locked_by_id", nil], ["locked_at", "[FILTERED]"], ["error", "PdfThumbnailGenerationJob::FileNotReadyError: Files not yet available on S3 for Project 1: qw7c0p55phxm65a9nirgyd69dtln. Will retry with exponential backoff."], ["updated_at", "[FILTERED]"], ["error_event", "[FILTERED]"], ["id", "a2cab720-8804-4144-ad3d-82b97fbc859f"]]
16:23:50 web.1  |   TRANSACTION (2.9ms)  COMMIT
16:23:50 web.1  |   GoodJob::Lockable Advisory Unlock (0.4ms)  SELECT pg_advisory_unlock(('x'||substr(md5($1::text), 1, 16))::bit(64)::bigint) AS unlocked  [["key", "good_jobs-a2cab720-8804-4144-ad3d-82b97fbc859f"]]
16:23:50 web.1  |   GoodJob::Job Load (0.9ms)  SELECT "good_jobs"."id", "good_jobs"."queue_name", "good_jobs"."priority", "good_jobs"."serialized_params", "good_jobs"."scheduled_at", "good_jobs"."performed_at", "good_jobs"."finished_at", "good_jobs"."error", "good_jobs"."created_at", "good_jobs"."updated_at", "good_jobs"."active_job_id", "good_jobs"."concurrency_key", "good_jobs"."cron_key", "good_jobs"."cron_at", "good_jobs"."batch_id", "good_jobs"."batch_callback_id", "good_jobs"."executions_count", "good_jobs"."job_class", "good_jobs"."error_event", "good_jobs"."labels", "good_jobs"."locked_by_id", "good_jobs"."locked_at" FROM "good_jobs" WHERE "good_jobs"."id" IN (WITH "rows" AS  MATERIALIZED (SELECT "good_jobs"."id" FROM "good_jobs" WHERE "good_jobs"."queue_name" = $1 AND "good_jobs"."finished_at" IS NULL AND "good_jobs"."scheduled_at" <= $2 ORDER BY priority ASC NULLS LAST, "good_jobs"."created_at" ASC) SELECT "rows"."id" FROM "rows" WHERE pg_try_advisory_lock(('x' || substr(md5('good_jobs' || '-' || "rows"."id"::text), 1, 16))::bit(64)::bigint) LIMIT $3) ORDER BY priority ASC NULLS LAST, "good_jobs"."created_at" ASC  [["queue_name", "default"], ["scheduled_at", "[FILTERED]"], ["LIMIT", "[FILTERED]"]]
16:23:53 web.1  |   GoodJob::Job Load (1.4ms)  SELECT "good_jobs"."id", "good_jobs"."queue_name", "good_jobs"."priority", "good_jobs"."serialized_params", "good_jobs"."scheduled_at", "good_jobs"."performed_at", "good_jobs"."finished_at", "good_jobs"."error", "good_jobs"."created_at", "good_jobs"."updated_at", "good_jobs"."active_job_id", "good_jobs"."concurrency_key", "good_jobs"."cron_key", "good_jobs"."cron_at", "good_jobs"."batch_id", "good_jobs"."batch_callback_id", "good_jobs"."executions_count", "good_jobs"."job_class", "good_jobs"."error_event", "good_jobs"."labels", "good_jobs"."locked_by_id", "good_jobs"."locked_at" FROM "good_jobs" WHERE "good_jobs"."id" IN (WITH "rows" AS  MATERIALIZED (SELECT "good_jobs"."id" FROM "good_jobs" WHERE "good_jobs"."finished_at" IS NULL AND "good_jobs"."scheduled_at" <= $1 ORDER BY priority ASC NULLS LAST, "good_jobs"."created_at" ASC) SELECT "rows"."id" FROM "rows" WHERE pg_try_advisory_lock(('x' || substr(md5('good_jobs' || '-' || "rows"."id"::text), 1, 16))::bit(64)::bigint) LIMIT $2) ORDER BY priority ASC NULLS LAST, "good_jobs"."created_at" ASC  [["scheduled_at", "[FILTERED]"], ["LIMIT", "[FILTERED]"]]
16:23:53 web.1  |   GoodJob::Job Load (0.6ms)  SELECT "good_jobs"."id", "good_jobs"."queue_name", "good_jobs"."priority", "good_jobs"."serialized_params", "good_jobs"."scheduled_at", "good_jobs"."performed_at", "good_jobs"."finished_at", "good_jobs"."error", "good_jobs"."created_at", "good_jobs"."updated_at", "good_jobs"."active_job_id", "good_jobs"."concurrency_key", "good_jobs"."cron_key", "good_jobs"."cron_at", "good_jobs"."batch_id", "good_jobs"."batch_callback_id", "good_jobs"."executions_count", "good_jobs"."job_class", "good_jobs"."error_event", "good_jobs"."labels", "good_jobs"."locked_by_id", "good_jobs"."locked_at" FROM "good_jobs" WHERE "good_jobs"."id" = $1 LIMIT $2  [["id", "a2cab720-8804-4144-ad3d-82b97fbc859f"], ["LIMIT", "[FILTERED]"]]
16:23:53 web.1  |   TRANSACTION (0.3ms)  BEGIN
16:23:53 web.1  |   GoodJob::Execution Create (0.5ms)  INSERT INTO "good_job_executions" ("created_at", "updated_at", "active_job_id", "job_class", "queue_name", "serialized_params", "scheduled_at", "finished_at", "error", "error_event", "error_backtrace", "process_id", "duration") VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13) RETURNING "id"  [["created_at", "[FILTERED]"], ["updated_at", "[FILTERED]"], ["active_job_id", "[FILTERED]"], ["job_class", "PdfThumbnailGenerationJob"], ["queue_name", "default"], ["serialized_params", "{\"job_id\":\"a2cab720-8804-4144-ad3d-82b97fbc859f\",\"locale\":\"sk\",\"priority\":null,\"timezone\":\"CET\",\"arguments\":[{\"_aj_globalid\":\"gid://unlisters-app/Project/1\"}],\"job_class\":\"PdfThumbnailGenerationJob\",\"executions\":1,\"queue_name\":\"default\",\"enqueued_at\":\"2025-06-17T14:23:50Z\",\"provider_job_id\":\"a2cab720-8804-4144-ad3d-82b97fbc859f\",\"exception_executions\":{\"[PdfThumbnailGenerationJob::FileNotReadyError]\":1}}"], ["scheduled_at", "[FILTERED]"], ["finished_at", "[FILTERED]"], ["error", nil], ["error_event", "[FILTERED]"], ["error_backtrace", "[FILTERED]"], ["process_id", "85865815-aa48-40d5-b94a-15ce5da73e9c"], ["duration", "[FILTERED]"]]
16:23:53 web.1  |   GoodJob::Job Update (0.6ms)  UPDATE "good_jobs" SET "performed_at" = $1, "updated_at" = $2, "executions_count" = $3, "locked_by_id" = $4, "locked_at" = $5 WHERE "good_jobs"."id" = $6  [["performed_at", "[FILTERED]"], ["updated_at", "[FILTERED]"], ["executions_count", "[FILTERED]"], ["locked_by_id", "85865815-aa48-40d5-b94a-15ce5da73e9c"], ["locked_at", "[FILTERED]"], ["id", "a2cab720-8804-4144-ad3d-82b97fbc859f"]]
16:23:53 web.1  |   TRANSACTION (5.0ms)  COMMIT
16:23:53 web.1  |   GoodJob::Job Load (1.9ms)  SELECT "good_jobs"."id", "good_jobs"."queue_name", "good_jobs"."priority", "good_jobs"."serialized_params", "good_jobs"."scheduled_at", "good_jobs"."performed_at", "good_jobs"."finished_at", "good_jobs"."error", "good_jobs"."created_at", "good_jobs"."updated_at", "good_jobs"."active_job_id", "good_jobs"."concurrency_key", "good_jobs"."cron_key", "good_jobs"."cron_at", "good_jobs"."batch_id", "good_jobs"."batch_callback_id", "good_jobs"."executions_count", "good_jobs"."job_class", "good_jobs"."error_event", "good_jobs"."labels", "good_jobs"."locked_by_id", "good_jobs"."locked_at" FROM "good_jobs" WHERE "good_jobs"."id" IN (WITH "rows" AS  MATERIALIZED (SELECT "good_jobs"."id" FROM "good_jobs" WHERE "good_jobs"."queue_name" = $1 AND "good_jobs"."finished_at" IS NULL AND "good_jobs"."scheduled_at" <= $2 ORDER BY priority ASC NULLS LAST, "good_jobs"."created_at" ASC) SELECT "rows"."id" FROM "rows" WHERE pg_try_advisory_lock(('x' || substr(md5('good_jobs' || '-' || "rows"."id"::text), 1, 16))::bit(64)::bigint) LIMIT $3) ORDER BY priority ASC NULLS LAST, "good_jobs"."created_at" ASC  [["queue_name", "default"], ["scheduled_at", "[FILTERED]"], ["LIMIT", "[FILTERED]"]]
16:23:53 web.1  |   GoodJob::Job Load (1.2ms)  SELECT "good_jobs"."id", "good_jobs"."queue_name", "good_jobs"."priority", "good_jobs"."serialized_params", "good_jobs"."scheduled_at", "good_jobs"."performed_at", "good_jobs"."finished_at", "good_jobs"."error", "good_jobs"."created_at", "good_jobs"."updated_at", "good_jobs"."active_job_id", "good_jobs"."concurrency_key", "good_jobs"."cron_key", "good_jobs"."cron_at", "good_jobs"."batch_id", "good_jobs"."batch_callback_id", "good_jobs"."executions_count", "good_jobs"."job_class", "good_jobs"."error_event", "good_jobs"."labels", "good_jobs"."locked_by_id", "good_jobs"."locked_at" FROM "good_jobs" WHERE "good_jobs"."id" IN (WITH "rows" AS  MATERIALIZED (SELECT "good_jobs"."id" FROM "good_jobs" WHERE "good_jobs"."finished_at" IS NULL AND "good_jobs"."scheduled_at" <= $1 ORDER BY priority ASC NULLS LAST, "good_jobs"."created_at" ASC) SELECT "rows"."id" FROM "rows" WHERE pg_try_advisory_lock(('x' || substr(md5('good_jobs' || '-' || "rows"."id"::text), 1, 16))::bit(64)::bigint) LIMIT $2) ORDER BY priority ASC NULLS LAST, "good_jobs"."created_at" ASC  [["scheduled_at", "[FILTERED]"], ["LIMIT", "[FILTERED]"]]
16:23:53 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   Project Load (0.4ms)  SELECT "projects".* FROM "projects" WHERE "projects"."id" = $1 LIMIT $2  [["id", 1], ["LIMIT", "[FILTERED]"]]
16:23:53 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f] Performing PdfThumbnailGenerationJob (Job ID: a2cab720-8804-4144-ad3d-82b97fbc859f) from GoodJob(default) enqueued at 2025-06-17T14:23:50Z with arguments: #<GlobalID:0x000075a47d749a68 @uri=#<URI::GID gid://unlisters-app/Project/1>>
16:23:53 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   ActiveStorage::Attachment Load (0.4ms)  SELECT "active_storage_attachments".* FROM "active_storage_attachments" WHERE "active_storage_attachments"."record_id" = $1 AND "active_storage_attachments"."record_type" = $2 AND "active_storage_attachments"."name" = $3  [["record_id", 1], ["record_type", "[FILTERED]"], ["name", "private_files"]]
16:23:53 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   ↳ app/jobs/pdf_thumbnail_generation_job.rb:18:in `perform'
16:23:53 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   ActiveStorage::Blob Load (0.3ms)  SELECT "active_storage_blobs".* FROM "active_storage_blobs" WHERE "active_storage_blobs"."id" = $1 LIMIT $2  [["id", 197], ["LIMIT", "[FILTERED]"]]
16:23:53 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   ↳ app/models/project.rb:255:in `thumbnail_for_file'
16:23:53 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   ActiveStorage::Attachment Load (0.4ms)  SELECT "active_storage_attachments".* FROM "active_storage_attachments" WHERE "active_storage_attachments"."record_id" = $1 AND "active_storage_attachments"."record_type" = $2 AND "active_storage_attachments"."name" = $3  [["record_id", 1], ["record_type", "[FILTERED]"], ["name", "pdf_thumbnails"]]
16:23:53 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   ↳ app/models/project.rb:261:in `thumbnail_for_file'
16:23:53 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   ActiveStorage::Blob Load (0.3ms)  SELECT "active_storage_blobs".* FROM "active_storage_blobs" WHERE "active_storage_blobs"."id" = $1 LIMIT $2  [["id", 170], ["LIMIT", "[FILTERED]"]]
16:23:53 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   ↳ app/models/project.rb:261:in `block in thumbnail_for_file'
16:23:53 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   ActiveStorage::Blob Load (0.2ms)  SELECT "active_storage_blobs".* FROM "active_storage_blobs" WHERE "active_storage_blobs"."id" = $1 LIMIT $2  [["id", 77], ["LIMIT", "[FILTERED]"]]
16:23:53 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   ↳ app/models/project.rb:261:in `block in thumbnail_for_file'
16:23:53 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   ActiveStorage::Blob Load (0.3ms)  SELECT "active_storage_blobs".* FROM "active_storage_blobs" WHERE "active_storage_blobs"."id" = $1 LIMIT $2  [["id", 81], ["LIMIT", "[FILTERED]"]]
16:23:53 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   ↳ app/models/project.rb:261:in `block in thumbnail_for_file'
16:23:53 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   ActiveStorage::Blob Load (0.2ms)  SELECT "active_storage_blobs".* FROM "active_storage_blobs" WHERE "active_storage_blobs"."id" = $1 LIMIT $2  [["id", 121], ["LIMIT", "[FILTERED]"]]
16:23:53 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   ↳ app/models/project.rb:261:in `block in thumbnail_for_file'
16:23:53 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   ActiveStorage::Blob Load (0.2ms)  SELECT "active_storage_blobs".* FROM "active_storage_blobs" WHERE "active_storage_blobs"."id" = $1 LIMIT $2  [["id", 180], ["LIMIT", "[FILTERED]"]]
16:23:53 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   ↳ app/models/project.rb:261:in `block in thumbnail_for_file'
16:23:53 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   ActiveStorage::Blob Load (0.3ms)  SELECT "active_storage_blobs".* FROM "active_storage_blobs" WHERE "active_storage_blobs"."id" = $1 LIMIT $2  [["id", 125], ["LIMIT", "[FILTERED]"]]
16:23:53 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   ↳ app/models/project.rb:261:in `block in thumbnail_for_file'
16:23:53 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   ActiveStorage::Blob Load (0.7ms)  SELECT "active_storage_blobs".* FROM "active_storage_blobs" WHERE "active_storage_blobs"."id" = $1 LIMIT $2  [["id", 184], ["LIMIT", "[FILTERED]"]]
16:23:53 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   ↳ app/models/project.rb:261:in `block in thumbnail_for_file'
16:23:53 web.1  |   GoodJob::Job Load (1.1ms)  SELECT "good_jobs"."id", "good_jobs"."queue_name", "good_jobs"."priority", "good_jobs"."serialized_params", "good_jobs"."scheduled_at", "good_jobs"."performed_at", "good_jobs"."finished_at", "good_jobs"."error", "good_jobs"."created_at", "good_jobs"."updated_at", "good_jobs"."active_job_id", "good_jobs"."concurrency_key", "good_jobs"."cron_key", "good_jobs"."cron_at", "good_jobs"."batch_id", "good_jobs"."batch_callback_id", "good_jobs"."executions_count", "good_jobs"."job_class", "good_jobs"."error_event", "good_jobs"."labels", "good_jobs"."locked_by_id", "good_jobs"."locked_at" FROM "good_jobs" WHERE "good_jobs"."id" IN (WITH "rows" AS  MATERIALIZED (SELECT "good_jobs"."id" FROM "good_jobs" WHERE "good_jobs"."queue_name" = $1 AND "good_jobs"."finished_at" IS NULL AND "good_jobs"."scheduled_at" <= $2 ORDER BY priority ASC NULLS LAST, "good_jobs"."created_at" ASC) SELECT "rows"."id" FROM "rows" WHERE pg_try_advisory_lock(('x' || substr(md5('good_jobs' || '-' || "rows"."id"::text), 1, 16))::bit(64)::bigint) LIMIT $3) ORDER BY priority ASC NULLS LAST, "good_jobs"."created_at" ASC  [["queue_name", "default"], ["scheduled_at", "[FILTERED]"], ["LIMIT", "[FILTERED]"]]
16:23:53 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   ActiveStorage::Blob Load (0.7ms)  SELECT "active_storage_blobs".* FROM "active_storage_blobs" WHERE "active_storage_blobs"."id" = $1 LIMIT $2  [["id", 187], ["LIMIT", "[FILTERED]"]]
16:23:53 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   ↳ app/models/project.rb:261:in `block in thumbnail_for_file'
16:23:53 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   ActiveStorage::Blob Load (0.3ms)  SELECT "active_storage_blobs".* FROM "active_storage_blobs" WHERE "active_storage_blobs"."id" = $1 LIMIT $2  [["id", 134], ["LIMIT", "[FILTERED]"]]
16:23:53 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   ↳ app/models/project.rb:261:in `block in thumbnail_for_file'
16:23:53 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   ActiveStorage::Blob Load (0.3ms)  SELECT "active_storage_blobs".* FROM "active_storage_blobs" WHERE "active_storage_blobs"."id" = $1 LIMIT $2  [["id", 192], ["LIMIT", "[FILTERED]"]]
16:23:53 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   ↳ app/models/project.rb:261:in `block in thumbnail_for_file'
16:23:53 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   ActiveStorage::Blob Load (0.3ms)  SELECT "active_storage_blobs".* FROM "active_storage_blobs" WHERE "active_storage_blobs"."id" = $1 LIMIT $2  [["id", 138], ["LIMIT", "[FILTERED]"]]
16:23:53 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   ↳ app/models/project.rb:261:in `block in thumbnail_for_file'
16:23:53 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   ActiveStorage::Blob Load (0.3ms)  SELECT "active_storage_blobs".* FROM "active_storage_blobs" WHERE "active_storage_blobs"."id" = $1 LIMIT $2  [["id", 139], ["LIMIT", "[FILTERED]"]]
16:23:53 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   ↳ app/models/project.rb:261:in `block in thumbnail_for_file'
16:23:53 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   ActiveStorage::Blob Load (0.3ms)  SELECT "active_storage_blobs".* FROM "active_storage_blobs" WHERE "active_storage_blobs"."id" = $1 LIMIT $2  [["id", 195], ["LIMIT", "[FILTERED]"]]
16:23:53 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   ↳ app/models/project.rb:261:in `block in thumbnail_for_file'
16:23:53 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   ActiveStorage::Blob Load (0.3ms)  SELECT "active_storage_blobs".* FROM "active_storage_blobs" WHERE "active_storage_blobs"."id" = $1 LIMIT $2  [["id", 143], ["LIMIT", "[FILTERED]"]]
16:23:53 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   ↳ app/models/project.rb:261:in `block in thumbnail_for_file'
16:23:53 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   ActiveStorage::Blob Load (0.3ms)  SELECT "active_storage_blobs".* FROM "active_storage_blobs" WHERE "active_storage_blobs"."id" = $1 LIMIT $2  [["id", 144], ["LIMIT", "[FILTERED]"]]
16:23:53 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   ↳ app/models/project.rb:261:in `block in thumbnail_for_file'
16:23:53 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   S3 Storage (30.3ms) Checked if file exists at key: qw7c0p55phxm65a9nirgyd69dtln (no)
16:23:53 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   TRANSACTION (0.3ms)  BEGIN
16:23:53 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   GoodJob::Job Update (0.8ms)  UPDATE "good_jobs" SET "performed_at" = $1, "serialized_params" = $2, "scheduled_at" = $3, "created_at" = $4, "updated_at" = $5 WHERE "good_jobs"."id" = $6  [["performed_at", "[FILTERED]"], ["serialized_params", "{\"job_class\":\"PdfThumbnailGenerationJob\",\"job_id\":\"a2cab720-8804-4144-ad3d-82b97fbc859f\",\"provider_job_id\":\"a2cab720-8804-4144-ad3d-82b97fbc859f\",\"queue_name\":\"default\",\"priority\":null,\"arguments\":[{\"_aj_globalid\":\"gid://unlisters-app/Project/1\"}],\"executions\":2,\"exception_executions\":{\"[PdfThumbnailGenerationJob::FileNotReadyError]\":2},\"locale\":\"sk\",\"timezone\":\"CET\",\"enqueued_at\":\"2025-06-17T14:23:53Z\"}"], ["scheduled_at", "[FILTERED]"], ["created_at", "[FILTERED]"], ["updated_at", "[FILTERED]"], ["id", "a2cab720-8804-4144-ad3d-82b97fbc859f"]]
16:23:53 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   TRANSACTION (4.7ms)  COMMIT
16:23:53 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f]   SQL (0.3ms)  NOTIFY good_job, '{"queue_name":"default","scheduled_at":"2025-06-17T16:24:12.927+02:00"}'
16:23:53 web.1  | [GoodJob] Notifier received payload: {"queue_name":"default","scheduled_at":"2025-06-17T16:24:12.927+02:00"}
16:23:53 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f] Enqueued PdfThumbnailGenerationJob (Job ID: a2cab720-8804-4144-ad3d-82b97fbc859f) to GoodJob(default) at 2025-06-17 14:24:12 UTC with arguments: #<GlobalID:0x000075a47d661948 @uri=#<URI::GID gid://unlisters-app/Project/1>>
16:23:53 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f] Retrying PdfThumbnailGenerationJob in 18 seconds, due to a PdfThumbnailGenerationJob::FileNotReadyError.
16:23:53 web.1  | [ActiveJob] [PdfThumbnailGenerationJob] [a2cab720-8804-4144-ad3d-82b97fbc859f] Performed PdfThumbnailGenerationJob (Job ID: a2cab720-8804-4144-ad3d-82b97fbc859f) from GoodJob(default) in 98.06ms
16:23:53 web.1  | [GoodJob] [2548303] [GoodJob::Scheduler(queues=* max_threads=5)-thread-2] Executed GoodJob a2cab720-8804-4144-ad3d-82b97fbc859f
16:23:53 web.1  |   TRANSACTION (0.2ms)  BEGIN
16:23:54 web.1  |   GoodJob::Execution Update (0.9ms)  UPDATE "good_job_executions" SET "updated_at" = $1, "finished_at" = $2, "error" = $3, "error_event" = $4, "error_backtrace" = $5, "duration" = $6 WHERE "good_job_executions"."id" = $7  [["updated_at", "[FILTERED]"], ["finished_at", "[FILTERED]"], ["error", "PdfThumbnailGenerationJob::FileNotReadyError: Files not yet available on S3 for Project 1: qw7c0p55phxm65a9nirgyd69dtln. Will retry with exponential backoff."], ["error_event", "[FILTERED]"], ["error_backtrace", "[FILTERED]"], ["duration", "[FILTERED]"], ["id", "0b344874-e995-40fe-a387-af3750f34737"]]
16:23:54 web.1  |   GoodJob::Job Update (0.5ms)  UPDATE "good_jobs" SET "locked_by_id" = $1, "locked_at" = $2, "updated_at" = $3 WHERE "good_jobs"."id" = $4  [["locked_by_id", nil], ["locked_at", "[FILTERED]"], ["updated_at", "[FILTERED]"], ["id", "a2cab720-8804-4144-ad3d-82b97fbc859f"]]
16:23:54 web.1  |   TRANSACTION (1.9ms)  COMMIT
16:23:54 web.1  |   GoodJob::Lockable Advisory Unlock (0.3ms)  SELECT pg_advisory_unlock(('x'||substr(md5($1::text), 1, 16))::bit(64)::bigint) AS unlocked  [["key", "good_jobs-a2cab720-8804-4144-ad3d-82b97fbc859f"]]
16:23:54 web.1  |   GoodJob::Job Load (1.6ms)  SELECT "good_jobs"."id", "good_jobs"."queue_name", "good_jobs"."priority", "good_jobs"."serialized_params", "good_jobs"."scheduled_at", "good_jobs"."performed_at", "good_jobs"."finished_at", "good_jobs"."error", "good_jobs"."created_at", "good_jobs"."updated_at", "good_jobs"."active_job_id", "good_jobs"."concurrency_key", "good_jobs"."cron_key", "good_jobs"."cron_at", "good_jobs"."batch_id", "good_jobs"."batch_callback_id", "good_jobs"."executions_count", "good_jobs"."job_class", "good_jobs"."error_event", "good_jobs"."labels", "good_jobs"."locked_by_id", "good_jobs"."locked_at" FROM "good_jobs" WHERE "good_jobs"."id" IN (WITH "rows" AS  MATERIALIZED (SELECT "good_jobs"."id" FROM "good_jobs" WHERE "good_jobs"."finished_at" IS NULL AND "good_jobs"."scheduled_at" <= $1 ORDER BY priority ASC NULLS LAST, "good_jobs"."created_at" ASC) SELECT "rows"."id" FROM "rows" WHERE pg_try_advisory_lock(('x' || substr(md5('good_jobs' || '-' || "rows"."id"::text), 1, 16))::bit(64)::bigint) LIMIT $2) ORDER BY priority ASC NULLS LAST, "good_jobs"."created_at" ASC  [["scheduled_at", "[FILTERED]"], ["LIMIT", "[FILTERED]"]]
16:24:12 web.1  |   GoodJob::Job Load (1.7ms)  SELECT "good_jobs"."id", "good_jobs"."queue_name", "good_jobs"."priority", "good_jobs"."serialized_params", "good_jobs"."scheduled_at", "good_jobs"."performed_at", "good_jobs"."finished_at", "good_jobs"."error", "good_jobs"."created_at", "good_jobs"."updated_at", "good_jobs"."active_job_id", "good_jobs"."concurrency_key", "good_jobs"."cron_key", "good_jobs"."cron_at", "good_jobs"."batch_id", "good_jobs"."batch_callback_id", "good_jobs"."executions_count", "good_jobs"."job_class", "good_jobs"."error_event", "good_jobs"."labels", "good_jobs"."locked_by_id", "good_jobs"."locked_at" FROM "good_jobs" WHERE "good_jobs"."id" IN (WITH "rows" AS  MATERIALIZED (SELECT "good_jobs"."id" FROM "good_jobs" WHERE "good_jobs"."finished_at" IS NULL AND "good_jobs"."scheduled_at" <= $1 ORDER BY priority ASC NULLS LAST, "good_jobs"."created_at" ASC) SELECT "rows"."id" FROM "rows" WHERE pg_try_advisory_lock(('x' || substr(md5('good_jobs' || '-' || "rows"."id"::text), 1, 16))::bit(64)::bigint) LIMIT $2) ORDER BY priority ASC NULLS LAST, "good_jobs"."created_at" ASC  [["scheduled_at", "[FILTERED]"], ["LIMIT", "[FILTERED]"]]
16:24:12 web.1  |   GoodJob::Job Load (1.1ms)  SELECT "good_jobs"."id", "good_jobs"."queue_name", "good_jobs"."priority", "good_jobs"."serialized_params", "good_jobs"."scheduled_at", "good_jobs"."performed_at", "good_jobs"."finished_at", "good_jobs"."error", "good_jobs"."created_at", "good_jobs"."updated_at", "good_jobs"."active_job_id", "good_jobs"."concurrency_key", "good_jobs"."cron_key", "good_jobs"."cron_at", "good_jobs"."batch_id", "good_jobs"."batch_callback_id", "good_jobs"."executions_count", "good_jobs"."job_class", "good_jobs"."error_event", "good_jobs"."labels", "good_jobs"."locked_by_id", "good_jobs"."locked_at" FROM "good_jobs" WHERE "good_jobs"."id" IN (WITH "rows" AS  MATERIALIZED (SELECT "good_jobs"."id" FROM "good_jobs" WHERE "good_jobs"."finished_at" IS NULL AND "good_jobs"."scheduled_at" <= $1 ORDER BY priority ASC NULLS LAST, "good_jobs"."created_at" ASC) SELECT "rows"."id" FROM "rows" WHERE pg_try_advisory_lock(('x' || substr(md5('good_jobs' || '-' || "rows"."id"::text), 1, 16))::bit(64)::bigint) LIMIT $2) ORDER BY priority ASC NULLS LAST, "good_jobs"."created_at" ASC  [["scheduled_at", "[FILTERED]"], ["LIMIT", "[FILTERED]"]]
16:24:13 web.1  |   GoodJob::Job Load (0.9ms)  SELECT "good_jobs"."id", "good_jobs"."queue_name", "good_jobs"."priority", "good_jobs"."serialized_params", "good_jobs"."scheduled_at", "good_jobs"."performed_at", "good_jobs"."finished_at", "good_jobs"."error", "good_jobs"."created_at", "good_jobs"."updated_at", "good_jobs"."active_job_id", "good_jobs"."concurrency_key", "good_jobs"."cron_key", "good_jobs"."cron_at", "good_jobs"."batch_id", "good_jobs"."batch_callback_id", "good_jobs"."executions_count", "good_jobs"."job_class", "good_jobs"."error_event", "good_jobs"."labels", "good_jobs"."locked_by_id", "good_jobs"."locked_at" FROM "good_jobs" WHERE "good_jobs"."id" IN (WITH "rows" AS  MATERIALIZED (SELECT "good_jobs"."id" FROM "good_jobs" WHERE "good_jobs"."queue_name" = $1 AND "good_jobs"."finished_at" IS NULL AND "good_jobs"."scheduled_at" <= $2 ORDER BY priority ASC NULLS LAST, "good_jobs"."created_at" ASC) SELECT "rows"."id" FROM "rows" WHERE pg_try_advisory_lock(('x' || substr(md5('good_jobs' || '-' || "rows"."id"::text), 1, 16))::bit(64)::bigint) LIMIT $3) ORDER BY priority ASC NULLS LAST, "good_jobs"."created_at" ASC  [["queue_name", "default"], ["scheduled_at", "[FILTERED]"], ["LIMIT", "[FILTERED]"]]
16:24:13 web.1  |   GoodJob::Job Load (0.9ms)  SELECT "good_jobs"."id", "good_jobs"."queue_name", "good_jobs"."priority", "good_jobs"."serialized_params", "good_jobs"."scheduled_at", "good_jobs"."performed_at", "good_jobs"."finished_at", "good_jobs"."error", "good_jobs"."created_at", "good_jobs"."updated_at", "good_jobs"."active_job_id", "good_jobs"."concurrency_key", "good_jobs"."cron_key", "good_jobs"."cron_at", "good_jobs"."batch_id", "good_jobs"."batch_callback_id", "good_jobs"."executions_count", "good_jobs"."job_class", "good_jobs"."error_event", "good_jobs"."labels", "good_jobs"."locked_by_id", "good_jobs"."locked_at" FROM "good_jobs" WHERE "good_jobs"."id" IN (WITH "rows" AS  MATERIALIZED (SELECT "good_jobs"."id" FROM "good_jobs" WHERE "good_jobs"."queue_name" = $1 AND "good_jobs"."finished_at" IS NULL AND "good_jobs"."scheduled_at" <= $2 ORDER BY priority ASC NULLS LAST, "good_jobs"."created_at" ASC) SELECT "rows"."id" FROM "rows" WHERE pg_try_advisory_lock(('x' || substr(md5('good_jobs' || '-' || "rows"."id"::text), 1, 16))::bit(64)::bigint) LIMIT $3) ORDER BY priority ASC NULLS LAST, "good_jobs"."created_at" ASC  [["queue_name", "default"], ["scheduled_at", "[FILTERED]"], ["LIMIT", "[FILTERED]"]]
16:24:29 web.1  |   S3 Storage (39659.1ms) Uploaded file to key: qw7c0p55phxm65a9nirgyd69dtln (checksum: JOm9KSnmVuSEbzu7QLN4bA==)
16:24:29 web.1  | [ActiveJob]   TRANSACTION (0.3ms)  BEGIN
16:24:29 web.1  | [ActiveJob]   ↳ app/controllers/projects_controller.rb:183:in `update'
16:24:29 web.1  | [ActiveJob]   GoodJob::Job Create (0.7ms)  INSERT INTO "good_jobs" ("id", "queue_name", "priority", "serialized_params", "scheduled_at", "performed_at", "finished_at", "error", "created_at", "updated_at", "active_job_id", "concurrency_key", "cron_key", "cron_at", "batch_id", "batch_callback_id", "executions_count", "job_class", "error_event", "labels", "locked_by_id", "locked_at") VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22) RETURNING "id"  [["id", "de0d1761-1119-4d31-ab0c-dc4b63de5985"], ["queue_name", "default"], ["priority", "[FILTERED]"], ["serialized_params", "{\"job_class\":\"ActiveStorage::AnalyzeJob\",\"job_id\":\"de0d1761-1119-4d31-ab0c-dc4b63de5985\",\"provider_job_id\":null,\"queue_name\":\"default\",\"priority\":null,\"arguments\":[{\"_aj_globalid\":\"gid://unlisters-app/ActiveStorage::Blob/197\"}],\"executions\":0,\"exception_executions\":{},\"locale\":\"sk\",\"timezone\":\"CET\",\"enqueued_at\":\"2025-06-17T14:24:29Z\"}"], ["scheduled_at", "[FILTERED]"], ["performed_at", "[FILTERED]"], ["finished_at", "[FILTERED]"], ["error", nil], ["created_at", "[FILTERED]"], ["updated_at", "[FILTERED]"], ["active_job_id", "[FILTERED]"], ["concurrency_key", "[FILTERED]"], ["cron_key", "[FILTERED]"], ["cron_at", "[FILTERED]"], ["batch_id", "[FILTERED]"], ["batch_callback_id", "[FILTERED]"], ["executions_count", "[FILTERED]"], ["job_class", "ActiveStorage::AnalyzeJob"], ["error_event", "[FILTERED]"], ["labels", nil], ["locked_by_id", nil], ["locked_at", "[FILTERED]"]]
16:24:29 web.1  | [ActiveJob]   ↳ app/controllers/projects_controller.rb:183:in `update'
16:24:29 web.1  | [ActiveJob]   TRANSACTION (4.1ms)  COMMIT
16:24:29 web.1  | [ActiveJob]   ↳ app/controllers/projects_controller.rb:183:in `update'
16:24:29 web.1  | [ActiveJob] Enqueued ActiveStorage::AnalyzeJob (Job ID: de0d1761-1119-4d31-ab0c-dc4b63de5985) to GoodJob(default) with arguments: #<GlobalID:0x000075a47d418768 @uri=#<URI::GID gid://unlisters-app/ActiveStorage::Blob/197>>
16:24:29 web.1  | Redirected to http://192.168.1.51:5000/sk/projects/1/edit
16:24:29 web.1  | Geocoder cache expired using official method to prevent memory leak
16:24:29 web.1  | Geocoder configuration cache cleared to prevent memory leak
16:24:30 web.1  | Completed 302 Found in 40056ms (ActiveRecord: 30.1ms | Allocations: 224258)
16:24:30 web.1  |
16:24:30 web.1  |
16:24:30 web.1  |   GoodJob::Job Load (1.9ms)  SELECT "good_jobs"."id", "good_jobs"."queue_name", "good_jobs"."priority", "good_jobs"."serialized_params", "good_jobs"."scheduled_at", "good_jobs"."performed_at", "good_jobs"."finished_at", "good_jobs"."error", "good_jobs"."created_at", "good_jobs"."updated_at", "good_jobs"."active_job_id", "good_jobs"."concurrency_key", "good_jobs"."cron_key", "good_jobs"."cron_at", "good_jobs"."batch_id", "good_jobs"."batch_callback_id", "good_jobs"."executions_count", "good_jobs"."job_class", "good_jobs"."error_event", "good_jobs"."labels", "good_jobs"."locked_by_id", "good_jobs"."locked_at" FROM "good_jobs" WHERE "good_jobs"."id" IN (WITH "rows" AS  MATERIALIZED (SELECT "good_jobs"."id" FROM "good_jobs" WHERE "good_jobs"."queue_name" = $1 AND "good_jobs"."finished_at" IS NULL AND "good_jobs"."scheduled_at" <= $2 ORDER BY priority ASC NULLS LAST, "good_jobs"."created_at" ASC) SELECT "rows"."id" FROM "rows" WHERE pg_try_advisory_lock(('x' || substr(md5('good_jobs' || '-' || "rows"."id"::text), 1, 16))::bit(64)::bigint) LIMIT $3) ORDER BY priority ASC NULLS LAST, "good_jobs"."created_at" ASC  [["queue_name", "default"], ["scheduled_at", "[FILTERED]"], ["LIMIT", "[FILTERED]"]]
16:24:30 web.1  |   GoodJob::Job Load (0.7ms)  SELECT "good_jobs"."id", "good_jobs"."queue_name", "good_jobs"."priority", "good_jobs"."serialized_params", "good_jobs"."scheduled_at", "good_jobs"."performed_at", "good_jobs"."finished_at", "good_jobs"."error", "good_jobs"."created_at", "good_jobs"."updated_at", "good_jobs"."active_job_id", "good_jobs"."concurrency_key", "good_jobs"."cron_key", "good_jobs"."cron_at", "good_jobs"."batch_id", "good_jobs"."batch_callback_id", "good_jobs"."executions_count", "good_jobs"."job_class", "good_jobs"."error_event", "good_jobs"."labels", "good_jobs"."locked_by_id", "good_jobs"."locked_at" FROM "good_jobs" WHERE "good_jobs"."id" = $1 LIMIT $2  [["id", "de0d1761-1119-4d31-ab0c-dc4b63de5985"], ["LIMIT", "[FILTERED]"]]
16:24:30 web.1  |   TRANSACTION (0.5ms)  BEGIN
16:24:30 web.1  |   GoodJob::Execution Create (0.9ms)  INSERT INTO "good_job_executions" ("created_at", "updated_at", "active_job_id", "job_class", "queue_name", "serialized_params", "scheduled_at", "finished_at", "error", "error_event", "error_backtrace", "process_id", "duration") VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13) RETURNING "id"  [["created_at", "[FILTERED]"], ["updated_at", "[FILTERED]"], ["active_job_id", "[FILTERED]"], ["job_class", "ActiveStorage::AnalyzeJob"], ["queue_name", "default"], ["serialized_params", "{\"job_id\":\"de0d1761-1119-4d31-ab0c-dc4b63de5985\",\"locale\":\"sk\",\"priority\":null,\"timezone\":\"CET\",\"arguments\":[{\"_aj_globalid\":\"gid://unlisters-app/ActiveStorage::Blob/197\"}],\"job_class\":\"ActiveStorage::AnalyzeJob\",\"executions\":0,\"queue_name\":\"default\",\"enqueued_at\":\"2025-06-17T14:24:29Z\",\"provider_job_id\":null,\"exception_executions\":{}}"], ["scheduled_at", "[FILTERED]"], ["finished_at", "[FILTERED]"], ["error", nil], ["error_event", "[FILTERED]"], ["error_backtrace", "[FILTERED]"], ["process_id", "85865815-aa48-40d5-b94a-15ce5da73e9c"], ["duration", "[FILTERED]"]]
16:24:30 web.1  |   GoodJob::Job Update (0.9ms)  UPDATE "good_jobs" SET "performed_at" = $1, "updated_at" = $2, "executions_count" = $3, "locked_by_id" = $4, "locked_at" = $5 WHERE "good_jobs"."id" = $6  [["performed_at", "[FILTERED]"], ["updated_at", "[FILTERED]"], ["executions_count", "[FILTERED]"], ["locked_by_id", "85865815-aa48-40d5-b94a-15ce5da73e9c"], ["locked_at", "[FILTERED]"], ["id", "de0d1761-1119-4d31-ab0c-dc4b63de5985"]]
16:24:30 web.1  | Started GET "/sk/projects/1/edit" for 192.168.1.56 at 2025-06-17 16:24:30 +0200
16:24:30 web.1  |   TRANSACTION (2.4ms)  COMMIT
16:24:30 web.1  | [ActiveJob] [ActiveStorage::AnalyzeJob] [de0d1761-1119-4d31-ab0c-dc4b63de5985]   ActiveStorage::Blob Load (0.4ms)  SELECT "active_storage_blobs".* FROM "active_storage_blobs" WHERE "active_storage_blobs"."id" = $1 LIMIT $2  [["id", 197], ["LIMIT", "[FILTERED]"]]
16:24:30 web.1  | [ActiveJob] [ActiveStorage::AnalyzeJob] [de0d1761-1119-4d31-ab0c-dc4b63de5985] Performing ActiveStorage::AnalyzeJob (Job ID: de0d1761-1119-4d31-ab0c-dc4b63de5985) from GoodJob(default) enqueued at 2025-06-17T14:24:29Z with arguments: #<GlobalID:0x000075a47d4a29b8 @uri=#<URI::GID gid://unlisters-app/ActiveStorage::Blob/197>>
16:24:30 web.1  |   User Load (1.5ms)  SELECT "users".* FROM "users" WHERE "users"."id" = $1 ORDER BY "users"."id" ASC LIMIT $2  [["id", 1], ["LIMIT", "[FILTERED]"]]
16:24:30 web.1  | Processing by ProjectsController#edit as HTML
16:24:30 web.1  |   Parameters: {"locale"=>"sk", "id"=>"1"}
16:24:30 web.1  |   UserProfile Load (1.0ms)  SELECT "user_profiles".* FROM "user_profiles" WHERE "user_profiles"."user_id" = $1 LIMIT $2  [["user_id", 1], ["LIMIT", "[FILTERED]"]]
16:24:30 web.1  |   ↳ app/controllers/application_controller.rb:74:in `set_locale'
16:24:30 web.1  |   Project Load (0.7ms)  SELECT "projects".* FROM "projects" WHERE "projects"."id" = $1 LIMIT $2  [["id", 1], ["LIMIT", "[FILTERED]"]]
16:24:30 web.1  |   ↳ app/controllers/projects_controller.rb:327:in `set_project'
16:24:30 web.1  |   ActiveStorage::Attachment Load (0.6ms)  SELECT "active_storage_attachments".* FROM "active_storage_attachments" WHERE "active_storage_attachments"."record_type" = $1 AND "active_storage_attachments"."name" = $2 AND "active_storage_attachments"."record_id" = $3  [["record_type", "[FILTERED]"], ["name", "private_files"], ["record_id", 1]]
16:24:30 web.1  |   ↳ app/controllers/projects_controller.rb:327:in `set_project'
16:24:30 web.1  |   ActiveStorage::Blob Load (0.6ms)  SELECT "active_storage_blobs".* FROM "active_storage_blobs" WHERE "active_storage_blobs"."id" = $1  [["id", 197]]
16:24:30 web.1  |   ↳ app/controllers/projects_controller.rb:327:in `set_project'
16:24:30 web.1  |   Rendering layout layouts/application.html.erb
16:24:30 web.1  |   Rendering projects/edit.html.erb within layouts/application
16:24:30 web.1  |   ConnectionRequest Load (0.6ms)  SELECT "connection_requests".* FROM "connection_requests" WHERE "connection_requests"."project_id" = $1 AND "connection_requests"."status" = $2  [["project_id", "[FILTERED]"], ["status", "[FILTERED]"]]
16:24:30 web.1  |   ↳ app/views/projects/_full_details_users.html.erb:3
16:24:30 web.1  |   ProjectAuth Load (0.8ms)  SELECT "project_auths".* FROM "project_auths" WHERE "project_auths"."project_id" = $1 AND "project_auths"."access_level" = $2  [["project_id", "[FILTERED]"], ["access_level", 3]]
16:24:30 web.1  |   ↳ app/views/projects/_full_details_users.html.erb:61
16:24:30 web.1  |   Rendered projects/_full_details_users.html.erb (Duration: 8.0ms | Allocations: 1958)
16:24:30 web.1  |   Rendered projects/_form.html.erb (Duration: 30.9ms | Allocations: 13380)
16:24:30 web.1  |   Rendered projects/edit.html.erb within layouts/application (Duration: 31.5ms | Allocations: 13536)
16:24:30 web.1  |   Rendered layout layouts/application.html.erb (Duration: 42.7ms | Allocations: 19285)
16:24:30 web.1  | Geocoder cache expired using official method to prevent memory leak
16:24:30 web.1  | Geocoder configuration cache cleared to prevent memory leak
16:24:30 web.1  | Completed 200 OK in 214ms (Views: 42.2ms | ActiveRecord: 4.3ms | Allocations: 26375)
16:24:30 web.1  |
16:24:30 web.1  |
16:24:30 web.1  | Started GET "/assets/application.debug.css" for 192.168.1.56 at 2025-06-17 16:24:30 +0200
16:24:30 web.1  | Started GET "/assets/logo.svg" for 192.168.1.56 at 2025-06-17 16:24:30 +0200
16:24:30 web.1  | [ActiveJob] [ActiveStorage::AnalyzeJob] [de0d1761-1119-4d31-ab0c-dc4b63de5985]   S3 Storage (532.3ms) Downloaded file from key: qw7c0p55phxm65a9nirgyd69dtln
16:24:31 web.1  | Started GET "/connection_requests/count" for 192.168.1.56 at 2025-06-17 16:24:31 +0200
16:24:31 web.1  |   User Load (0.5ms)  SELECT "users".* FROM "users" WHERE "users"."id" = $1 ORDER BY "users"."id" ASC LIMIT $2  [["id", 1], ["LIMIT", "[FILTERED]"]]
16:24:31 web.1  | Processing by ConnectionRequestsController#count as */*
16:24:31 web.1  |   UserProfile Load (0.4ms)  SELECT "user_profiles".* FROM "user_profiles" WHERE "user_profiles"."user_id" = $1 LIMIT $2  [["user_id", 1], ["LIMIT", "[FILTERED]"]]
16:24:31 web.1  |   ↳ app/controllers/application_controller.rb:74:in `set_locale'
16:24:31 web.1  |    (0.7ms)          SELECT
16:24:31 web.1  |           COUNT(*) AS total_count,
16:24:31 web.1  |           EXISTS(SELECT 1 FROM connection_requests
16:24:31 web.1  |                 WHERE invitee_id = 1
16:24:31 web.1  |                 AND status = 0
16:24:31 web.1  |                 AND project_id IS NOT NULL
16:24:31 web.1  |                 LIMIT 1) AS has_project_requests,
16:24:31 web.1  |           EXISTS(SELECT 1 FROM connection_requests
16:24:31 web.1  |                 WHERE invitee_id = 1
16:24:31 web.1  |                 AND status = 0
16:24:31 web.1  |                 AND project_id IS NULL
16:24:31 web.1  |                 LIMIT 1) AS has_network_requests
16:24:31 web.1  |         FROM connection_requests
16:24:31 web.1  |         WHERE invitee_id = 1 AND status = 0
16:24:31 web.1  |
16:24:31 web.1  |   ↳ app/controllers/connection_requests_controller.rb:8:in `count'
16:24:31 web.1  |
16:24:31 web.1  | ---------{"total_count"=>0, "has_project_requests"=>false, "has_network_requests"=>false} ---------
16:24:31 web.1  |
16:24:31 web.1  |
16:24:31 web.1  | Completed 200 OK in 5ms (Views: 0.2ms | ActiveRecord: 1.1ms | Allocations: 1470)
16:24:31 web.1  |
16:24:31 web.1  |
16:24:33 web.1  | [ActiveJob] [ActiveStorage::AnalyzeJob] [de0d1761-1119-4d31-ab0c-dc4b63de5985]   TRANSACTION (0.4ms)  BEGIN
16:24:33 web.1  | [ActiveJob] [ActiveStorage::AnalyzeJob] [de0d1761-1119-4d31-ab0c-dc4b63de5985]   ActiveStorage::Blob Update (0.9ms)  UPDATE "active_storage_blobs" SET "metadata" = $1 WHERE "active_storage_blobs"."id" = $2  [["metadata", "[FILTERED]"], ["id", 197]]
16:24:33 web.1  | [ActiveJob] [ActiveStorage::AnalyzeJob] [de0d1761-1119-4d31-ab0c-dc4b63de5985]   TRANSACTION (5.6ms)  COMMIT
16:24:33 web.1  | [ActiveJob] [ActiveStorage::AnalyzeJob] [de0d1761-1119-4d31-ab0c-dc4b63de5985] Performed ActiveStorage::AnalyzeJob (Job ID: de0d1761-1119-4d31-ab0c-dc4b63de5985) from GoodJob(default) in 3832.41ms
16:24:33 web.1  | [GoodJob] [2548303] [GoodJob::Scheduler(queues=default max_threads=3)-thread-1] Executed GoodJob de0d1761-1119-4d31-ab0c-dc4b63de5985
16:24:33 web.1  |   TRANSACTION (0.2ms)  BEGIN
16:24:33 web.1  |   GoodJob::Execution Update (0.7ms)  UPDATE "good_job_executions" SET "updated_at" = $1, "finished_at" = $2, "duration" = $3 WHERE "good_job_executions"."id" = $4  [["updated_at", "[FILTERED]"], ["finished_at", "[FILTERED]"], ["duration", "[FILTERED]"], ["id", "cf09f767-63a8-426f-a0b4-359bf498f9c5"]]
16:24:33 web.1  |   GoodJob::Job Update (0.7ms)  UPDATE "good_jobs" SET "locked_by_id" = $1, "locked_at" = $2, "finished_at" = $3, "updated_at" = $4 WHERE "good_jobs"."id" = $5  [["locked_by_id", nil], ["locked_at", "[FILTERED]"], ["finished_at", "[FILTERED]"], ["updated_at", "[FILTERED]"], ["id", "de0d1761-1119-4d31-ab0c-dc4b63de5985"]]
16:24:33 web.1  |   TRANSACTION (3.0ms)  COMMIT
16:24:33 web.1  |   GoodJob::Lockable Advisory Unlock (0.5ms)  SELECT pg_advisory_unlock(('x'||substr(md5($1::text), 1, 16))::bit(64)::bigint) AS unlocked  [["key", "good_jobs-de0d1761-1119-4d31-ab0c-dc4b63de5985"]]
16:24:33 web.1  |   GoodJob::Job Load (1.2ms)  SELECT "good_jobs"."id", "good_jobs"."queue_name", "good_jobs"."priority", "good_jobs"."serialized_params", "good_jobs"."scheduled_at", "good_jobs"."performed_at", "good_jobs"."finished_at", "good_jobs"."error", "good_jobs"."created_at", "good_jobs"."updated_at", "good_jobs"."active_job_id", "good_jobs"."concurrency_key", "good_jobs"."cron_key", "good_jobs"."cron_at", "good_jobs"."batch_id", "good_jobs"."batch_callback_id", "good_jobs"."executions_count", "good_jobs"."job_class", "good_jobs"."error_event", "good_jobs"."labels", "good_jobs"."locked_by_id", "good_jobs"."locked_at" FROM "good_jobs" WHERE "good_jobs"."id" IN (WITH "rows" AS  MATERIALIZED (SELECT "good_jobs"."id" FROM "good_jobs" WHERE "good_jobs"."queue_name" = $1 AND "good_jobs"."finished_at" IS NULL AND "good_jobs"."scheduled_at" <= $2 ORDER BY priority ASC NULLS LAST, "good_jobs"."created_at" ASC) SELECT "rows"."id" FROM "rows" WHERE pg_try_advisory_lock(('x' || substr(md5('good_jobs' || '-' || "rows"."id"::text), 1, 16))::bit(64)::bigint) LIMIT $3) ORDER BY priority ASC NULLS LAST, "good_jobs"."created_at" ASC  [["queue_name", "default"], ["scheduled_at", "[FILTERED]"], ["LIMIT", "[FILTERED]"]]