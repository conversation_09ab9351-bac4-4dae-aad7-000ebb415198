#!/usr/bin/env ruby

# Simple test script to verify Project model translations
# Run this from Rails console or as a standalone script

puts "Testing Project Model i18n Translations"
puts "=" * 50

# Test English translations
I18n.locale = :en
puts "\n🇺🇸 ENGLISH TRANSLATIONS:"
puts "Model name (singular): #{I18n.t('models.project.one')}"
puts "Model name (plural): #{I18n.t('models.project.other')}"

puts "\nProject Types:"
Project.project_types.keys.each do |type|
  puts "  #{type}: #{I18n.t("models.project.project_types.#{type}")}"
end

puts "\nStatus Labels:"
['draft', 'pending', 'published'].each do |status|
  puts "  #{status}: #{I18n.t("models.project.statuses.#{status}")}"
end

puts "\nValidation Errors:"
puts "  Invalid currency: #{I18n.t('activerecord.errors.models.project.attributes.price_currency.invalid_currency', value: 'USD')}"
puts "  Sharing required: #{I18n.t('activerecord.errors.models.project.attributes.base.sharing_options_required')}"

# Test Slovak translations
I18n.locale = :sk
puts "\n🇸🇰 SLOVAK TRANSLATIONS:"
puts "Model name (singular): #{I18n.t('models.project.one')}"
puts "Model name (plural): #{I18n.t('models.project.other')}"

puts "\nProject Types:"
Project.project_types.keys.first(3).each do |type|
  puts "  #{type}: #{I18n.t("models.project.project_types.#{type}")}"
end

puts "\nStatus Labels:"
['draft', 'pending', 'published'].each do |status|
  puts "  #{status}: #{I18n.t("models.project.statuses.#{status}")}"
end

puts "\nValidation Errors:"
puts "  Invalid currency: #{I18n.t('activerecord.errors.models.project.attributes.price_currency.invalid_currency', value: 'USD')}"
puts "  Sharing required: #{I18n.t('activerecord.errors.models.project.attributes.base.sharing_options_required')}"

# Reset to default locale
I18n.locale = I18n.default_locale

puts "\n✅ Translation test completed!"
puts "All hardcoded English strings have been replaced with i18n translations."
