# ABOUTME: Migration to create referral_codes table for subscription tier upgrades
# ABOUTME: Includes proper constraints, defaults, and indexes for referral code management
class CreateReferralCodes < ActiveRecord::Migration[7.0]
  def change
    create_table :referral_codes do |t|
      t.string :code, null: false
      t.references :created_by, null: false, foreign_key: { to_table: :users }
      t.integer :status, default: 0, null: false
      t.integer :tier_upgrade_to, default: 1
      t.integer :duration_months, default: 1
      t.integer :max_uses, default: 1
      t.integer :current_uses, default: 0
      t.datetime :expires_at
      t.text :description

      t.timestamps
    end
    
    add_index :referral_codes, :code, unique: true
    add_index :referral_codes, [:status, :expires_at]
    
    # Database constraint for data integrity
    execute <<-SQL
      ALTER TABLE referral_codes ADD CONSTRAINT current_uses_lte_max_uses CHECK (current_uses <= max_uses);
    SQL
  end
end
