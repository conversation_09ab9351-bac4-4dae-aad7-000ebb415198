class CreateWants < ActiveRecord::Migration[7.0]
  def change
    create_table :wants do |t|
      t.references :user, null: false, foreign_key: true
      t.text :summary
      t.integer :price_min
      t.integer :price_max
      t.integer :want_type
      t.integer :category
      t.integer :subcategory
      t.string :place
      t.integer :radius
      t.string :country
      t.string :country_code
      t.string :price_currency
      t.boolean :notification

      t.timestamps
    end
  end
end
