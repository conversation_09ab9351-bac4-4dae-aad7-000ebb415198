class AllowNullForProjectEnums < ActiveRecord::Migration[7.0]
  def change
    # Allow NULL values for enum columns to enable proper dirty tracking
    # These columns previously had NOT NULL DEFAULT 0 constraints
    
    # Remove NOT NULL constraints (allow NULL values)
    change_column_null :projects, :project_type, true
    change_column_null :projects, :category, true  
    change_column_null :projects, :subcategory, true
    
    # Remove DEFAULT values (change from 0 to nil for reversibility)
    change_column_default :projects, :project_type, from: 0, to: nil
    change_column_default :projects, :category, from: 0, to: nil
    change_column_default :projects, :subcategory, from: 0, to: nil
  end
end
