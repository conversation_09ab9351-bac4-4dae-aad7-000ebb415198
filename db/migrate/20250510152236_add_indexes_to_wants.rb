class AddIndexesToWants < ActiveRecord::Migration[7.0]
  def change
    add_index :wants, :price_min
    add_index :wants, :price_max
    add_index :wants, :want_type
    add_index :wants, :category
    add_index :wants, :subcategory
    add_index :wants, :place
    add_index :wants, :notification

    add_index :wants, [:price_min, :price_max]
    add_index :wants, [:category, :place]
    add_index :wants, [:subcategory, :place]
  end
end
