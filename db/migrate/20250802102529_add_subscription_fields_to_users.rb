# ABOUTME: Migration to add subscription tier system fields to users table
# ABOUTME: Adds subscription_tier enum, expiration tracking, and referral codes with proper indexing
class AddSubscriptionFieldsToUsers < ActiveRecord::Migration[7.0]
  def change
    add_column :users, :subscription_tier, :integer, default: 0, null: false
    add_column :users, :subscription_expires_at, :datetime
    add_column :users, :referral_code, :string
    
    add_index :users, :subscription_tier
    add_index :users, :referral_code, unique: true
  end
end
