class CreateUploads < ActiveRecord::Migration[7.0]
  def change
    create_table :uploads do |t|
      t.references :user, null: false, foreign_key: true
      t.string :status, null: false, default: 'pending'
      t.string :original_filename, null: false
      t.string :content_type, null: false
      t.bigint :file_size, null: false
      t.string :temp_file_path
      t.string :s3_key
      t.text :error_message
      t.string :target_model
      t.integer :target_id
      t.string :signed_id_token, null: false
      t.integer :progress_percentage, default: 0

      t.timestamps
    end

    # Add indexes for efficient queries
    add_index :uploads, :status
    add_index :uploads, :signed_id_token, unique: true
    add_index :uploads, [:target_model, :target_id]
    add_index :uploads, [:user_id, :status]
    add_index :uploads, :created_at
  end
end
