class UpdateUploadStatusEnum < ActiveRecord::Migration[7.0]
  def up
    # First, update any existing 'ready' status to 'transferred' for backwards compatibility
    execute <<-SQL
      UPDATE uploads 
      SET status = 'transferred' 
      WHERE status = 'ready';
    SQL
    
    # Now update any 'uploading' status to 'processing' for consistency
    execute <<-SQL
      UPDATE uploads 
      SET status = 'processing' 
      WHERE status = 'uploading';
    SQL
    
    # Log the migration for visibility
    say "Updated Upload status enum: ready -> transferred, uploading -> processing"
    say "New states: pending, transferred, processing, completed, failed, cancelled"
  end
  
  def down
    # Revert 'transferred' back to 'ready'
    execute <<-SQL
      UPDATE uploads 
      SET status = 'ready' 
      WHERE status = 'transferred';
    SQL
    
    # Revert 'processing' back to 'uploading'
    execute <<-SQL
      UPDATE uploads 
      SET status = 'uploading' 
      WHERE status = 'processing';
    SQL
  end
end
