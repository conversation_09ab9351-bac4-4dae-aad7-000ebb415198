class CreateConnectionRequests < ActiveRecord::Migration[7.0]
  def change
    create_table :connection_requests do |t|
      t.bigint :inviter_id, null: false
      t.bigint :invitee_id, null: false
      t.integer :status, default: 0, null: false

      t.timestamps
    end

    add_index :connection_requests, :inviter_id
    add_index :connection_requests, :invitee_id
    add_foreign_key :connection_requests, :users, column: :inviter_id
    add_foreign_key :connection_requests, :users, column: :invitee_id
  end
end
