class CreateProjects < ActiveRecord::Migration[7.0]
  def change
    create_table :projects do |t|
      t.references :user, null: false, foreign_key: true
      t.string :title
      t.string :location
      t.text :short_description
      t.text :summary
      t.boolean :public_visible
      t.boolean :summary_only
      t.boolean :full_access
      t.integer :visibility

      t.timestamps
    end
  end
end
