class AddProjectTypeToProjects < ActiveRecord::Migration[7.0]
  def change
    add_column :projects, :project_type, :integer, default: 0, null: false

    # Adding indexes for the new projects' index query
    add_index :network_connections, [:invitee_id, :inviter_id]
    add_index :network_connections, [:inviter_id, :invitee_id]

    # Removing indexes not being used after index query update
    remove_index :network_connections, name: 'index_network_connections_on_is_friend'
    remove_index :projects, name: 'index_projects_on_friends_only'
  end
end
