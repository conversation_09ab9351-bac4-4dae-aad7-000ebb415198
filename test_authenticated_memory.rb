#!/usr/bin/env ruby
# Test memory usage with proper authentication
# Usage: ruby test_authenticated_memory.rb

require 'net/http'
require 'uri'
require 'json'

class AuthenticatedMemoryTester
  BASE_URL = 'http://localhost:3000'
  
  def initialize
    puts "🔍 Authenticated Memory Leak Test"
    puts "=" * 50
  end
  
  def run_test
    # Get baseline
    baseline = get_memory_stats
    return unless baseline
    puts "📊 Baseline: #{baseline['process_memory_mb'].round(2)} MB"
    
    # Test with authenticated requests
    puts "\n⚡ Testing authenticated project requests..."
    test_authenticated_requests(20)
    
    # Final measurement
    final = get_memory_stats
    return unless final
    
    memory_growth = final['process_memory_mb'] - baseline['process_memory_mb']
    object_growth = final['object_counts']['TOTAL'] - baseline['object_counts']['TOTAL']
    
    puts "\n" + "=" * 50
    puts "🎯 AUTHENTICATED TEST RESULTS"
    puts "=" * 50
    puts "Memory Growth: #{memory_growth.round(2)} MB"
    puts "Object Growth: #{object_growth} objects"
    puts "Baseline Memory: #{baseline['process_memory_mb'].round(2)} MB"
    puts "Final Memory: #{final['process_memory_mb'].round(2)} MB"
    
    if memory_growth > 10
      puts "🚨 SIGNIFICANT MEMORY GROWTH (>10MB)"
    elsif memory_growth > 5
      puts "⚠️  MODERATE MEMORY GROWTH (>5MB)"
    else
      puts "✅ Memory usage appears normal"
    end
    
    # Compare suspect objects
    puts "\nSuspect Object Changes:"
    if final['suspect_objects'] && baseline['suspect_objects']
      final['suspect_objects'].each do |type, count|
        baseline_count = baseline['suspect_objects'][type] || 0
        growth = count - baseline_count
        puts "  #{type}: #{count} (+#{growth})" if growth > 0
      end
    end
  end
  
  private
  
  def test_authenticated_requests(count)
    # Instead of hitting the protected route directly,
    # use the memory debug endpoint which simulates the problematic queries
    
    count.times do |i|
      response = post_request('/memory_debug/test_projects_leak')
      if response
        print "✓"
      else
        print "✗"
      end
      
      print "\n" if (i + 1) % 10 == 0
      sleep(0.1)
    end
    
    puts "\nTest completed!"
  end
  
  def get_memory_stats
    response = get_request('/memory_debug/stats')
    if response
      puts "Current memory: #{response['process_memory_mb'].round(2)} MB, Objects: #{response['object_counts']['TOTAL']}"
    end
    response
  end
  
  def get_request(path)
    uri = URI("#{BASE_URL}#{path}")
    
    begin
      response = Net::HTTP.get_response(uri)
      if response.code == '200'
        JSON.parse(response.body)
      else
        puts "❌ Error: #{response.code} - #{response.message}"
        nil
      end
    rescue => e
      puts "❌ Request failed: #{e.message}"
      nil
    end
  end
  
  def post_request(path)
    uri = URI("#{BASE_URL}#{path}")
    
    begin
      response = Net::HTTP.post(uri, '')
      if response.code == '200'
        JSON.parse(response.body)
      else
        puts "❌ Error: #{response.code} - #{response.message}"
        nil
      end
    rescue => e
      puts "❌ Request failed: #{e.message}"
      nil
    end
  end
end

# Run the test
if __FILE__ == $0
  tester = AuthenticatedMemoryTester.new
  tester.run_test
end