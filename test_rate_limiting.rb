#!/usr/bin/env ruby

# Comprehensive test script to verify rate limiting is working
# This script tests the rate limiting functionality for secure file token requests and streaming

require 'net/http'
require 'json'
require 'uri'

class RateLimitTester
  def initialize(base_url = 'http://localhost:3000')
    @base_url = base_url
    @session_cookie = nil
  end

  def test_rate_limiting
    puts "🧪 Comprehensive Rate Limiting Test Suite"
    puts "=" * 60
    
    # Test 1: Check if server is running
    unless server_running?
      puts "❌ Server not running on #{@base_url}"
      puts "Please start the server with: bin/rails server"
      return false
    end
    
    puts "✅ Server is running"
    
    # Test 2: Test token request rate limiting (IP-based)
    puts "\n📊 Testing Token Request Rate Limits (IP-based)..."
    token_test_passed = test_token_request_rate_limiting
    
    # Test 3: Test streaming endpoint rate limiting
    puts "\n📊 Testing Streaming Endpoint Rate Limits..."
    stream_test_passed = test_streaming_rate_limiting
    
    # Test 4: Test precise path matching
    puts "\n📊 Testing Precise Path Matching..."
    path_test_passed = test_path_matching_precision
    
    # Summary
    puts "\n" + "=" * 60
    puts "📋 Test Summary:"
    puts "   Token Request Rate Limiting: #{token_test_passed ? '✅ PASS' : '❌ FAIL'}"
    puts "   Streaming Rate Limiting: #{stream_test_passed ? '✅ PASS' : '❌ FAIL'}"
    puts "   Path Matching Precision: #{path_test_passed ? '✅ PASS' : '❌ FAIL'}"
    
    overall_success = token_test_passed && stream_test_passed && path_test_passed
    
    if overall_success
      puts "\n🎉 All rate limiting tests passed!"
    else
      puts "\n⚠️  Some tests failed. Review the results above."
    end
    
    return overall_success
  end

  private

  def server_running?
    uri = URI("#{@base_url}/")
    begin
      response = Net::HTTP.get_response(uri)
      response.code.to_i < 500
    rescue StandardError
      false
    end
  end

  def test_token_request_rate_limiting
    success_count = 0
    rate_limited_count = 0
    auth_required_count = 0
    
    # Make 110 requests (should exceed the 100/minute IP limit)
    puts "Making 110 rapid requests to test IP-based rate limiting..."
    (1..110).each do |i|
      response = make_token_request(1)
      
      case response
      when :success
        success_count += 1
        print "✅"
      when :rate_limited
        rate_limited_count += 1
        print "🚫"
      when :auth_required
        auth_required_count += 1
        print "🔐"
      when :error
        print "❌"
      end
      
      # Flush output immediately
      STDOUT.flush
      
      # Small delay to avoid overwhelming the server
      sleep(0.05) if i % 20 == 0
    end
    
    puts "\n📈 Token Request Results:"
    puts "   Success responses: #{success_count}"
    puts "   Rate limited responses: #{rate_limited_count}"
    puts "   Auth required responses: #{auth_required_count}"
    
    # Should have some rate-limited responses
    if rate_limited_count > 0
      puts "✅ Token request rate limiting is working!"
      return true
    else
      puts "⚠️  Token request rate limiting may not be working."
      puts "This could be normal if authentication blocks the requests first."
      return false
    end
  end

  def test_streaming_rate_limiting
    rate_limited_count = 0
    other_count = 0
    
    # Make 70 requests to streaming endpoint (should exceed 60/minute limit)
    puts "Making 70 rapid requests to streaming endpoint..."
    (1..70).each do |i|
      response = make_streaming_request
      
      case response
      when :rate_limited
        rate_limited_count += 1
        print "🚫"
      else
        other_count += 1
        print "🔐"  # Most will be forbidden due to invalid tokens
      end
      
      STDOUT.flush
      sleep(0.05) if i % 20 == 0
    end
    
    puts "\n📈 Streaming Results:"
    puts "   Rate limited responses: #{rate_limited_count}"
    puts "   Other responses (forbidden/error): #{other_count}"
    
    if rate_limited_count > 0
      puts "✅ Streaming rate limiting is working!"
      return true
    else
      puts "⚠️  Streaming rate limiting may not be working."
      return false
    end
  end

  def test_path_matching_precision
    # Test that similar but different paths are not rate limited
    puts "Testing that similar paths don't trigger rate limiting..."
    
    # Test a path that contains the token path but shouldn't match
    similar_path_response = make_request_to_path("/api/request_file_token_info")
    
    case similar_path_response
    when :not_found, :error
      puts "✅ Similar paths are not affected by rate limiting"
      return true
    when :rate_limited
      puts "❌ Rate limiting is too broad - affecting unintended paths"
      return false
    else
      puts "✅ Similar paths behave normally (not rate limited)"
      return true
    end
  end

  def make_token_request(project_id = 1)
    uri = URI("#{@base_url}/projects/#{project_id}/request_file_token")
    
    http = Net::HTTP.new(uri.host, uri.port)
    request = Net::HTTP::Post.new(uri)
    request['Content-Type'] = 'application/json'
    request['Accept'] = 'application/json'
    request.body = { file_hash: 'test_hash_12345' }.to_json
    
    begin
      response = http.request(request)
      
      case response.code.to_i
      when 429
        :rate_limited
      when 200
        :success
      when 401, 403
        :auth_required
      else
        :error
      end
    rescue StandardError
      :error
    end
  end

  def make_streaming_request
    uri = URI("#{@base_url}/secure/stream?t=invalid_token_test")
    
    http = Net::HTTP.new(uri.host, uri.port)
    request = Net::HTTP::Get.new(uri)
    
    begin
      response = http.request(request)
      
      case response.code.to_i
      when 429
        :rate_limited
      when 403
        :forbidden
      else
        :other
      end
    rescue StandardError
      :error
    end
  end

  def make_request_to_path(path)
    uri = URI("#{@base_url}#{path}")
    
    http = Net::HTTP.new(uri.host, uri.port)
    request = Net::HTTP::Get.new(uri)
    
    begin
      response = http.request(request)
      
      case response.code.to_i
      when 429
        :rate_limited
      when 404
        :not_found
      else
        :other
      end
    rescue StandardError
      :error
    end
  end
end

# Run the test
if __FILE__ == $0
  tester = RateLimitTester.new
  success = tester.test_rate_limiting
  
  puts "\n" + "=" * 60
  if success
    puts "🎉 All rate limiting tests completed successfully!"
    puts "Your secure file system has robust DoS protection."
  else
    puts "ℹ️  Rate limiting tests completed with some issues."
    puts "Review the results above and check your configuration."
  end
  
  exit(success ? 0 : 1)
end