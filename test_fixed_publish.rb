#!/usr/bin/env ruby

puts "=== TESTING FIXED PUBLISH WORKFLOW ==="

p = Project.find(110)
puts "1. Project 110 current state:"
puts "   summary: '#{p.summary}'"
puts "   location: '#{p.location}'"
puts "   project_status: #{p.project_status}"

# Test the publish logic with validation errors
puts "\n2. Simulating fixed controller logic..."
p.project_status = true

# Check validation state (this is what logging will show)
puts "   Before save - valid?: #{p.valid?}"
if !p.valid?
  puts "   Validation errors: #{p.errors.full_messages}"
end

# Attempt save
save_result = p.save
puts "   save result: #{save_result}"

if save_result
  puts "   SUCCESS: Project published!"
else
  puts "   FAILED: Validation errors prevent publishing"
  error_details = p.errors.full_messages.join(', ')
  puts "   Error message user will see: 'Cannot publish: #{error_details}'"
end

puts "\n3. Final project state:"
puts "   project_status: #{p.project_status}"
puts "   status: #{p.status}"

# Clean up - reset to original state
p.reload
puts "\n4. Project reset to original state"