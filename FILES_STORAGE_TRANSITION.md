# Files Storage Transition Plan

## Overview

Transition from single S3 bucket to separate buckets for uploads and thumbnails to align with Lambda thumbnail generation expectations and improve organization.

## Current State Analysis

- **Rails Version**: 7.0.8 (No native folder/prefix support in Active Storage)
- **Current Setup**: Single bucket per environment with ~30 existing files
- **Current Legacy Buckets**: 
  - Dev: `floating-sierra-56086fd6-d570-470e-82e6-80e348975de7-dev`
  - Prod: `floating-sierra-56086fd6-d570-470e-82e6-80e348975de7-prod`
- **New Bucket Names**:
  - Dev uploads: `app-sierra-a7b3c9e2-f8d1-4e56-9c2a-dev-uploads`
  - Dev thumbnails: `app-sierra-d4e8f2a6-c3b7-4d91-8e5a-dev-thumbnails`
  - Prod uploads: `app-sierra-b1c5e9a3-d7f2-4a68-9b4e-prod-uploads`
  - Prod thumbnails: `app-sierra-e8a4c2f6-b9d3-4e71-a5c8-prod-thumbnails`
- **File Location**: ✅ **CONFIRMED**: All files stored in bucket root (no prefixes used)
- **Invalid Config Found**: `upload: prefix: uploads/` in prod config (ignored by Rails)
- **Lambda Integration**: Expects uploads/ and thumbnails/ folder structure
- **Simplified Challenge**: Need separate buckets, not prefix migration

## Research Findings

### Rails Active Storage Folder Support
- **Rails 7.0.8**: ❌ No native prefix/folder support
- **Rails 7.1/7.2/8.0**: ❌ Still no native support
- **Workarounds**: Complex, brittle, maintenance-heavy custom code
- **Recommendation**: Use separate buckets (Rails-native approach)

### Alternative Solutions Evaluated
1. **Custom S3 Service**: Complex monkey-patching, breaks upgrades
2. **Manual Blob Creation**: Requires extensive custom code
3. **Separate Buckets**: ✅ Clean, Rails-native, Lambda-compatible

## Target Architecture

### Bucket Structure
```
Development:
├── app-sierra-a7b3c9e2-f8d1-4e56-9c2a-dev-uploads      (new uploads)
└── app-sierra-d4e8f2a6-c3b7-4d91-8e5a-dev-thumbnails   (generated thumbnails)

Production:
├── app-sierra-b1c5e9a3-d7f2-4a68-9b4e-prod-uploads     (new uploads)
└── app-sierra-e8a4c2f6-b9d3-4e71-a5c8-prod-thumbnails  (generated thumbnails)
```

### Service Configuration Strategy
Use Active Storage's `service_name` column for seamless transition:
- **Legacy files**: Point to `amazon_dev`/`amazon_prod` services (existing buckets)
- **New files**: Point to `amazon_dev_uploads`/`amazon_prod_uploads` services
- **Thumbnails**: Stored in `amazon_dev_thumbnails`/`amazon_prod_thumbnails` services

### Simplified Migration (No Prefix Issues)
Since all files are in bucket root with no prefixes:
- ✅ No complex S3 object copying needed
- ✅ Simple bucket-to-bucket file migration 
- ✅ Invalid `upload: prefix:` config can be safely removed

### Refactored Architecture Benefits
Applied official Rails best practices for multi-bucket Active Storage:
- ✅ **Single Source of Truth**: Service defined where attachment is declared
- ✅ **Scalable**: Adding new attachment types = one line change
- ✅ **Self-Documenting**: Code clearly shows where files are stored
- ✅ **Future-Ready**: Easy to add profile pictures, documents, etc.
- ✅ **Simplified Jobs**: Background jobs don't need service logic

## Implementation Plan

### Phase 1: Preparation (Zero Downtime) ✅ COMPLETED

#### 1.1 ✅ Buckets Available
**Status**: Buckets already created and available

- ✅ Development buckets:
  - `app-sierra-a7b3c9e2-f8d1-4e56-9c2a-dev-uploads`
  - `app-sierra-d4e8f2a6-c3b7-4d91-8e5a-dev-thumbnails`
- ✅ Production buckets:
  - `app-sierra-b1c5e9a3-d7f2-4a68-9b4e-prod-uploads` 
  - `app-sierra-e8a4c2f6-b9d3-4e71-a5c8-prod-thumbnails`

#### 1.2 Configure Bucket Policies & CORS
**Dependency**: Reference Lambda permissions guide

- [ ] Copy CORS configuration from existing bucket to new uploads buckets
- [ ] Set up IAM policies:
  - Rails app: Full access to uploads bucket
  - Lambda: Read from uploads, write to thumbnails
- [ ] Configure lifecycle policies (if needed)

#### 1.3 ✅ Update Storage Configuration
**File**: `config/storage.yml`

✅ **COMPLETED**: New service configurations added to `storage.yml` with correct bucket names:

```yaml
# Legacy services (existing buckets with current files)
amazon_dev:    # floating-sierra-56086fd6-d570-470e-82e6-80e348975de7-dev
amazon_prod:   # floating-sierra-56086fd6-d570-470e-82e6-80e348975de7-prod

# New upload services
amazon_dev_uploads:    # app-sierra-a7b3c9e2-f8d1-4e56-9c2a-dev-uploads
amazon_prod_uploads:   # app-sierra-b1c5e9a3-d7f2-4a68-9b4e-prod-uploads

# New thumbnail services
amazon_dev_thumbnails:   # app-sierra-d4e8f2a6-c3b7-4d91-8e5a-dev-thumbnails
amazon_prod_thumbnails:  # app-sierra-e8a4c2f6-b9d3-4e71-a5c8-prod-thumbnails
```

### Phase 2: Code Deployment (Zero Downtime) ✅ COMPLETED & IMPROVED

#### 2.1 ✅ **CRITICAL FIX**: Logical Service Names Implementation
**Files**: `app/models/project.rb`, `config/storage.yml`

**❌ Issue Discovered**: Conditional service specification caused `ActiveJob::DeserializationError` during background job execution due to Rails initialization race conditions.

**✅ SOLUTION IMPLEMENTED**: Logical service names approach (Rails-native best practice):

**Model** (`app/models/project.rb`):
```ruby
class Project < ApplicationRecord
  # Static logical service names - no conditional logic
  has_many_attached :private_files, service: :amazon_uploads
  has_many_attached :pdf_thumbnails, service: :amazon_thumbnails
end
```

**Configuration** (`config/storage.yml`):
```yaml
# Logical services - environment resolution via ERB in config layer
amazon_uploads:
  service: S3
  bucket: <%= Rails.env.development? ? 'app-sierra-...-dev-uploads' : 'app-sierra-...-prod-uploads' %>
  # ... other config

amazon_thumbnails:
  service: S3
  bucket: <%= Rails.env.development? ? 'app-sierra-...-dev-thumbnails' : 'app-sierra-...-prod-thumbnails' %>
  # ... other config
```

**Benefits**:
- ✅ **Fixes job deserialization**: No more `Cannot configure service` errors
- ✅ **Rails-native**: Follows framework conventions for service configuration
- ✅ **Clean architecture**: Separates business logic from infrastructure config
- ✅ **Verified working**: New uploads → uploads bucket, thumbnails → thumbnails bucket

#### 2.2 ✅ Environment Configuration Cleanup
**Files**: `config/environments/development.rb`, `config/environments/production.rb`

✅ **COMPLETED**: Removed global service configuration (Rails best practice):
- **Old approach**: Global `config.active_storage.service` 
- **New approach**: Service specified per attachment in models

#### 2.3 ✅ Simplified Thumbnail Job
**File**: `app/jobs/pdf_thumbnail_generation_job.rb`

✅ **COMPLETED**: Simplified job logic - service automatically determined by model:
```ruby
# Service automatically determined by model attachment definition
project.pdf_thumbnails.attach(
  io: StringIO.new(thumbnail_blob),
  filename: thumbnail_filename,
  content_type: 'image/png'
)
```

#### 2.2 Create Migration Rake Task
**File**: `lib/tasks/s3_migration.rake`

```ruby
namespace :s3 do
  desc "Migrate existing blobs from legacy bucket to new uploads bucket"
  task migrate_blobs: :environment do
    require 'aws-sdk-s3'
    
    legacy_service = ActiveStorage::Blob.service_configurations['amazon_legacy']
    uploads_service = ActiveStorage::Blob.service_configurations['amazon_uploads']
    
    s3_client = Aws::S3::Client.new(
      access_key_id: legacy_service['access_key_id'],
      secret_access_key: legacy_service['secret_access_key'],
      region: legacy_service['region']
    )
    
    legacy_bucket = legacy_service['bucket']
    uploads_bucket = uploads_service['bucket']
    
    # Find all blobs still using legacy service
    legacy_blobs = ActiveStorage::Blob.where(service_name: ['amazon_dev', 'amazon_prod', nil])
    
    puts "Migrating #{legacy_blobs.count} blobs..."
    
    legacy_blobs.find_each do |blob|
      begin
        # Copy object from legacy bucket to uploads bucket
        copy_source = { bucket: legacy_bucket, key: blob.key }
        
        s3_client.copy_object(
          copy_source: copy_source,
          bucket: uploads_bucket,
          key: blob.key
        )
        
        # Update blob to point to new service
        blob.update!(service_name: 'amazon_uploads')
        
        puts "✅ Migrated blob #{blob.id}: #{blob.filename}"
        
      rescue => e
        puts "❌ Failed to migrate blob #{blob.id}: #{e.message}"
      end
    end
    
    puts "Migration completed!"
  end
end
```

#### 2.3 Deploy Code Changes
- [ ] Deploy updated storage configuration
- [ ] Deploy environment configuration changes  
- [ ] Deploy migration rake task

**Result**: New uploads go to new bucket, existing files still work from old bucket

### Phase 3: Lambda Integration Update (Brief Service Interruption)

#### 3.1 Update Lambda Configuration
**Dependency**: Lambda integration guide

- [ ] Update Lambda S3 trigger:
  - Remove trigger from old bucket
  - Add trigger to new uploads bucket for `s3:ObjectCreated:*` events
- [ ] Update Lambda environment variables:
  - Source bucket: new uploads bucket
  - Destination bucket: new thumbnails bucket
- [ ] Deploy Lambda changes

**Result**: New upload → Lambda thumbnail generation workflow active

### Phase 4: Data Migration (Maintenance Window)

#### 4.1 Execute Migration
**Timeline**: 5-10 minutes for 30 files

```bash
# In production console
rails s3:migrate_blobs
```

#### 4.2 Verification Steps
- [ ] Check sample files load correctly
- [ ] Verify URLs point to new uploads bucket
- [ ] Confirm `active_storage_blobs.service_name` updated
- [ ] Test new file upload end-to-end
- [ ] Test thumbnail generation with Lambda

**Result**: All files migrated and served from new buckets

### Phase 5: Cleanup (1-2 Weeks Later)

#### 5.1 Remove Legacy Configuration
- [ ] Remove `amazon_legacy` service from `storage.yml`
- [ ] Remove migration rake task
- [ ] Update documentation

#### 5.2 Decommission Old Resources
- [ ] Delete old S3 bucket (after backup if needed)
- [ ] Clean up unused IAM policies

## Thumbnail Job Updates

### Current Behavior
- `PdfThumbnailGenerationJob` saves thumbnails to same bucket as originals
- Uses `project.pdf_thumbnails.attach()` method

### Required Changes
- [ ] Modify job to attach thumbnails using `amazon_thumbnails` service
- [ ] Update attachment method:

```ruby
# Before
project.pdf_thumbnails.attach(
  io: StringIO.new(thumbnail_blob),
  filename: thumbnail_filename,
  content_type: 'image/png'
)

# After  
project.pdf_thumbnails.attach(
  io: StringIO.new(thumbnail_blob),
  filename: thumbnail_filename,
  content_type: 'image/png',
  service_name: 'amazon_thumbnails'
)
```

## Risk Mitigation

### Critical Safeguards
1. **Idempotent Migration**: Rake task can be run multiple times safely
2. **Gradual Transition**: Old files work during entire transition
3. **Rollback Plan**: Keep legacy service until full verification
4. **Backup Strategy**: Original bucket remains untouched until cleanup

### Testing Strategy
1. **Development First**: Complete entire transition in dev environment
2. **File Verification**: Test both legacy and new files after each phase
3. **Lambda Testing**: Verify thumbnail generation works end-to-end
4. **Authorization Testing**: Ensure ActionPolicy still works correctly

## Success Criteria

- [ ] All existing files accessible and downloadable
- [ ] New uploads save to uploads bucket
- [ ] Lambda generates thumbnails in thumbnails bucket  
- [ ] File proxy controller works unchanged
- [ ] Download functionality unchanged
- [ ] ActionPolicy authorization unchanged
- [ ] No broken inline file display
- [ ] Zero data loss during transition

## Rollback Plan

If issues arise during migration:
1. **Immediate**: Revert `config.active_storage.service` to legacy service
2. **Database**: Reset `service_name` on migrated blobs back to legacy value
3. **Lambda**: Revert S3 trigger to old bucket
4. **Investigation**: Analyze issues before retry

## Tools & Dependencies

### Required Tools
- **AWS MCP Server**: For bucket creation and configuration
- **Context7 MCP**: For Rails Active Storage documentation reference
- **aws-sdk-s3 gem**: For S3 operations in migration task

### Documentation Dependencies  
- **Lambda Integration Guide**: For bucket permissions setup
- **LAMBDA_THUMBNAIL_INTEGRATION.md**: Current Lambda configuration
- **This Document**: For transition execution

## Context7 Documentation Reference

This transition plan addresses the findings that:
- Rails 7.0.8 has no native S3 prefix/folder support
- Custom workarounds are complex and brittle
- Separate buckets is the Rails-native, production-proven approach
- Lambda integration expects this bucket separation pattern

Store these findings in Context7 for future reference on Rails Active Storage S3 organization patterns.

---

**Next Steps**: Begin Phase 1 preparation with bucket creation using AWS MCP server.