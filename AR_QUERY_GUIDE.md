# Advanced ActiveRecord Querying Guide: The `full_list_for_user` Scope

This guide provides an in-depth look at the `Project.full_list_for_user` scope in the Unlisters App. We will analyze its current implementation, explain how it works, and detail a practical solution for making it compatible with pagination gems like Pagy, which rely on `.count`.

## 1. Current Implementation: The `full_list_for_user` Scope

The `:full_list_for_user` scope is a powerful query designed to fetch all projects a user is authorized to see, along with custom attributes about their access level.

Here is the current code from `app/models/project.rb`:

```ruby
scope :full_list_for_user, ->(user) {
  joins(<<~SQL)
    LEFT JOIN network_connections ON
      (network_connections.invitee_id = #{connection.quote(user.id)} AND network_connections.inviter_id = projects.user_id) OR
      (network_connections.inviter_id = #{connection.quote(user.id)} AND network_connections.invitee_id = projects.user_id)
    LEFT JOIN project_auths ON
      project_auths.project_id = projects.id AND
      project_auths.user_id = #{connection.quote(user.id)}
    LEFT JOIN users ON users.id = projects.user_id
  SQL
  .select(
    :id, :summary_only, :semi_public, :full_access, :summary,
    :location, :category, :subcategory, :project_type,
    :updated_at, :user_id,
    'users.id as owner_id',
    'project_auths.access_level AS auth_level',
    'project_auths.id AS auth_id'
  )
  .where(
    "(projects.network_only = true AND network_connections.id IS NOT NULL) OR
     projects.semi_public = true OR
     projects.user_id = :user_id",
    user_id: user.id
  )
  .order(updated_at: :desc)
}
```

### 1.1. How It Works: Deconstructing the Query

*   **Complex `JOIN`s with Raw SQL**: The scope uses a raw SQL `joins` clause to establish relationships that are too complex for standard ActiveRecord associations. It correctly handles the bidirectional nature of `NetworkConnection` and joins `ProjectAuth` specifically for the current user.
    *   **Security Note**: It uses `connection.quote(user.id)` to sanitize the user ID, which is a necessary security measure to prevent SQL injection when building raw SQL strings.

*   **Custom `SELECT`s and Dynamic Attributes**: The `.select()` clause is key. It retrieves standard `Project` columns (e.g., `:summary`) and creates three dynamic, read-only attributes on the resulting `Project` objects from joined tables:
    *   `owner_id`: The ID of the project's owner (`users.id`).
    *   `auth_level`: The access level the current user has for the project (`project_auths.access_level`).
    *   `auth_id`: The ID of the `ProjectAuth` record (`project_auths.id`).
    These attributes are essential for the views to render project listings correctly.

*   **`WHERE` Clause**: The `where` clause implements the business logic for visibility: a project is visible if it's shared with the user's network, is semi-public, or belongs to the user.

## 2. The Challenge: Incompatibility with `.count`

This scope works perfectly for fetching data, but it fails when you try to count the results, for instance, with `Project.full_list_for_user(user).count`. This is a critical issue for pagination libraries like `pagy`, which need to run a count query to know the total number of pages.

The failure happens because ActiveRecord tries to transform the custom `.select(...)` clause into a `COUNT` query, resulting in invalid SQL like `SELECT COUNT(projects.id, ..., 'users.id as owner_id', ...)` which databases cannot execute.

## 3. Immediate Solution: The `unscope(:select)` Strategy

To solve this problem without a major refactor, we can instruct `pagy` to use a count calculated from a "countable" version of the query. We create this countable version by temporarily removing the problematic `.select` clause using `unscope(:select)`.

This is the recommended immediate fix to apply in the controller.

**Implementation in `projects_controller.rb`:**

```ruby
# app/controllers/projects_controller.rb

def index
  @projects = if current_user
    # ...
    base_query = Project.full_list_for_user(current_user).includes(user: :user_profile).active.approved
    
    # ... (filtering logic) ...

    # Calculate count on a "countable" version of the query by removing the custom select.
    # The joins and where clauses remain, so the count is accurate.
    projects_count = base_query.unscope(:select).count
    
    # Pass the pre-calculated count to pagy.
    @pagy, @projects = pagy(base_query, count: projects_count, limit: 10)

  else
    Project.summary_visible_public
  end
  # ...
end
```

### Why This Works
This solution is effective because the `joins` and `where` clauses—which determine *which* records to count—are preserved. We only remove the `select` clause for the counting operation itself. The final `pagy` call still uses the original `base_query` to fetch the data for the current page, so the dynamic attributes (`owner_id`, `auth_level`, etc.) are available in the view as expected.

This approach stabilizes the application and ensures pagination works correctly while allowing for a more comprehensive refactor to be planned separately. 