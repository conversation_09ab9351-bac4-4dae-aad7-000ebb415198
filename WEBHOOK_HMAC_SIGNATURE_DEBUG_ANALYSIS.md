# Webhook HMAC Signature Debug Analysis

## Current Status: Rails HMAC Calculation is Wrong

**Confirmed Facts:**
- ✅ **Test Script/Lambda HMAC**: `588a6443a8cf6d4ccb1fce0e8c2ea4e3cf67331bcd630f1aad4f34ea6a00877d` (CORRECT)
- ❌ **Rails HMAC**: `e606acc2ff31cd68a8e7d501eb2bddcd61572a1acc06d800ddd9696f0bab4159` (WRONG)
- ✅ **Secret Match**: Both sides using `ye0fYc3~]d^v[l5Y]c:$g]:&)(nS_a0VfzwGDy$gX&asj3,2dGHIP~n$%-=8j+w]`
- ✅ **Timestamp Match**: `1750244233`
- ✅ **Payload Match**: Visually identical JSON structure

## Issues Already Solved ✅

### 1. Header Reading Problem (SOLVED)
- **Problem**: Rails couldn't read webhook headers
- **Root Cause**: Used `request.headers['X-Signature-Timestamp']` instead of `request.headers['HTTP_X_SIGNATURE_TIMESTAMP']`
- **Solution**: Fixed header access with HTTP_ prefix
- **Status**: ✅ Headers now read correctly

### 2. Secret Configuration (SOLVED)
- **Problem**: Suspected secret mismatch between Lambda and Rails
- **Verification**: Rails logs show `ye0fYc3~]d...` matching expected secret
- **Status**: ✅ Secrets are identical

### 3. Basic Payload Structure (SOLVED)
- **Problem**: Suspected JSON formatting differences
- **Verification**: Visual inspection shows identical JSON structure
- **Status**: ✅ Basic structure is identical

## Current Problem: Rails HMAC Calculation Bug

### Most Likely Root Causes (Per Gemini Analysis)

## 1. Raw Request Body Tampering (HIGHEST PROBABILITY)

**Issue**: Rails framework automatically parses and re-serializes JSON request bodies, introducing subtle changes.

**Evidence**:
- Rails logs show visually identical JSON
- HMAC signatures don't match despite identical inputs
- Classic symptom of framework body tampering

**Current Attempt**: Using `request.env['RAW_POST_DATA'] || request.raw_post`
**Status**: ⚠️ Still failing - may need deeper investigation

**Potential Tampering**:
- **Whitespace changes**: Spaces, newlines, indentation
- **Key ordering**: JSON key reordering (JSON doesn't guarantee order)
- **Data type conversion**: Number to string conversion, etc.

## 2. Character Encoding Issues (MEDIUM PROBABILITY)

**Issue**: Different character encodings between Lambda (Python) and Rails (Ruby)

**Current Debug**: Added encoding logging
**Need to Check**: 
- String encoding should be UTF-8 on both sides
- Byte representation must be identical

## 3. Hidden Characters in String-to-Sign (MEDIUM PROBABILITY)

**Issue**: Invisible characters (newlines, carriage returns, extra spaces) in string construction

**Current Debug**: Added length logging
**Missing Debug**: Need to use `.dump` to see non-printable characters

## Debugging Steps Completed

```ruby
# Added to Rails webhook controller:
Rails.logger.info "String to sign: #{string_to_sign}"
Rails.logger.info "String to sign length: #{string_to_sign.length}"
Rails.logger.info "String to sign encoding: #{string_to_sign.encoding}"
Rails.logger.info "Rails secret (first 10 chars): #{Rails.application.credentials.thumbnail_webhook_secret[0..9]}..."
```

## Next Debugging Steps (Priority Order)

### IMMEDIATE (High Priority)

1. **Add .dump Logging** to reveal hidden characters:
   ```ruby
   Rails.logger.info "String to sign (dumped): #{string_to_sign.dump}"
   ```

2. **Test Webhook Functionality** (signature verification temporarily disabled):
   - Verify core webhook logic works (thumbnail creation)
   - Isolate HMAC issue from business logic issues

3. **Byte-by-Byte Comparison**:
   - Get exact byte representation from both Lambda and Rails
   - Compare character by character

### SYSTEMATIC (Medium Priority)

4. **Alternative Raw Body Access**:
   ```ruby
   # Try different methods to get truly raw body
   request_body = request.body.read
   # or
   request_body = request.env['rack.input'].read
   ```

5. **Manual HMAC Verification**:
   - Create test script that uses exact Rails string-to-sign
   - Verify if Rails HMAC calculation logic is fundamentally broken

6. **Character Encoding Force**:
   ```ruby
   string_to_sign = string_to_sign.force_encoding('UTF-8')
   ```

### FALLBACK (Low Priority)

7. **Alternative Signature Method**:
   - Consider implementing simpler authentication (basic secret header)
   - If HMAC proves too problematic for Rails integration

## Success Criteria

**Phase 1: Core Functionality**
- ✅ Webhook receives request
- ✅ Thumbnail creation logic executes
- ✅ Thumbnail attached to Rails database

**Phase 2: Security**
- ❌ HMAC signature verification passes
- ❌ Request authentication works end-to-end

## Risk Assessment

**Current Risk**: Low
- Webhook functionality can work without signature verification in development
- Lambda thumbnail generation is working correctly
- Core file system integration is successful

**Security Risk**: Medium
- Production deployment needs signature verification
- Without HMAC, webhook is vulnerable to spoofing

## Implementation Strategy

1. **Get core functionality working first** (signature disabled)
2. **Fix HMAC calculation systematically**
3. **Re-enable signature verification**
4. **Test end-to-end integration**

## Files Modified

- `app/controllers/webhooks/thumbnails_controller.rb` - Added extensive debugging
- `app/models/project.rb` - Updated thumbnail lookup for Lambda naming
- Header access fix implemented

## Next Session Goals

1. Run webhook test with signature verification disabled
2. Analyze detailed debug output for string encoding/length
3. Add .dump logging for hidden characters
4. Implement byte-by-byte comparison if needed