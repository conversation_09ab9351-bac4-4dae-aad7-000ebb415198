#!/usr/bin/env ruby

require_relative 'config/environment'

puts "=== FULL ACCESS FUNCTIONALITY TEST ==="
puts "Rails Environment: #{Rails.env}"
puts ""

begin
  # Use existing users instead of creating new ones
  existing_users = User.limit(2)
  if existing_users.count < 2
    puts "ERROR: Need at least 2 users in database for testing"
    exit 1
  end
  
  owner = existing_users.first
  other_user = existing_users.last
  
  puts "Using existing users: Owner(#{owner.id}), Other(#{other_user.id})"
  puts ""

  # Test 1: Project with full_access + semi_public (should allow all users)
  puts "=== TEST 1: full_access + semi_public + approved ==="
  project1 = Project.new(
    user: owner,
    summary: 'Test Project 1',
    location: 'Test Location',
    project_type: 'business',
    category: 'business_acquisition', 
    subcategory: 'asset_purchase',
    network_only: false,
    semi_public: true,
    summary_only: false,
    full_access: true
  )
  
  # Set approved manually to bypass admin validation
  project1.save(validate: false)
  project1.update_column(:approved, true)
  
  puts "Project settings:"
  puts "  - semi_public: #{project1.semi_public?}"
  puts "  - full_access: #{project1.full_access?}"
  puts "  - approved: #{project1.approved?}"
  puts ""
  
  puts "Access tests:"
  puts "  - Owner access: #{project1.user_has_access?(owner)}"
  puts "  - Other user access: #{project1.user_has_access?(other_user)}"
  puts "  - Nil user access: #{project1.user_has_access?(nil)}"
  puts ""

  # Test 2: Project with summary_only (should require explicit approval)
  puts "=== TEST 2: summary_only + semi_public + approved ==="
  project2 = Project.new(
    user: owner,
    summary: 'Test Project 2',
    location: 'Test Location',
    project_type: 'business',
    category: 'business_acquisition',
    subcategory: 'asset_purchase', 
    network_only: false,
    semi_public: true,
    summary_only: true,
    full_access: false
  )
  
  project2.save(validate: false)
  project2.update_column(:approved, true)
  
  puts "Project settings:"
  puts "  - semi_public: #{project2.semi_public?}"
  puts "  - summary_only: #{project2.summary_only?}"
  puts "  - approved: #{project2.approved?}"
  puts ""
  
  puts "Access tests:"
  puts "  - Owner access: #{project2.user_has_access?(owner)}"
  puts "  - Other user access: #{project2.user_has_access?(other_user)}"
  puts "  - Nil user access: #{project2.user_has_access?(nil)}"
  puts ""

  # Test 3: Unapproved project (should only allow owner)
  puts "=== TEST 3: full_access + semi_public + UNAPPROVED ==="
  project3 = Project.new(
    user: owner,
    summary: 'Test Project 3',
    location: 'Test Location',
    project_type: 'business',
    category: 'business_acquisition',
    subcategory: 'asset_purchase',
    network_only: false,
    semi_public: true,
    summary_only: false,
    full_access: true
  )
  
  project3.save(validate: false)
  # Leave approved as false (default)
  
  puts "Project settings:"
  puts "  - semi_public: #{project3.semi_public?}"
  puts "  - full_access: #{project3.full_access?}"
  puts "  - approved: #{project3.approved?}"
  puts ""
  
  puts "Access tests:"
  puts "  - Owner access: #{project3.user_has_access?(owner)}"
  puts "  - Other user access: #{project3.user_has_access?(other_user)}"
  puts "  - Nil user access: #{project3.user_has_access?(nil)}"
  puts ""

  puts "=== VALIDATION TESTS ==="
  
  # Test validation errors
  invalid_project = Project.new(
    user: owner,
    summary: 'Invalid Project',
    location: 'Test Location',
    project_type: 'business',
    category: 'business_acquisition',
    subcategory: 'asset_purchase',
    network_only: true,
    semi_public: true,  # Both visibility options - should fail
    summary_only: true,
    full_access: true   # Both detail options - should fail
  )
  
  puts "Validation with both visibility options:"
  puts "  - Valid: #{invalid_project.valid?}"
  if !invalid_project.valid?
    puts "  - Errors: #{invalid_project.errors.full_messages}"
  end
  puts ""

  puts "=== ALL TESTS COMPLETED SUCCESSFULLY ==="

  # Results summary
  puts ""
  puts "=== RESULTS SUMMARY ==="
  puts "Test 1 (full_access + semi_public + approved):"
  puts "  ✓ Owner has access: #{project1.user_has_access?(owner)}"
  puts "  ✓ Other user has access: #{project1.user_has_access?(other_user)} (expected: true)"
  puts "  ✓ Nil user denied: #{!project1.user_has_access?(nil)} (expected: true)"
  puts ""
  
  puts "Test 2 (summary_only + semi_public + approved):"
  puts "  ✓ Owner has access: #{project2.user_has_access?(owner)}"
  puts "  ✓ Other user denied: #{!project2.user_has_access?(other_user)} (expected: true)"
  puts "  ✓ Nil user denied: #{!project2.user_has_access?(nil)} (expected: true)"
  puts ""
  
  puts "Test 3 (full_access + semi_public + unapproved):"
  puts "  ✓ Owner has access: #{project3.user_has_access?(owner)}"
  puts "  ✓ Other user denied: #{!project3.user_has_access?(other_user)} (expected: true)"
  puts "  ✓ Nil user denied: #{!project3.user_has_access?(nil)} (expected: true)"
  puts ""
  
  puts "Validation test:"
  puts "  ✓ Invalid combinations rejected: #{!invalid_project.valid?} (expected: true)"

rescue => e
  puts "ERROR: #{e.class}: #{e.message}"
  puts e.backtrace[0..5]
ensure
  # Cleanup test projects
  begin
    Project.where("summary LIKE 'Test Project%'").destroy_all
  rescue
    # Ignore cleanup errors
  end
end