#!/usr/bin/env ruby

require_relative 'config/environment'

puts "=== FULL ACCESS FUNCTIONALITY TEST ==="
puts "Rails Environment: #{Rails.env}"
puts ""

begin
  # Clear test data
  ActiveRecord::Base.connection.execute("DELETE FROM projects WHERE summary LIKE 'Test Project%'")
  ActiveRecord::Base.connection.execute("DELETE FROM users WHERE email LIKE '<EMAIL>'")
  
  # Create test users
  puts "Creating test users..."
  owner = User.create!(
    email: '<EMAIL>',
    password: 'password123',
    password_confirmation: 'password123',
    confirmed_at: Time.current
  )
  
  other_user = User.create!(
    email: '<EMAIL>', 
    password: 'password123',
    password_confirmation: 'password123',
    confirmed_at: Time.current
  )
  
  puts "✓ Created users: Owner(#{owner.id}), Other(#{other_user.id})"
  puts ""

  # Test 1: Project with full_access + semi_public (should allow all users)
  puts "=== TEST 1: full_access + semi_public + approved ==="
  project1 = Project.create!(
    user: owner,
    summary: 'Test Project 1',
    location: 'Test Location',
    project_type: 'business',
    category: 'business_acquisition', 
    subcategory: 'asset_purchase',
    network_only: false,
    semi_public: true,
    summary_only: false,
    full_access: true,
    approved: true
  )
  
  puts "Project settings:"
  puts "  - semi_public: #{project1.semi_public?}"
  puts "  - full_access: #{project1.full_access?}"
  puts "  - approved: #{project1.approved?}"
  puts ""
  
  puts "Access tests:"
  puts "  - Owner access: #{project1.user_has_access?(owner)}"
  puts "  - Other user access: #{project1.user_has_access?(other_user)}"
  puts "  - Nil user access: #{project1.user_has_access?(nil)}"
  puts ""

  # Test 2: Project with summary_only (should require explicit approval)
  puts "=== TEST 2: summary_only + semi_public + approved ==="
  project2 = Project.create!(
    user: owner,
    summary: 'Test Project 2',
    location: 'Test Location',
    project_type: 'business',
    category: 'business_acquisition',
    subcategory: 'asset_purchase', 
    network_only: false,
    semi_public: true,
    summary_only: true,
    full_access: false,
    approved: true
  )
  
  puts "Project settings:"
  puts "  - semi_public: #{project2.semi_public?}"
  puts "  - summary_only: #{project2.summary_only?}"
  puts "  - approved: #{project2.approved?}"
  puts ""
  
  puts "Access tests:"
  puts "  - Owner access: #{project2.user_has_access?(owner)}"
  puts "  - Other user access: #{project2.user_has_access?(other_user)}"
  puts "  - Nil user access: #{project2.user_has_access?(nil)}"
  puts ""

  # Test 3: Unapproved project (should only allow owner)
  puts "=== TEST 3: full_access + semi_public + UNAPPROVED ==="
  project3 = Project.create!(
    user: owner,
    summary: 'Test Project 3',
    location: 'Test Location',
    project_type: 'business',
    category: 'business_acquisition',
    subcategory: 'asset_purchase',
    network_only: false,
    semi_public: true,
    summary_only: false,
    full_access: true,
    approved: false
  )
  
  puts "Project settings:"
  puts "  - semi_public: #{project3.semi_public?}"
  puts "  - full_access: #{project3.full_access?}"
  puts "  - approved: #{project3.approved?}"
  puts ""
  
  puts "Access tests:"
  puts "  - Owner access: #{project3.user_has_access?(owner)}"
  puts "  - Other user access: #{project3.user_has_access?(other_user)}"
  puts "  - Nil user access: #{project3.user_has_access?(nil)}"
  puts ""

  # Test 4: Explicit ProjectAuth (should override automatic rules)
  puts "=== TEST 4: Explicit ProjectAuth ==="
  # Give other_user explicit access to project2
  project_auth = ProjectAuth.create!(
    user: other_user,
    project: project2,
    access_level: 'full_details'
  )
  
  puts "Created explicit ProjectAuth for project2"
  puts "Access tests:"
  puts "  - Owner access: #{project2.user_has_access?(owner)}"
  puts "  - Other user access (with ProjectAuth): #{project2.user_has_access?(other_user)}"
  puts ""

  puts "=== VALIDATION TESTS ==="
  
  # Test validation errors
  invalid_project = Project.new(
    user: owner,
    summary: 'Invalid Project',
    location: 'Test Location',
    project_type: 'business',
    category: 'business_acquisition',
    subcategory: 'asset_purchase',
    network_only: true,
    semi_public: true,  # Both visibility options - should fail
    summary_only: true,
    full_access: true   # Both detail options - should fail
  )
  
  puts "Validation with both visibility options:"
  puts "  - Valid: #{invalid_project.valid?}"
  if !invalid_project.valid?
    puts "  - Errors: #{invalid_project.errors.full_messages}"
  end
  puts ""

  puts "=== ALL TESTS COMPLETED SUCCESSFULLY ==="

rescue => e
  puts "ERROR: #{e.class}: #{e.message}"
  puts e.backtrace[0..5]
ensure
  # Cleanup
  ActiveRecord::Base.connection.execute("DELETE FROM projects WHERE summary LIKE 'Test Project%'")
  ActiveRecord::Base.connection.execute("DELETE FROM users WHERE email LIKE '<EMAIL>'")
end