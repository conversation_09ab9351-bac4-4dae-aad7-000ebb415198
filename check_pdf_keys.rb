#!/usr/bin/env ruby

project = Project.find(1)
pdf_file = project.private_files.joins(:blob).where('active_storage_blobs.content_type = ?', 'application/pdf').first

if pdf_file
  puts "PDF filename: #{pdf_file.filename}"
  puts "PDF key: #{pdf_file.blob.key}"
  puts "Expected thumbnail key: #{pdf_file.blob.key.gsub(/\.[^.]+$/, '.png')}"
  
  # Check if the key matches the pattern you showed
  if pdf_file.blob.key.include?("51b057160181d79369b19779a30de1e6")
    puts "✅ This matches the S3 example key pattern"
  else
    puts "❌ Key doesn't match expected pattern"
  end
  
  # Check what the actual S3 directory structure looks like
  key_parts = pdf_file.blob.key.split('/')
  puts "Key structure: #{key_parts.inspect}"
  
else
  puts "No PDF files found"
end