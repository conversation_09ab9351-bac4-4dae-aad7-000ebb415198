# Lambda Webhook Setup Instructions

## Adding Webhook Secret to Rails Credentials

You need to manually add the webhook secret to your Rails credentials. Follow these steps:

1. **Open Rails credentials for editing:**
   ```bash
   EDITOR=nano rails credentials:edit
   ```

2. **Add the webhook secret:**
   Add this line to your credentials file:
   ```yaml
   thumbnail_webhook_secret: YOUR_WEBHOOK_SECRET_FROM_LAMBDA
   ```
   
   Replace `YOUR_WEBHOOK_SECRET_FROM_LAMBDA` with the actual secret provided when you deployed the Lambda function.

3. **Save and exit:**
   - Press `Ctrl+X` to exit
   - Press `Y` to confirm saving
   - Press `Enter` to confirm the filename

4. **Verify the credentials were saved:**
   ```bash
   rails console
   Rails.application.credentials.thumbnail_webhook_secret
   ```
   
   This should return your webhook secret (or nil if not set yet).

## Important Notes

- The webhook secret must match exactly between Lambda and Rails
- Keep this secret secure - it's used to verify webhook authenticity
- The secret should be a long, random string (e.g., 64+ characters)
- Never commit the secret to version control

## Testing the Webhook

Once the secret is configured, you can test the webhook endpoint:

```bash
# Generate test timestamp
TIMESTAMP=$(date +%s)

# Generate test signature (replace YOUR_SECRET with actual secret)
PAYLOAD='{"original_blob_key":"uploads/test.pdf","thumbnail":{"key":"thumbnails/test.png","filename":"test.png","content_type":"image/png","byte_size":12345,"checksum":"abc123"}}'
STRING_TO_SIGN="${TIMESTAMP}.${PAYLOAD}"
SIGNATURE=$(echo -n "$STRING_TO_SIGN" | openssl dgst -sha256 -hmac "YOUR_SECRET" | cut -d' ' -f2)

# Send test request
curl -X POST http://localhost:3000/wh/thumb_ready \
  -H "Content-Type: application/json" \
  -H "X-Signature-Timestamp: $TIMESTAMP" \
  -H "X-Signature-Hmac-Sha256: $SIGNATURE" \
  -d "$PAYLOAD"
```

Expected response:
- 404 if blob not found (normal for test data)
- 401 if signature is invalid
- 200 if processing succeeds