
# More hacky way to secure routes that would be better handled by disabling them completely.
# In application.rb: 
#     config.active_storage.draw_routes = false
 
#
# Rails.application.config.after_initialize do
#   Rails.application.routes.default_url_options[:host] = 'localhost:3000'
  
#   ActiveStorage::BaseController.class_eval do
#     before_action :check_private_file
    
#     private
    
#     def check_private_file
#       if blob.attachments.any? { |a| a.record.is_a?(Project) }
#         head :forbidden
#       end
#     end
#   end
# end