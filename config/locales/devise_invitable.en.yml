en:
  devise:
    failure:
      invited: "You have a pending invitation, accept it to finish creating your account."
    invitations:
      send_instructions: "An invitation email has been sent to %{email}."
      invitation_token_invalid: "The invitation token provided is not valid!"
      updated: "Your password was set successfully. You are now signed in."
      updated_not_active: "Your password was set successfully."
      no_invitations_remaining: "No invitations remaining"
      invitation_removed: "Your invitation was removed."
      new:
        header: "Send invitation"
        submit_button: "Send an invitation"
      edit:
        header: "Set your password"
        submit_button: "Set my password"
    mailer:
      invitation_instructions:
        subject: "%{inviter_first_name} %{inviter_last_name} invites you to join the Unlisters Network"
        hello: "To %{email}"
        someone_invited_you: "A member of our professional community has invited you to join the Unlisters Network, a platform for connections and opportunities."
        accept: "Join Unlisters"
        accept_until: "This invitation will expire on %{due_date}."
        ignore: "If you prefer not to join at this time, you may disregard this email. Your account will only be created after you visit the link above and set your password."
        features:
          title: "Benefits of Unlisters Network"
          curated_membership: "A community of professionals and investors"
          private_sharing: "Secure sharing of unlisted opportunities"
          control_visibility: "Complete control over your information visibility"
          meaningful_connections: "Relevant connections based on shared interests"
        closing: "Regards,"
        signature: "Unlisters Team"
        questions_contact: "Questions? Contact us at"
  
  time:
    formats:
      devise:
        mailer:
          invitation_instructions:
            accept_until_format: "%B %d, %Y %I:%M %p"