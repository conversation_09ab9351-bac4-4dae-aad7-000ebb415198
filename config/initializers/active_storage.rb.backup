# Active Storage configuration

Rails.application.config.after_initialize do
  # Configure variant processor - using libvips for better performance
  Rails.application.config.active_storage.variant_processor = :vips
  
  # Ensure Active Storage routes are not drawn (security measure)
  # This is already set in application.rb, but reinforcing here for clarity
  Rails.application.config.active_storage.draw_routes = false
  
  # PopplerPDFPreviewer should be included by default, but let's be explicit
  # Note: Only add if not already present to avoid duplicates
  unless ActiveStorage.previewers.include?(ActiveStorage::Previewer::PopplerPDFPreviewer)
    ActiveStorage.previewers << ActiveStorage::Previewer::PopplerPDFPreviewer
  end
end