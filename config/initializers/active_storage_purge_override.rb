# ABOUTME: ❌ INCORRECT FIX - ActiveStorage patch based on wrong root cause analysis
# ABOUTME: ⚠️  WARNING: This approach is MISLEADING - kept as debugging breadcrumb for future reference

# ❌ INCORRECT APPROACH - DO NOT RE-ENABLE ❌
# This was an incorrect attempt to fix bulk upload race condition by preventing purge_later calls
# 
# REAL ROOT CAUSE (confirmed): ActiveRecord race condition in has_many_attached during concurrent access
# - Multiple jobs read same project.private_files state
# - "Last write wins" overwrites earlier attachment records
# - Files exist on S3 but ActiveStorage::Attachment records are lost
#
# CORRECT FIX: Pessimistic locking with project.with_lock in FileUploadJob
# See: docs/features/file-system/10_bulk-upload-race-condition-investigation.md
#
# KEPT FOR REFERENCE: If you're debugging similar symptoms, this purge approach is a red herring

# Override ActiveStorage::Blob#purge_later to respect Current.skip_purge_duplicates flag
# This prevents race conditions where duplicate blobs are purged before Lambda can process them
# ActiveSupport.on_load(:active_storage_blob) do
#   ActiveStorage::Blob.class_eval do
#     # Store original method before overriding
#     alias_method :original_purge_later, :purge_later
#     
#     # Override purge_later to respect skip_purge_duplicates context
#     def purge_later
#       # Skip purge when flag is set (during bulk uploads)
#       if Current.skip_purge_duplicates
#         Rails.logger.debug "Skipping purge_later for blob #{id} due to Current.skip_purge_duplicates flag"
#         return
#       end
#       
#       # Call original method when purge is allowed
#       original_purge_later
#     end
#   end
# end

# Rails.logger.info "ActiveStorage purge override initialized for bulk upload race condition prevention"
Rails.logger.info "LEGACY: ActiveStorage purge override disabled - investigating transaction isolation issue"