# Require JWT for token decoding in streaming user throttle
require 'jwt'

class Rack::Attack
  # CACHE STRATEGY: Multiple options available for different deployment scenarios
  
  # ACTIVE: FileStore - Production-ready shared cache without infrastructure dependencies
  # Pros: Works across multiple processes, no additional services required
  # Cons: File I/O overhead, not optimal for very high traffic
  # SCALING WARNING: Only works correctly on SINGLE INSTANCE deployments
  # When scaling to multiple instances, rate limits become per-instance (undermines DoS protection)
  # Gemini Recommendation: Configure size limit to prevent disk space issues
  Rack::Attack.cache.store = ActiveSupport::Cache::FileStore.new(
    Rails.root.join("tmp", "cache", "rack_attack"),
    size: 64.megabytes # Prevent unlimited disk usage
  )
  
  # FALLBACK OPTION 1: Database-backed cache (table exists: cache_entries)
  # Uncomment to use database instead of FileStore
  # Pros: Familiar infrastructure, works with existing database
  # Cons: Adds database load, potential DoS risk during attacks
  # Rack::Attack.cache.store = ActiveSupport::Cache::DatabaseStore.new(
  #   "cache_entries", # table name
  #   namespace: "rack_attack",
  #   expires_in: 1.day
  # )
  
  # FUTURE OPTION: Redis (see BACKLOG.md #19)
  # Best performance for high-traffic production environments
  # Requires Redis infrastructure ($7-15/month)
  # Rack::Attack.cache.store = ActiveSupport::Cache::RedisCacheStore.new(url: ENV['REDIS_URL'])
  
  # DEVELOPMENT FALLBACK: MemoryStore (single process only)
  # Only use in development for testing purposes
  # Rack::Attack.cache.store = ActiveSupport::Cache::MemoryStore.new if Rails.env.development?

  # Throttle Spammy Clients 
  throttle('req/ip', limit: 300, period: 5.minutes) do |req|
    req.ip
  end

  # Throttle login attempts (Devise)
  throttle('limit logins/ip', limit: 5, period: 1.minute) do |req|
    if req.path == '/users/sign_in' && req.post?
      req.ip
    end
  end

  # Throttle registration attempts (Devise)
  throttle('registrations/ip', limit: 5, period: 1.hour) do |req|
    req.ip if req.path == '/users/sign_up' && req.post?
  end

  # Throttle password reset attempts (Devise)
  throttle('password-reset/ip', limit: 3, period: 15.minutes) do |req|
    if req.path == '/users/password' && req.post?
      req.ip
    end
  end

  # SECURITY FIX: Removed path constants as we now use precise regex matching
  # instead of broad string inclusion to prevent unintended rate limiting

  # SECURITY FIX: Use precise regex matching instead of broad string inclusion
  # This prevents unintended rate limiting on other endpoints that might contain the path segment
  # Throttle secure file token requests by IP (more permissive for shared IPs/NAT)
  throttle('secure_file_tokens/ip', limit: 100, period: 1.minute) do |req|
    if req.path.match?(%r{/projects/\d+/request_file_token$}) && req.post?
      req.ip
    end
  end

  # SECURITY FIX: User-based throttle should be stricter than IP-based
  # This prevents individual users from abusing the system even from shared IPs
  throttle('secure_file_tokens/user', limit: 20, period: 1.minute) do |req|
    if req.path.match?(%r{/projects/\d+/request_file_token$}) && req.post?
      # SECURITY FIX: Use official Warden API instead of internal session structure
      # This is more stable and resistant to gem updates
      user = req.env['warden']&.user
      user&.id
    end
  end

  # CRITICAL SECURITY FIX: Add rate limiting to the file streaming endpoint itself.
  # This prevents DoS attacks where an attacker uses a single valid token to repeatedly
  # request large files, exhausting server bandwidth, I/O, and CPU resources.
  # SECURITY FIX: Use exact path matching for the actual route
  throttle('secure_stream/ip', limit: 60, period: 1.minute) do |req|
    if req.path == '/secure/stream' && req.get?
      req.ip
    end
  end

  # Additional protection: Rate limit streaming by user (if token contains user info)
  # This provides defense-in-depth against authenticated users abusing file downloads
  throttle('secure_stream/user', limit: 40, period: 1.minute) do |req|
    if req.path == '/secure/stream' && req.get?
      # Extract user from token parameter for additional rate limiting
      token = req.params['t']
      if token.present?
        begin
          # This mirrors the token decoding logic from SecureFileTokenService
          payload = JWT.decode(token, Rails.application.secret_key_base, true, algorithm: 'HS256')[0]
          payload['user_id'] if payload
        rescue JWT::DecodeError
          # If token is invalid, we still want to rate limit by something
          # Fall back to IP-based limiting which is already handled above
          nil
        end
      end
    end
  end

  # Custom response for throttled requests to return JSON.
  # This provides a better experience for API consumers.
  self.throttled_responder = lambda do |env|
    match_data = env['rack.attack.match_data']
    
    # Check if the throttle is one of the secure file throttles
    if match_data[:name].start_with?('secure_file_tokens/', 'secure_stream/')
      headers = {
        'Content-Type' => 'application/json',
        'Retry-After' => match_data[:period].to_s
      }
      body = {
        error: 'Rate limit exceeded. Please try again later.',
        retry_after: match_data[:period]
      }.to_json

      [429, headers, [body]]
    else
      # Fallback to default Rack::Attack response for other throttles
      [ 
        429, 
        { 'Content-Type' => 'text/plain' },
        ["Retry later\n"] 
      ]
    end
  end

  # Allow higher rates from localhost for development
  safelist('allow from localhost') do |req|
    req.ip == '127.0.0.1' || req.ip == '::1'
  end

  # Allow LAN access in development without rate limiting
  if Rails.env.development?
    safelist('allow from LAN') do |req|
      # Common private network ranges
      ip = IPAddr.new(req.ip)
      
      # Check if IP is in private network ranges
      private_ranges = [
        IPAddr.new('10.0.0.0/8'),         # Class A private
        IPAddr.new('**********/12'),      # Class B private
        IPAddr.new('***********/16'),     # Class C private (includes your 192.168.1.x)
        IPAddr.new('fc00::/7')            # IPv6 private
      ]
      
      private_ranges.any? { |range| range.include?(ip) }
    rescue IPAddr::InvalidAddressError
      false
    end
  end
end