if Rails.env.development? && ENV['MEMORY_PROFILING'].present?
  require 'objspace'
  
  puts "🔍 Memory profiling enabled! Object allocation tracing is ON."
  puts "⚠️  This will impact performance - only use for debugging memory leaks."
  
  # Enable allocation tracing to get detailed object creation info
  ObjectSpace.trace_object_allocations_start
  
  # Add a cleanup hook for when the server shuts down
  at_exit do
    ObjectSpace.trace_object_allocations_stop
    puts "🔍 Memory profiling disabled."
  end
end