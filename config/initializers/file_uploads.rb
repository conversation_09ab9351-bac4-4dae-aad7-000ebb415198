# ABOUTME: File upload configuration for server-side uploads with Ready Flag pattern
# ABOUTME: Defines upload paths, size limits, and allowed content types for secure file handling

Rails.application.configure do
  # Upload temporary storage path - uses Render Disk in production, local temp in development
  config.upload_temp_path = ENV.fetch('RENDER_DISK_UPLOAD_PATH', Rails.root.join('tmp', 'uploads'))
  
  # File upload constraints
  config.max_upload_size = ENV.fetch('MAX_UPLOAD_SIZE_MB', '100').to_i.megabytes
  config.max_uploads_per_request = ENV.fetch('MAX_UPLOADS_PER_REQUEST', '10').to_i
  
  # Allowed content types for security
  config.allowed_upload_types = ENV.fetch('ALLOWED_UPLOAD_TYPES', 
    'image/jpeg,image/png,image/gif,image/webp,' \
    'application/pdf,' \
    'application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,' \
    'application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,' \
    'application/vnd.ms-powerpoint,application/vnd.openxmlformats-officedocument.presentationml.presentation,' \
    'text/plain,text/csv'
  ).split(',').map(&:strip)
  
  # File cleanup settings
  config.temp_file_cleanup_interval = ENV.fetch('TEMP_FILE_CLEANUP_HOURS', '24').to_i.hours
  config.failed_upload_retention = ENV.fetch('FAILED_UPLOAD_RETENTION_DAYS', '7').to_i.days
  
  # Upload rate limiting
  config.upload_rate_limit_per_user_per_hour = ENV.fetch('UPLOAD_RATE_LIMIT', '50').to_i
  
  # Progress update frequency (in bytes)
  config.progress_update_threshold = ENV.fetch('PROGRESS_UPDATE_THRESHOLD_MB', '1').to_i.megabytes
end

# Ensure upload temp directory exists (only at runtime, not during build)
Rails.application.config.after_initialize do
  # Only create directory if we're running as a Render service (web or worker)
  # RENDER_SERVICE_TYPE is not set during build phase
  if ENV['RENDER_SERVICE_TYPE']
    upload_path = Rails.application.config.upload_temp_path
    
    # If using Render disk mount, the directory already exists as the mount point
    if ENV['RENDER_DISK_UPLOAD_PATH']
      Rails.logger.info "Using Render disk mount for uploads: #{upload_path}"
    else
      # For local temp directories, create if needed
      begin
        FileUtils.mkdir_p(upload_path) unless File.directory?(upload_path)
        Rails.logger.info "Upload temp path configured: #{upload_path}"
      rescue => e
        Rails.logger.error "Could not create upload temp path: #{upload_path}. Error: #{e.message}"
      end
    end
  else
    Rails.logger.info "Skipping upload temp path creation (build phase or local env)"
  end
end

# Helper module for upload utilities
module UploadUtils
  extend self
  
  def allowed_content_type?(content_type)
    Rails.application.config.allowed_upload_types.include?(content_type)
  end
  
  def file_size_valid?(size)
    size > 0 && size <= Rails.application.config.max_upload_size
  end
  
  def generate_temp_filename(original_filename)
    ext = File.extname(original_filename)
    "#{SecureRandom.hex(16)}#{ext}"
  end
  
  def temp_file_path(filename)
    File.join(Rails.application.config.upload_temp_path, filename)
  end
  
  def content_type_category(content_type)
    case content_type
    when /^image\//
      'image'
    when /^application\/pdf/
      'pdf'
    when /word|document/
      'document'
    when /excel|spreadsheet/
      'spreadsheet'
    when /powerpoint|presentation/
      'presentation'
    when /^text\//
      'text'
    else
      'other'
    end
  end
end