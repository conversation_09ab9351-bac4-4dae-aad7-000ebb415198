# Active Storage S3 Key Extension for Normal Uploads
#
# This initializer adds file extensions to S3 keys for normal form uploads.
# Since this app doesn't use actual direct uploads (no Active Storage JS), 
# files are uploaded via normal form submission and the blob is created 
# during Rails save process.
#
# Result: S3 keys like "abc123xyz.pdf" instead of "abc123xyz"

Rails.application.config.to_prepare do
  ActiveStorage::Blob.class_eval do
    # Add callback to append extension to key after generation but before save
    before_create :append_file_extension_to_key

    private

    def append_file_extension_to_key
      # Only add extension if key exists and filename has an extension
      return unless key.present? && filename.present?
      
      # Get the file extension with dot (e.g., ".pdf", ".jpg")
      extension = filename.extension_with_delimiter
      
      # Only append if there's an extension and it's not already there
      if extension.present? && !key.end_with?(extension)
        self.key = "#{key}#{extension}"
        Rails.logger.debug "Added extension to S3 key: #{key}"
      end
    end
  end
  
  Rails.logger.info "Active Storage S3 key extension for normal uploads loaded"
end