# PRODUCTION MEMORY LEAK PREVENTION
# This initializer prevents memory leaks in production by managing caches and forcing periodic cleanup

if Rails.env.production?
  
  # Background thread for periodic memory cleanup
  Thread.new do
    loop do
      begin
        sleep 300 # Every 5 minutes
        
        current_memory_mb = `ps -o rss= -p #{Process.pid}`.to_i / 1024.0
        
        Rails.logger.info "Memory check: #{current_memory_mb.round(1)}MB"
        
        # Aggressive cleanup if memory is high
        if current_memory_mb > 400
          Rails.logger.warn "High memory usage detected: #{current_memory_mb.round(1)}MB - performing cleanup"
          
          # Clear all caches
          Rails.cache.clear
          
          # Clear ActionView template cache
          ActionView::LookupContext::DetailsKey.clear if defined?(ActionView::LookupContext::Details<PERSON>ey)
          
          # Clear I18n cache
          I18n.backend.reload! if I18n.backend.respond_to?(:reload!)
          
          # Clear geocoder cache
          if defined?(Geocoder) && Geocoder.config.cache.is_a?(Hash)
            Geocoder.config.cache.clear
          end
          
          # Clear ActiveRecord query cache
          ActiveRecord::Base.connection.clear_query_cache
          
          # Force garbage collection
          GC.start
          ObjectSpace.garbage_collect
          
          after_memory_mb = `ps -o rss= -p #{Process.pid}`.to_i / 1024.0
          freed_mb = current_memory_mb - after_memory_mb
          
          Rails.logger.info "Memory cleanup completed: #{after_memory_mb.round(1)}MB (freed #{freed_mb.round(1)}MB)"
        end
        
      rescue => e
        Rails.logger.error "Memory management thread error: #{e.message}"
      end
    end
  end
  
  # Hook into request cycle for periodic cleanup
  ActiveSupport::Notifications.subscribe('process_action.action_controller') do |*args|
    begin
      # Randomly clear caches on 5% of requests to prevent accumulation
      if rand < 0.05
        # Clear translation cache
        cache_keys = Rails.cache.instance_variable_get(:@data)&.keys || []
        translation_keys = cache_keys.select { |k| k.to_s.include?('project_translations') || k.to_s.include?('project_placeholders') }
        
        if translation_keys.size > 20 # If we have too many cached translations
          translation_keys.each { |key| Rails.cache.delete(key) }
          Rails.logger.debug "Cleared #{translation_keys.size} translation cache entries"
        end
      end
      
      # Force GC on 2% of requests
      if rand < 0.02
        GC.start
      end
      
    rescue => e
      Rails.logger.error "Request cleanup error: #{e.message}"
    end
  end
  
  Rails.logger.info "Production memory management initialized"
  
end