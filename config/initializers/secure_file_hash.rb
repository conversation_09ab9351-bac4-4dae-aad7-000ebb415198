# config/initializers/secure_file_hash.rb

# This initializer configures a dedicated salt for secure file hashing.
# Using a specific salt for this feature enhances security by isolating
# the file hash generation from other cryptographic functions that might
# also use Rails.application.secret_key_base.

# Generate a salt from the environment or fall back to a derivation from the secret_key_base.
# Using a dedicated environment variable is recommended for production.
FILE_HASH_SALT = ENV.fetch('FILE_HASH_SALT') do
  # Fallback for development/testing if ENV['FILE_HASH_SALT'] is not set.
  # This still provides a strong secret derived from the main application secret.
  Digest::SHA256.hexdigest(Rails.application.secret_key_base + "file_hash_salt")
end 