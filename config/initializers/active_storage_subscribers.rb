# Active Storage Thumbnail Generation Subscriber
#
# This subscriber listens for when ActiveStorage::Ana<PERSON><PERSON><PERSON><PERSON> completes.
# At this point:
# 1. File has been uploaded to S3 completely
# 2. ActiveStorage has analyzed the file metadata
# 3. <PERSON><PERSON><PERSON> is ready for thumbnail generation
#
# We use the ActiveJob perform event to hook into the completion of analysis

Rails.application.config.after_initialize do
  # Subscribe to ActiveJob perform events
  ActiveSupport::Notifications.subscribe "perform.active_job" do |*args|
    event = ActiveSupport::Notifications::Event.new(*args)
    job = event.payload[:job]
    
    # Only process ActiveStorage::AnalyzeJob completions
    next unless job.is_a?(ActiveStorage::AnalyzeJob)
    
    # Get the blob from the job arguments
    blob = job.arguments.first
    
    # Ensure we have a valid blob
    unless blob.is_a?(ActiveStorage::Blob)
      Rails.logger.warn "Active Storage subscriber: No valid blob in AnalyzeJob"
      next
    end
    
    begin
      # DISABLED: Rails thumbnail generation for images - use Lambda for all file types
      # Only process blobs that are attached to Project records
      # blob.attachments.each do |attachment|
      #   next unless attachment.record_type == 'Project'
      #   
      #   # Only process image files (PDFs handled by Lambda)
      #   next unless blob.analyzed? && blob.image?
      #   
      #   Rails.logger.info "Active Storage subscriber: Enqueuing thumbnail job for blob #{blob.id} (#{blob.filename})"
      #   
      #   # Enqueue the thumbnail generation job
      #   # The blob is now guaranteed to be uploaded and analyzed
      #   ThumbnailGenerationJob.perform_later(attachment.record, blob.id)
      # end
      
      # ALL THUMBNAILS NOW HANDLED BY LAMBDA via webhook
      Rails.logger.info "Active Storage subscriber: Skipping Rails thumbnail generation - Lambda handles all file types"
    rescue => e
      Rails.logger.error "Active Storage subscriber error: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      # Don't re-raise - we don't want to break the analyze job
    end
  end
  
  Rails.logger.info "Active Storage thumbnail subscriber initialized"
end