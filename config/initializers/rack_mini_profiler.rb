# MEMORY LEAK FIX: Ensure rack-mini-profiler is NEVER enabled in production
if Rails.env.development? && defined?(Rack::MiniProfiler)
  Rack::MiniProfiler.config.position = 'bottom-right'
  Rack::MiniProfiler.config.start_hidden = false
elsif Rails.env.production? && defined?(Rack::MiniProfiler)
  # Force disable in production if somehow loaded
  Rack::MiniProfiler.config.enabled = false
  Rails.logger.warn "Rack::MiniProfiler detected in production - forcibly disabled to prevent memory leaks"
end