# JWT Configuration for Secure File Token System
# 
# This initializer configures JWT constants for generating secure, time-limited tokens
# used in the secure inline file display system. These tokens provide access to 
# private files without exposing file URLs or S3 paths.
#
# Security Features:
# - Uses Rails application secret key base for cryptographic security
# - HS256 algorithm for HMAC-based token signing
# - 5-minute expiration to minimize security exposure window
# - Tokens contain no sensitive file metadata

# JWT secret key - uses Rails secure secret key base
JWT_SECRET = Rails.application.secret_key_base

# JWT algorithm - HMAC SHA256 for secure signing
JWT_ALGORITHM = 'HS256'

# Token expiration time - 5 minutes for security
JWT_EXPIRATION = 5.minutes

Rails.logger.info "[JWT] Secure file token system initialized with #{JWT_EXPIRATION.to_i}s expiration"