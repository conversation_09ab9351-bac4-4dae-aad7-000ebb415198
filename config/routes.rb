Rails.application.routes.draw do
  scope "(:locale)", locale: /#{I18n.available_locales.join("|")}/ do

    # Secure streaming endpoint - no file IDs in URL
    # This route is token-based and does not require a user session, so it's outside the authenticated block.
    get '/secure/stream', to: 'private_files#stream_content'
    
    # Webhook endpoint for Lambda thumbnail notifications
    # This route accepts external webhooks and has its own authentication via HMAC
    post '/wh/thumb_ready', to: 'webhooks/thumbnails#create'

    # Customize Devise Controllers:
    # To handle lost invitation registrations
    devise_for :users, controllers: { 
      confirmations: 'confirmations',
      registrations: 'registrations',
      invitations: 'users/invitations'
    }

    authenticated :user do
      
      get 'connection_requests/count', to: 'connection_requests#count'
      get 'user_profile/set_language', to: 'user_profiles#set_language', as: :set_user_language

      resources :user_profiles, only: [:show, :edit, :update]
      resources :projects do
        collection do
          get :show_my
        end
        member do
          get :show_users
          delete :destroy_file
          delete :bulk_delete_files
          delete :delete_access
          patch 'update_approval'
          post :request_file_token
        end
        resources :private_files, only: [] do
          member do
            get :show
            get :download
            get :secure_download
            get :stream_content
          end
        end
      end
      resources :wants do
        collection do
          get :show_my
        end
      end
      # File proxy routes for secure thumbnail and file serving
      get 'projects/:project_id/files/:file_id/thumbnail', to: 'file_proxy#thumbnail', as: 'file_thumbnail'
      get 'projects/:project_id/files/:file_id/download', to: 'file_proxy#download', as: 'file_download'
      get 'projects/:project_id/files/:file_id/inline', to: 'file_proxy#inline', as: 'file_inline'
      resources :network_connections, only: [:index, :destroy] do
        collection do
          get :my
          get :invite
        end
      end
      resources :connection_requests, only: [:index, :show, :new, :destroy] do
        member do
          get :user_profile
          post :accept_network_request
          post :reject_network_request
          post :accept_auth_request
          post :reject_auth_request
        end
        collection do
          post :create_network_request
          post :create_auth_request
        end
      end
      resources :invitations, only: [:index, :create]
      # resources :project_shares # not being used yet
      
      get 'admin_dashboard', to: 'admin_access#index'
      delete 'admin_access/projects/:id', to: 'admin_access#destroy_project', as: 'admin_destroy_project'
      
      # Admin subscription management routes
      namespace :admin do
        resources :subscriptions, only: [:index] do
          member do
            patch :update_tier
            patch :update_approval
          end
        end
        resources :referral_codes
      end
      
      # User referral code redemption
      post 'redeem_referral_code', to: 'referral_codes#redeem'
      get 'redeem_code', to: 'referral_codes#new', as: :new_referral_code_redemption
      
      # Server-side file upload routes (Ready Flag pattern)
      resources :uploads, only: [:create, :show, :destroy] do
        member do
          post :cleanup_stuck
        end
      end
      
      # Mount GoodJob dashboard (admin only)
      mount GoodJob::Engine => 'good_job', :constraints => lambda { |request|
        request.env['warden'].authenticate? && request.env['warden'].user.admin?
      }
      
      post 'resend_invitation/:id', to: 'network_connections#resend_invitation', as: 'resend_invitation'
      
      root 'projects#index', as: :authenticated_root
    end

    get '/home', to: redirect('https://unlisters.com'), as: :public_root
    root to: "welcome#index"
    
    # Catch-all for authenticated routes accessed by unauthenticated users
    # This handles cases like /sk/projects/12 when not logged in
    match '*path', to: 'application#require_authentication', via: :all, constraints: lambda { |req|
      path = req.path
      # Check if this looks like a protected resource path
      protected_patterns = %r{^/[a-z]{2}/(projects|wants|network_connections|user_profiles|connection_requests|invitations|admin_dashboard)}
      path.match?(protected_patterns)
    }

  end

  # get '/translations', to: 'translations#index'
  
  # Catch-all route for missing locale - redirect to default locale
  # This handles requests like /projects that should be /sk/projects
  get '/*path', to: 'application#redirect_to_locale', constraints: lambda { |req|
    # Only redirect if path doesn't already have a locale and isn't an asset
    path = req.path
    available_locales = I18n.available_locales.map(&:to_s)
    !path.start_with?('/assets') && 
    !path.start_with?('/rails') &&
    !path.match?(/\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$/i) &&
    !available_locales.any? { |locale| path.start_with?("/#{locale}/") || path == "/#{locale}" }
  }
  

  if Rails.env.development?
    get '/debug/heap_dump', to: 'debug#heap_dump'
    # Memory debugging routes
    get '/memory_debug/heap_dump', to: 'memory_debug#heap_dump'
    get '/memory_debug/stats', to: 'memory_debug#memory_stats'
    post '/memory_debug/force_gc', to: 'memory_debug#force_gc'
    post '/memory_debug/clear_cache', to: 'memory_debug#clear_query_cache'
    post '/memory_debug/test_projects_leak', to: 'memory_debug#test_projects_leak'
  end
end
