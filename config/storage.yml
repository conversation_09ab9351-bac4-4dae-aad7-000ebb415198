test:
  service: Disk
  root: <%= Rails.root.join("tmp/storage") %>

local:
  service: Disk
  root: <%= Rails.root.join("storage") %>

# Use bin/rails credentials:edit to set the AWS secrets (as aws:access_key_id|secret_access_key)
amazon_dev:
  service: S3
  access_key_id: <%= Rails.application.credentials.dig(:aws_dev, :access_key_id) %>
  secret_access_key: <%= Rails.application.credentials.dig(:aws_dev, :secret_access_key) %>
  region: <%= Rails.application.credentials.dig(:aws_dev, :region) %>
  bucket: <%= Rails.application.credentials.dig(:aws_dev, :bucket) %>

amazon_prod:
  service: S3
  access_key_id: <%= Rails.application.credentials.dig(:aws_prod, :access_key_id) %>
  secret_access_key: <%= Rails.application.credentials.dig(:aws_prod, :secret_access_key) %>
  region: <%= Rails.application.credentials.dig(:aws_prod, :region) %>
  bucket: <%= Rails.application.credentials.dig(:aws_prod, :bucket) %>

# Logical service configurations - used by models (environment resolution via ERB)
amazon_uploads:
<% if Rails.env.test? %>
  service: Disk
  root: <%= Rails.root.join("tmp/storage") %>
<% else %>
  service: S3
  access_key_id: <%= Rails.env.development? ? Rails.application.credentials.dig(:aws_dev, :access_key_id) : Rails.application.credentials.dig(:aws_prod, :access_key_id) %>
  secret_access_key: <%= Rails.env.development? ? Rails.application.credentials.dig(:aws_dev, :secret_access_key) : Rails.application.credentials.dig(:aws_prod, :secret_access_key) %>
  region: <%= Rails.env.development? ? Rails.application.credentials.dig(:aws_dev, :region) : Rails.application.credentials.dig(:aws_prod, :region) %>
  bucket: <%= Rails.env.development? ? 'app-sierra-a7b3c9e2-f8d1-4e56-9c2a-dev-uploads' : 'app-sierra-b1c5e9a3-d7f2-4a68-9b4e-prod-uploads' %>
<% end %>

amazon_thumbnails:
<% if Rails.env.test? %>
  service: Disk
  root: <%= Rails.root.join("tmp/storage") %>
<% else %>
  service: S3
  access_key_id: <%= Rails.env.development? ? Rails.application.credentials.dig(:aws_dev, :access_key_id) : Rails.application.credentials.dig(:aws_prod, :access_key_id) %>
  secret_access_key: <%= Rails.env.development? ? Rails.application.credentials.dig(:aws_dev, :secret_access_key) : Rails.application.credentials.dig(:aws_prod, :secret_access_key) %>
  region: <%= Rails.env.development? ? Rails.application.credentials.dig(:aws_dev, :region) : Rails.application.credentials.dig(:aws_prod, :region) %>
  bucket: <%= Rails.env.development? ? 'app-sierra-d4e8f2a6-c3b7-4d91-8e5a-dev-thumbnails' : 'app-sierra-e8a4c2f6-b9d3-4e71-a5c8-prod-thumbnails' %>
<% end %>

# Environment-specific service configurations (for migration and legacy support)
amazon_dev_uploads:
  service: S3
  access_key_id: <%= Rails.application.credentials.dig(:aws_dev, :access_key_id) %>
  secret_access_key: <%= Rails.application.credentials.dig(:aws_dev, :secret_access_key) %>
  region: <%= Rails.application.credentials.dig(:aws_dev, :region) %>
  bucket: app-sierra-a7b3c9e2-f8d1-4e56-9c2a-dev-uploads

amazon_dev_thumbnails:
  service: S3
  access_key_id: <%= Rails.application.credentials.dig(:aws_dev, :access_key_id) %>
  secret_access_key: <%= Rails.application.credentials.dig(:aws_dev, :secret_access_key) %>
  region: <%= Rails.application.credentials.dig(:aws_dev, :region) %>
  bucket: app-sierra-d4e8f2a6-c3b7-4d91-8e5a-dev-thumbnails

amazon_prod_uploads:
  service: S3
  access_key_id: <%= Rails.application.credentials.dig(:aws_prod, :access_key_id) %>
  secret_access_key: <%= Rails.application.credentials.dig(:aws_prod, :secret_access_key) %>
  region: <%= Rails.application.credentials.dig(:aws_prod, :region) %>
  bucket: app-sierra-b1c5e9a3-d7f2-4a68-9b4e-prod-uploads

amazon_prod_thumbnails:
  service: S3
  access_key_id: <%= Rails.application.credentials.dig(:aws_prod, :access_key_id) %>
  secret_access_key: <%= Rails.application.credentials.dig(:aws_prod, :secret_access_key) %>
  region: <%= Rails.application.credentials.dig(:aws_prod, :region) %>
  bucket: app-sierra-e8a4c2f6-b9d3-4e71-a5c8-prod-thumbnails

# Remember not to checkin your GCS keyfile to a repository
# google:
#   service: GCS
#   project: your_project
#   credentials: <%= Rails.root.join("path/to/gcs.keyfile") %>
#   bucket: your_own_bucket-<%= Rails.env %>

# Use bin/rails credentials:edit to set the Azure Storage secret (as azure_storage:storage_access_key)
# microsoft:
#   service: AzureStorage
#   storage_account_name: your_account_name
#   storage_access_key: <%= Rails.application.credentials.dig(:azure_storage, :storage_access_key) %>
#   container: your_container_name-<%= Rails.env %>

# mirror:
#   service: Mirror
#   primary: local
#   mirrors: [ amazon, google, microsoft ]
