#!/usr/bin/env ruby

# Manually attach the existing S3 thumbnail to test display
project = Project.find(66)
file = project.private_files.find(187)
file_hash = project.generate_secure_file_hash(file)

puts "Project: #{project.id}"
puts "File: #{file.filename}"
puts "File hash: #{file_hash}"

# Create the thumbnail filename that the webhook should have created
thumbnail_filename = "thumb_#{file_hash[0..8]}_#{file.blob.filename.base}.png"
puts "Expected thumbnail filename: #{thumbnail_filename}"

# Check if the S3 blob exists
s3_key = "gu0oc5wt0utc5c0r7ijnayo3k61m"
existing_blob = ActiveStorage::Blob.find_by(key: s3_key)

if existing_blob
  puts "Found existing blob: #{existing_blob.filename}"
else
  puts "Creating new blob for S3 key: #{s3_key}.png"
  
  # Create a new blob record for the existing S3 thumbnail
  thumbnail_blob = ActiveStorage::Blob.create!(
    key: s3_key,
    filename: thumbnail_filename,
    content_type: 'image/png',
    byte_size: 31700, # From the S3 info you provided
    checksum: nil # We don't have this, but it's optional for display
  )
  
  # Attach it to the project's pdf_thumbnails
  project.pdf_thumbnails.attach(thumbnail_blob)
  
  puts "Successfully attached thumbnail!"
  
  # Verify it can be found
  found_thumbnail = project.thumbnail_for_file(file)
  if found_thumbnail
    puts "✅ Thumbnail found via thumbnail_for_file method"
  else
    puts "❌ Thumbnail still not found - there may be an issue with the lookup logic"
  end
end