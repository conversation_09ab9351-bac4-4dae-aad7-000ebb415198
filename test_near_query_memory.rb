#!/usr/bin/env ruby
# Test memory usage of the .near() geocoder query
# Usage: ruby test_near_query_memory.rb

require_relative 'config/environment'
require 'objspace'

class NearQueryMemoryTester
  def initialize
    puts "🔍 Testing .near() Query Memory Usage"
    puts "=" * 50
  end
  
  def run_test
    # Ensure we have some test data
    setup_test_data
    
    puts "\n📊 Testing different query approaches..."
    
    # Test 1: Original approach (potentially problematic)
    test_original_near_query
    
    # Test 2: Limited dataset approach
    test_limited_near_query
    
    # Test 3: Simple coordinate filtering
    test_coordinate_filtering
    
    puts "\n" + "=" * 50
    puts "🎯 RECOMMENDATIONS"
    puts "=" * 50
  end
  
  private
  
  def setup_test_data
    puts "Setting up test data..."
    
    # Create a test user if needed
    @test_user = User.first || User.create!(
      email: '<EMAIL>',
      password: 'password123',
      invited_by: nil,
      confirmed_at: Time.current
    )
    
    unless @test_user.user_profile
      @test_user.create_user_profile!(
        first_name: 'Test',
        last_name: 'User',
        phone: '+421000000000'
      )
    end
    
    # Create some test projects with coordinates if we don't have enough
    project_count = Project.where.not(latitude: nil, longitude: nil).count
    puts "Found #{project_count} projects with coordinates"
    
    if project_count < 10
      puts "Creating additional test projects..."
      (10 - project_count).times do |i|
        Project.create!(
          user: @test_user,
          summary: "Test Project #{i}",
          location: "Prague, Czech Republic",
          latitude: 50.0755 + (rand(-10..10) * 0.01),
          longitude: 14.4378 + (rand(-10..10) * 0.01),
          project_type: :business,
          category: :business_acquisition,
          subcategory: :asset_purchase,
          project_status: true,
          approved: true
        )
      end
    end
    
    puts "Test data ready!"
  end
  
  def test_original_near_query
    puts "\n🧪 Test 1: Original .near() query"
    
    before_stats = get_memory_snapshot
    
    # This is what happens in the controller
    5.times do
      projects = Project.full_list_for_user(@test_user)
                       .includes(user: :user_profile)
                       .active
                       .approved
                       .near('Prague', 20, units: :km)
      projects.load  # Force execution
    end
    
    after_stats = get_memory_snapshot
    
    memory_growth = after_stats[:memory_mb] - before_stats[:memory_mb]
    object_growth = after_stats[:total_objects] - before_stats[:total_objects]
    
    puts "Memory growth: #{memory_growth.round(2)} MB"
    puts "Object growth: #{object_growth}"
    
    @original_memory_growth = memory_growth
  end
  
  def test_limited_near_query
    puts "\n🧪 Test 2: Limited dataset .near() query"
    
    before_stats = get_memory_snapshot
    
    # Limit the initial dataset before applying .near()
    5.times do
      # First get a rough bounding box
      center_lat, center_lon = 50.0755, 14.4378  # Prague coordinates
      lat_range = (center_lat - 0.5)..(center_lat + 0.5)
      lon_range = (center_lon - 0.5)..(center_lon + 0.5)
      
      projects = Project.full_list_for_user(@test_user)
                       .includes(user: :user_profile)
                       .active
                       .approved
                       .where(latitude: lat_range, longitude: lon_range)
                       .near('Prague', 20, units: :km)
      projects.load  # Force execution
    end
    
    after_stats = get_memory_snapshot
    
    memory_growth = after_stats[:memory_mb] - before_stats[:memory_mb]
    object_growth = after_stats[:total_objects] - before_stats[:total_objects]
    
    puts "Memory growth: #{memory_growth.round(2)} MB"
    puts "Object growth: #{object_growth}"
    
    @limited_memory_growth = memory_growth
  end
  
  def test_coordinate_filtering
    puts "\n🧪 Test 3: Pure coordinate filtering (no .near())"
    
    before_stats = get_memory_snapshot
    
    # Use only database-level filtering
    5.times do
      center_lat, center_lon = 50.0755, 14.4378  # Prague coordinates
      lat_range = (center_lat - 0.2)..(center_lat + 0.2)  # Roughly 20km
      lon_range = (center_lon - 0.3)..(center_lon + 0.3)  # Roughly 20km
      
      projects = Project.full_list_for_user(@test_user)
                       .includes(user: :user_profile)
                       .active
                       .approved
                       .where(latitude: lat_range, longitude: lon_range)
      projects.load  # Force execution
    end
    
    after_stats = get_memory_snapshot
    
    memory_growth = after_stats[:memory_mb] - before_stats[:memory_mb]
    object_growth = after_stats[:total_objects] - before_stats[:total_objects]
    
    puts "Memory growth: #{memory_growth.round(2)} MB"
    puts "Object growth: #{object_growth}"
    
    @coordinate_memory_growth = memory_growth
    
    # Show recommendations
    show_recommendations
  end
  
  def get_memory_snapshot
    GC.start
    {
      timestamp: Time.current.to_f,
      memory_mb: `ps -o rss= -p #{Process.pid}`.to_i / 1024.0,
      total_objects: ObjectSpace.count_objects[:TOTAL],
      gc_count: GC.count
    }
  end
  
  def show_recommendations
    puts "\n📋 ANALYSIS:"
    puts "Original .near() query: #{@original_memory_growth.round(2)} MB"
    puts "Limited dataset: #{@limited_memory_growth.round(2)} MB"
    puts "Coordinate filtering only: #{@coordinate_memory_growth.round(2)} MB"
    
    if @original_memory_growth > 5
      puts "\n🚨 HIGH MEMORY USAGE DETECTED in original query!"
      puts "The .near() scope is likely loading too much data into memory."
    elsif @original_memory_growth > 2
      puts "\n⚠️  MODERATE MEMORY USAGE in original query"
    else
      puts "\n✅ Memory usage appears normal"
    end
    
    puts "\n🔧 RECOMMENDED SOLUTIONS:"
    
    if @limited_memory_growth < @original_memory_growth * 0.5
      puts "1. ✅ Use bounding box pre-filtering (reduces memory by #{((@original_memory_growth - @limited_memory_growth) / @original_memory_growth * 100).round(1)}%)"
    end
    
    if @coordinate_memory_growth < @original_memory_growth * 0.3
      puts "2. ✅ Consider replacing .near() with coordinate ranges for better performance"
    end
    
    puts "3. 🔄 Add result caching for location searches"
    puts "4. 📊 Consider using PostGIS for true spatial queries"
  end
end

# Run the test
if __FILE__ == $0
  tester = NearQueryMemoryTester.new
  tester.run_test
end