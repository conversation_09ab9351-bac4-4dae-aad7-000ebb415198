#!/usr/bin/env ruby
require_relative 'config/environment'

puts "Looking for the image file mentioned in logs: 4dd590449fa2cb7dcafb981a836120eb.jpg"

# Search all blobs for this key
matching_blobs = ActiveStorage::Blob.where('key LIKE ?', '%4dd590449fa2cb7dcafb981a836120eb%')
puts "Found #{matching_blobs.count} matching blobs"

matching_blobs.each do |blob|
  puts "Blob ID: #{blob.id}, Key: #{blob.key}, Filename: #{blob.filename}, Service: #{blob.service_name}"
  
  # Find what it's attached to
  blob.attachments.each do |attachment|
    puts "  Attached to: #{attachment.record_type} ##{attachment.record_id} as #{attachment.name}"
  end
end

puts ""
puts "Looking for corresponding thumbnail blob..."
thumbnail_blobs = ActiveStorage::Blob.where('key LIKE ?', '%4dd590449fa2cb7dcafb981a836120eb%').where(service_name: 'amazon_thumbnails')
puts "Found #{thumbnail_blobs.count} thumbnail blobs"

thumbnail_blobs.each do |blob|
  puts "Thumbnail blob ID: #{blob.id}, Key: #{blob.key}, Filename: #{blob.filename}"
  
  # Find what it's attached to
  blob.attachments.each do |attachment|
    puts "  Attached to: #{attachment.record_type} ##{attachment.record_id} as #{attachment.name}"
  end
end

puts ""
puts "Also checking for .png version in any service..."
png_blobs = ActiveStorage::Blob.where('key LIKE ? OR filename LIKE ?', '%4dd590449fa2cb7dcafb981a836120eb.png%', '%4dd590449fa2cb7dcafb981a836120eb.png%')
puts "Found #{png_blobs.count} PNG blobs"

png_blobs.each do |blob|
  puts "PNG blob ID: #{blob.id}, Key: #{blob.key}, Filename: #{blob.filename}, Service: #{blob.service_name}"
  
  # Find what it's attached to
  blob.attachments.each do |attachment|
    puts "  Attached to: #{attachment.record_type} ##{attachment.record_id} as #{attachment.name}"
  end
end