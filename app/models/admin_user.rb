class AdminUser < ApplicationRecord
  # Include default devise modules. Others available are:
  # :confirmable, :lockable, :timeoutable, :trackable and :omniauthable


  # Legacy. Not dropped tables yet after removing Active Admin gem and respective files
  devise :database_authenticatable, 
         :recoverable, :rememberable, :validatable


  # Link AdminUser with regular User
  def user
    User.find_by(email: email)
  end

  def super_admin?
    user&.super_admin?
  end

end
