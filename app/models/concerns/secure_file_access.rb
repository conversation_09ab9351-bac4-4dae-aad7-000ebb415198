# SecureFileAccess Concern
#
# This concern provides secure file access methods for models that have private file attachments.
# It implements cryptographic hashing for file identification without exposing file IDs,
# preventing enumeration attacks and maintaining zero metadata exposure.
#
# Security Features:
# - SHA256 cryptographic hashing for file identification
# - Uses Rails secret key base for additional security
# - Truncated hash (32 chars) for URL safety while maintaining security
# - No file metadata or structure exposed to clients
# - Secure file lookup by hash instead of sequential IDs
#
# Usage:
#   class Project < ApplicationRecord
#     include SecureFileAccess
#     has_many_attached :private_files
#   end
#
#   # Generate secure hash for file
#   hash = project.generate_secure_file_hash(file_attachment)
#
#   # Find file by secure hash
#   file = project.find_file_by_secure_hash(hash)

module SecureFileAccess
  extend ActiveSupport::Concern
  
  # Generate a cryptographically secure hash for file identification
  #
  # This method creates a unique, non-enumerable identifier for files that:
  # - Cannot be guessed or enumerated by attackers
  # - Does not expose file IDs or metadata
  # - Is cryptographically secure using SHA256
  # - Includes project ID, file ID, and Rails secret for uniqueness
  #
  # @param file_attachment [ActiveStorage::Attachment] The file to hash
  # @return [String] 32-character cryptographic hash
  def generate_secure_file_hash(file_attachment)
    raise ArgumentError, 'File attachment cannot be nil' unless file_attachment
    
    # Create unique string combining project and file IDs
    data_to_hash = "#{id}-#{file_attachment.id}"
    
    # Use HMAC-SHA256 for a more secure, standard-based hash
    # Keyed hash provides better protection than simple digest
    hash = OpenSSL::HMAC.hexdigest(
      'sha256',
      FILE_HASH_SALT,
      data_to_hash
    )

    # Truncate to 32 characters for URL safety and consistency
    # 128 bits of entropy is sufficient for this use case
    hash[0..31]
  end
  
  # Find a file attachment by its secure hash
  #
  # This method safely looks up files without exposing file structure:
  # - Iterates through all attached files (small dataset, acceptable performance)
  # - Compares cryptographic hashes (constant time comparison)
  # - Returns nil if no match found (no enumeration information leaked)
  # - Includes performance safeguards against DoS attacks
  #
  # @param hash [String] The secure hash to search for
  # @return [ActiveStorage::Attachment, nil] The matching file or nil
  def find_file_by_secure_hash(hash)
    return nil unless hash.present?
    return nil unless private_files.attached?
    
    # PERFORMANCE OPTIMIZATION: Cache file count to avoid repeated queries
    file_count = private_files.count
    
    # SCALABILITY FIX: Enhanced DoS protection with performance monitoring
    # TODO: For true scalability, this should be replaced with an indexed database lookup
    # by adding a `secure_hash` column to active_storage_attachments table
    max_files_for_linear_search = 100
    
    if file_count > max_files_for_linear_search
      Rails.logger.warn "[SECURE_FILE] DoS Protection: Aborting search due to large file count (#{file_count}) for project #{id}"
      Rails.logger.warn "[SECURE_FILE] RECOMMENDATION: Implement indexed secure_hash column for O(1) lookups"
      
      # Graceful degradation - return nil to prevent resource exhaustion
      # This maintains security while protecting server performance
      return nil
    end
    
    # Performance monitoring with more granular thresholds
    case file_count
    when 25..49
      Rails.logger.info "[SECURE_FILE] Performance: Moderate file count (#{file_count}) for project #{id}"
    when 50..99
      Rails.logger.warn "[SECURE_FILE] Performance: High file count (#{file_count}) approaching limit for project #{id}"
    end
    
    # Measure search performance for optimization insights
    start_time = Time.current
    
    result = private_files.find do |file|
      # Use secure comparison to prevent timing attacks
      ActiveSupport::SecurityUtils.secure_compare(
        generate_secure_file_hash(file),
        hash.to_s
      )
    end
    
    # Log slow searches for monitoring
    search_duration = Time.current - start_time
    if search_duration > 0.1 # 100ms threshold
      Rails.logger.warn "[SECURE_FILE] Performance: Slow file search (#{search_duration.round(3)}s) for #{file_count} files in project #{id}"
    end
    
    result
  end
  
  # Get all secure file hashes for this record
  # Useful for frontend initialization and debugging
  # SECURITY: Limited metadata exposure to prevent information leakage
  #
  # @param expose_filenames [Boolean] Whether to include actual filenames (default: false)
  # @return [Array<Hash>] Array of file info with secure hashes
  def secure_file_hashes(expose_filenames: false)
    return [] unless private_files.attached?
    
    private_files.map do |file|
      {
        secure_hash: generate_secure_file_hash(file),
        content_type: file.content_type,
        # SECURITY: Only expose filenames when explicitly requested
        # This prevents accidental information leakage through sensitive filenames
        display_name: expose_filenames ? file.filename.to_s : "File.#{file.filename.extension}",
        byte_size: file.byte_size,
        created_at: file.created_at
      }
    end
  end
  
  # Validate that a secure hash belongs to this record
  # Additional security check for authorization flows
  #
  # @param hash [String] The hash to validate
  # @return [Boolean] True if hash belongs to this record
  def owns_secure_file_hash?(hash)
    find_file_by_secure_hash(hash).present?
  end
end