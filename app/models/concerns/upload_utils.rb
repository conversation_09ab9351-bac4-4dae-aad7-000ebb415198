# ABOUTME: Utility module providing helper methods for file uploads
# ABOUTME: Includes content type categorization and other upload-related utilities

module UploadUtils
  extend ActiveSupport::Concern
  
  class << self
    # Categorize content type for S3 organization
    def content_type_category(content_type)
      case content_type
      when /^image\//
        'images'
      when /^video\//
        'videos'
      when /^audio\//
        'audio'
      when /^application\/pdf$/
        'documents'
      when /^application\/(msword|vnd\.openxmlformats|vnd\.ms-)/
        'documents'
      when /^text\//
        'documents'
      else
        'misc'
      end
    end
    
    # Check if content type is an image
    def image?(content_type)
      content_type.to_s.start_with?('image/')
    end
    
    # Check if content type is a PDF
    def pdf?(content_type)
      content_type == 'application/pdf'
    end
    
    # Check if content type is a video
    def video?(content_type)
      content_type.to_s.start_with?('video/')
    end
    
    # Generate a secure random filename preserving extension
    def generate_secure_filename(original_filename)
      extension = File.extname(original_filename)
      base_name = SecureRandom.hex(16)
      "#{base_name}#{extension}"
    end
    
    # Check if file size is valid
    def file_size_valid?(size)
      size > 0 && size <= max_upload_size
    end
    
    # Maximum upload size (defaults to 100MB)
    def max_upload_size
      Rails.application.config.max_upload_size || 100.megabytes
    end
    
    # Check if content type is allowed
    def allowed_content_type?(content_type)
      allowed_types.include?(content_type)
    end
    
    # List of allowed content types
    def allowed_types
      [
        # Images
        'image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml',
        # Documents
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-powerpoint',
        'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        # Text
        'text/plain', 'text/csv',
        # Archives
        'application/zip', 'application/x-zip-compressed',
        # Videos
        'video/mp4', 'video/quicktime', 'video/x-msvideo'
      ]
    end
    
    # Generate temp filename for upload
    def generate_temp_filename(original_filename)
      timestamp = Time.current.strftime('%Y%m%d%H%M%S')
      random = SecureRandom.hex(8)
      extension = File.extname(original_filename)
      "#{timestamp}_#{random}#{extension}"
    end
    
    # Get temp file path
    def temp_file_path(filename)
      File.join(upload_temp_path, filename)
    end
    
    # Get upload temp path from config or default
    def upload_temp_path
      ENV.fetch('RENDER_DISK_UPLOAD_PATH', Rails.root.join('tmp', 'uploads').to_s)
    end
  end
end