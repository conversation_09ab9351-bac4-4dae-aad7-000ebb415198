class User < ApplicationRecord

  #attr_accessor :skip_inviter_profile_check
  
  # Include default devise modules. Others available are:
  # :confirmable, :lockable, :timeoutable, :trackable and :omniauthable
  devise :invitable, :database_authenticatable, :registerable, :recoverable, :rememberable, :validatable, :confirmable, :lockable, :timeoutable, :trackable

  enum role: { regular: 0, super_boss: 2 }  
  enum subscription_tier: { free: 0, premium: 1, beta: 2 }, _prefix: :tier  

  # Changed to be reciprocal, bidirectional connection in related methods
  has_many :network_connections, foreign_key: :inviter_id, dependent: :destroy
  has_many :connected_users, through: :network_connections, source: :invitee
  
  has_many :inverse_network_connections, class_name: 'NetworkConnection', foreign_key: :invitee_id, dependent: :destroy
  
  has_many :connection_requests, foreign_key: :inviter_id, dependent: :destroy
  has_many :inverse_connection_requests, class_name: 'ConnectionRequest', foreign_key: :invitee_id, dependent: :destroy
  
  has_many :projects, dependent: :destroy
  has_many :wants, dependent: :destroy
  has_many :project_auths, dependent: :destroy
  has_many :accessible_projects, through: :project_auths, source: :project
  has_many :uploads, dependent: :destroy

  has_one :user_profile, dependent: :destroy

  #validate :inviter_has_profile_name, on: :invitation_created
  validate :validate_invitation_if_required

  after_create :create_user_profile
  after_invitation_accepted :create_network_connection

  scope :active, -> { where.not(invitation_accepted_at: nil).where.not(confirmed_at: nil).where(locked_at: nil) }
  scope :approved, -> { where(approved: true) }


  
  def network_connections
    NetworkConnection.where('inviter_id = ? OR invitee_id = ?', id, id)
  end

  def super_admin?
    role == "super_boss"
  end

  def admin?
    role == "super_boss"
  end

  def connected_users_bidirectional
    # Use existing NetworkConnection.for_user scope and Rails associations
    # Gets users from both sides of bidirectional connections
    User.where(
      id: NetworkConnection.where(inviter_id: id).select(:invitee_id)
    ).or(
      User.where(
        id: NetworkConnection.where(invitee_id: id).select(:inviter_id)
      )
    )
  end

  # Subscription tier methods
  def active_subscription?
    return true if tier_beta? # Beta never expires
    return false if tier_free?
    tier_premium? && subscription_expires_at.present? && subscription_expires_at > Time.current
  end

  def subscription_expired?
    # An expired subscription is one where a premium user's subscription has expired
    # Free users don't have "expired" subscriptions, they just don't have active ones
    tier_premium? && !active_subscription?
  end

  def generate_referral_code!
    loop do
      self.referral_code = SecureRandom.alphanumeric(8).upcase
      break unless User.exists?(referral_code: referral_code)
    end
    save!
  end

  # Approval system methods
  def approve!
    transaction do
      update!(approved: true)
      
      # If user is not yet invitation accepted, simulate invitation acceptance
      if invitation_accepted_at.nil?
        update!(invitation_accepted_at: Time.current)
        # Trigger network connection creation if needed
        create_network_connection if invited_by_id.present?
      end
      
      # Send approval notification email if needed
      # UserMailer.approval_notification(self).deliver_later
    end
  end

  def disapprove!
    update!(approved: false)
    # Send disapproval notification email if needed
    # UserMailer.disapproval_notification(self).deliver_later
  end

  # Override Devise method to allow all users to login (approved and unapproved)
  def active_for_authentication?
    super
  end

  # Custom message for unapproved users
  def inactive_message
    super
  end

  
  private

  def validate_invitation_if_required
    if ENV.fetch('INVITE_ONLY', 'false') == 'true' && 
       new_record? && 
       !invitation_token.present?
      errors.add(:base, 'Registration requires an invitation')
    end
  end

  # invited_by is a method provided by devise_invitable that returns the User who sent the invitation
  # invited_by is the User who originally sent the invitation
  # self is the User accepting the invitation
  # also checking if the User existis in the database already
  def create_network_connection
    inviter = User.find(invited_by_id)
    existing_connection = NetworkConnection.where(
      '(inviter_id = :user1 AND invitee_id = :user2) OR (inviter_id = :user2 AND invitee_id = :user1)',
      user1: inviter.id,
      user2: self.id
    ).first

    if existing_connection.nil?
      NetworkConnection.create(
        inviter_id: inviter.id,
        invitee_id: self.id
      )
    end

  end

  def create_user_profile
    self.build_user_profile(email: self.email, first_name: self.email.split('@').first, last_name: '', location: '').save
  end

  def inviter_has_profile_name
    if invited_by.present? && (!invited_by.user_profile&.first_name.present? || !invited_by.user_profile&.last_name.present?)
      errors.add(:base, 'You must have a name in your profile before inviting anyone.')
    end
  end

  
end
