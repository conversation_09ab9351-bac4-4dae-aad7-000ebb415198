class ProjectAuth < ApplicationRecord
  belongs_to :project
  belongs_to :user

  validates :user_id, uniqueness: { scope: :project_id }
  validates :access_level, presence: true

  # TODO: level of the access is not being used yet
  # Default is set to pending - on creation of request.
  # Owner of the Project can set no_access (rejects forever)
  # Owner can reject now - delete the record
  # Summary has no meaning yet
  # Full access is the highest access available
  enum access_level: { denied: 0, pending: 1, limited: 2, full_details: 3 }

  #scope approved, -> { where(access_level: :full_details) }

  def self.for_projects_of_user(user)
    joins(:project).where(projects: { user_id: user.id })
  end
  
  def approved_full?
    self.access_level == 'full_details'
  end

  def self.exists_for?(user, project)
    exists?(user: user, project: project)
  end

  # Old request access to a project
  # def self.request_access(project, user)
  #   create(project: project, user: user, access_level: :pending)
  # end
  
  def approve_full!
    update(access_level: :full_details)
  end

  def approve_limited!
    update(access_level: :limited)
  end

  def reject!
    update(access_level: :denied)
  end

end
