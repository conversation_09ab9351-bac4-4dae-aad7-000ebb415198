class NetworkConnection < ApplicationRecord

  # Despite the names, it is a bidirectional connection
  belongs_to :inviter, class_name: 'User', counter_cache: :connections_count
  belongs_to :invitee, class_name: 'User', counter_cache: :connections_count

  validate :unique_connection, on: :create
  validate :cannot_invite_self, on: :create
  #validate :inviter_has_profile_name, on: :create, unless: -> { inviter.skip_inviter_profile_check }
  
  scope :for_user, ->(user_id) {
    where(inviter_id: user_id).or(where(invitee_id: user_id))
  }

  # scope :with_user_profiles, ->(user_id) {
  #   joins("LEFT JOIN user_profiles ON user_profiles.user_id = network_connections.inviter_id OR user_profiles.user_id = network_connections.invitee_id")
  #     .where("network_connections.inviter_id = :user_id OR network_connections.invitee_id = :user_id", user_id: user_id)
  #     .select("network_connections.*, user_profiles.first_name, user_profiles.last_name")
  # }

  scope :with_user_profiles, ->(user_id) {
    includes(inviter: :user_profile, invitee: :user_profile)
      .where("network_connections.inviter_id = :user_id OR network_connections.invitee_id = :user_id", user_id: user_id)
  }

  # def self.with_connection_status(current_user_id)
  #   select('user_profiles.first_name, user_profiles.last_name, user_profiles.location, 
  #           CASE 
  #             WHEN network_connections.inviter_id = :current_user_id OR network_connections.invitee_id = :current_user_id 
  #             THEN true 
  #             ELSE false 
  #           END AS connected')
  #   .joins('LEFT JOIN network_connections ON user_profiles.user_id = network_connections.inviter_id OR user_profiles.user_id = network_connections.invitee_id')
  #   .group('user_profiles.id, network_connections.inviter_id, network_connections.invitee_id')
  #   .order('user_profiles.first_name, user_profiles.last_name')
  #   .distinct
  # end
  def connected_to?(user_id)
    inviter_id == user_id || invitee_id == user_id
  end
  
  def belongs_to_user?(user_id)
    inviter_id == user_id || invitee_id == user_id
  end

  def connected_user(current_user_id)
    inviter_id == current_user_id ? invitee : inviter
  end

  private
  
  def unique_connection
    if NetworkConnection.exists?(inviter_id: inviter_id, invitee_id: invitee_id) ||
      NetworkConnection.exists?(inviter_id: invitee_id, invitee_id: inviter_id)
      errors.add(:base, 'A connection already exists.')
    end
  end

  def cannot_invite_self
    if inviter_id == invitee_id
      errors.add(:base, 'You cannot invite yourself.')
    end
  end

  def inviter_has_profile_name
    unless inviter.user_profile&.first_name.present? && inviter.user_profile&.last_name.present?
      errors.add(:base, 'You must have a name in your profile before inviting anyone.')
    end
  end
  
end
