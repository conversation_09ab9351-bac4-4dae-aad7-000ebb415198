class Upload < ApplicationRecord
  # Custom exception for invalid state transitions
  class InvalidStateTransition < StandardError; end
  
  # Associations
  belongs_to :user
  belongs_to :target, polymorphic: true, optional: true
  
  # Callbacks (must come before validations that depend on them)
  before_validation :generate_signed_id_token, on: :create
  after_update :broadcast_status_if_changed
  
  # Validations
  validates :original_filename, presence: true
  validates :content_type, presence: true
  validates :file_size, presence: true, numericality: { greater_than: 0 }
  validates :signed_id_token, presence: true, uniqueness: true
  validates :status, presence: true
  validates :progress_percentage, 
            numericality: { greater_than_or_equal_to: 0, less_than_or_equal_to: 100 }
  
  # Enhanced state machine with improved feedback and race condition handling
  enum status: {
    pending: 'pending',          # Initial state - file upload in progress to temp storage
    transferred: 'transferred',  # File fully saved to temp storage, ready for background processing
    processing: 'processing',    # Background job has verified file and started S3 upload
    completed: 'completed',      # Successfully uploaded to S3 and attached
    failed: 'failed',           # Upload failed at any stage
    cancelled: 'cancelled',     # User cancelled the upload
    aborted: 'aborted'          # System aborted stuck upload after timeout
  }
  
  # Scopes for different upload states
  scope :processable, -> { where(status: :transferred) }
  scope :active, -> { where(status: [:pending, :transferred, :processing]) }
  scope :finished, -> { where(status: [:completed, :failed, :cancelled, :aborted]) }
  
  # Enhanced state transition methods
  def mark_transferred!
    raise InvalidStateTransition, "Cannot mark transferred from #{status}" unless pending?
    update!(status: :transferred)
  end
  
  def can_process?
    transferred?
  end
  
  def mark_processing!
    raise InvalidStateTransition, "Cannot mark processing from #{status}" unless transferred?
    update!(status: :processing, progress_percentage: 0)
  end
  
  def mark_completed!(s3_key = nil)
    raise InvalidStateTransition, "Cannot mark completed from #{status}" unless processing?
    update!(
      status: :completed, 
      progress_percentage: 100,
      s3_key: s3_key || self.s3_key
    )
  end
  
  def mark_failed!(error_message)
    update!(
      status: :failed,
      error_message: error_message
    )
  end
  
  def mark_cancelled!
    raise InvalidStateTransition, "Cannot cancel from #{status} state" unless can_cancel?
    update!(status: :cancelled)
  end
  
  # Progress tracking
  def update_progress!(percentage)
    self.progress_percentage = percentage
    save!
    broadcast_status
  end
  
  def calculate_progress
    progress_percentage
  end
  
  # File management
  def temp_file_exists?
    temp_file_path.present? && File.exist?(temp_file_path)
  end
  
  def cleanup_temp_file!
    return unless temp_file_path.present?
    
    begin
      File.delete(temp_file_path) if File.exist?(temp_file_path)
      update_column(:temp_file_path, nil)
    rescue => e
      Rails.logger.error "Failed to cleanup temp file #{temp_file_path}: #{e.message}"
    end
  end
  
  # Secure signed ID for Action Cable channels
  def generate_secure_signed_id
    signed_id(purpose: :upload_progress, expires_in: 24.hours)
  end
  
  # Action Cable broadcasting
  def broadcast_status
    payload = {
      id: id,
      status: status,
      progress: progress_percentage,
      original_filename: original_filename,
      file_size: file_size
    }
    
    # Include error message for failed uploads
    payload[:error_message] = error_message if failed? && error_message.present?
    
    # Include S3 key for completed uploads
    payload[:s3_key] = s3_key if completed? && s3_key.present?
    
    UploadChannel.broadcast_to(self, payload)
  end
  
  # Utility methods
  def human_file_size
    ActiveSupport::NumberHelper.number_to_human_size(file_size)
  end
  
  def file_extension
    File.extname(original_filename).downcase
  end
  
  def image?
    content_type.start_with?('image/')
  end
  
  def can_cancel?
    pending? || transferred?
  end
  
  # Detect uploads stuck in intermediate states for too long
  def stuck?
    (transferred? || processing?) && updated_at < 30.minutes.ago
  end
  
  # Manual cleanup for stuck uploads (user-initiated only)
  def cleanup_stuck_upload!
    return false unless stuck?
    
    Rails.logger.info "Cleaning up stuck upload #{id} (#{original_filename}) - stuck since #{updated_at}"
    
    # 1. Update database state (primary goal)
    update!(status: :aborted)
    
    # 2. Clean temp file (secondary goal)
    cleanup_temp_file! if temp_file_exists?
    
    Rails.logger.info "Successfully cleaned up stuck upload #{id}"
    true
  end
  
  def processing_time
    return nil unless completed? || failed?
    return nil unless transferred_at = status_transitions['transferred']
    
    completed_at = status_transitions['completed'] || status_transitions['failed'] || updated_at
    (completed_at - transferred_at).seconds
  end
  
  private
  
  def generate_signed_id_token
    return if signed_id_token.present?
    self.signed_id_token = SecureRandom.urlsafe_base64(32)
  end
  
  def broadcast_status_if_changed
    # Only auto-broadcast on status changes, not progress changes
    # Progress changes use manual broadcast_status calls
    broadcast_status if saved_change_to_status?
  end
  
  # Track status change timestamps for performance analysis
  def status_transitions
    @status_transitions ||= begin
      changes = {}
      changes['pending'] = created_at
      changes['transferred'] = updated_at if transferred? || processing? || completed? || failed?
      changes['processing'] = updated_at if processing? || completed? || failed?
      changes['completed'] = updated_at if completed?
      changes['failed'] = updated_at if failed?
      changes
    end
  end

  private

end