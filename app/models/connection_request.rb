class ConnectionRequest < ApplicationRecord
  belongs_to :inviter, class_name: 'User' 
  belongs_to :invitee, class_name: 'User' 
  belongs_to :project, optional: true

  enum status: { pending: 0, approved: 1, declined: 2 }

  before_create :set_default_status
  validate :connection_or_projectauth_exists, on: :create
  validate :no_pending_inviter_invitee, on: :create, if: -> { !project.present? }
  validate :no_pending_inviter_project, on: :create, if: -> { project.present? }

  
  def approved!
    update!(status: :approved)
  end
  
  def reject!
    update!(status: :declined)
  end



  private
  
  def set_default_status
    self.status ||= :pending
  end

  def connection_or_projectauth_exists
    if !project && NetworkConnection.exists?(inviter: inviter, invitee: invitee)
      errors.add(:base, "Requested network connection exists.")
    elsif ProjectAuth.exists?(user: inviter, project: project)
      errors.add(:base, "You should already have that project permission.")
    end
  end

  # This checks if there is pending request between the users
  # But should not check project auth connection request because project could be semi+public - and this handles action policy for project auth request
  def no_pending_inviter_invitee
    if ConnectionRequest.exists?(inviter: inviter, invitee: invitee, status: :pending) ||
       ConnectionRequest.exists?(inviter: invitee, invitee: inviter, status: :pending)
      puts " \n-----no_pending_inviter_invitee:   ----#{ConnectionRequest.where(inviter: invitee, invitee: inviter, status: :pending).inspect}--------- \n\n " 
      puts " \n-----no_pending_inviter_invitee:   ----#{ConnectionRequest.where(inviter: inviter, invitee: invitee, status: :pending).inspect}--------- \n\n "
      errors.add(:base, "There's already a pending request.")
    end
  end

  def no_pending_inviter_project
    if ConnectionRequest.exists?(inviter: inviter, project: project, status: :pending)
      puts " \n-----no_pending_inviter_project:   ----#{ConnectionRequest.where(inviter: inviter, project: project, status: :pending).inspect}--------- \n\n "
      errors.add(:base, "There's already a pending request between this user and the project.")
    end
  end

end