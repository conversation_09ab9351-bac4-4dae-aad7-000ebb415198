//Connections
/* Main connections container */
@import "variables";
.connections-container {
  width: 100%;
  padding: 1rem;
}

/* Search section */
.connections-search {
  width: 100%;
  margin-top: 1.25rem;
}

.search-form {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.search-input {
  padding: 0.5rem;
  border: 1px solid #e2e8f0;
  border-radius: 0.375rem;
  width: 200px;
}

.search-button {
  padding: 12px 24px;
  background-color: $blue;
  color: white;
  border-radius: 0.375rem;
  border: none;
  cursor: pointer;
  margin-top: 0;
}

/* User list styles */
.connections-list {
  width: 100%;
  margin-top: 1rem;
}

.connection-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 5rem;
  padding: 0.5rem 0;
  border-bottom: 1px solid #e5e7eb;
}

.connection-name {
  font-weight: 500;
}

.connection-actions {
  display: flex;
  align-items: center;
}

/* Status badges */
.status-badge {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: $small-font-size;
}

.status-badge--self {
  background-color: #e5e7eb;
  color: #374151;
}

.status-badge--connected {
  background-color: $primary-color;
  color: white;
}

/* Profile completion warning */
.profile-warning {
  color: $red;
  background-color: #fee2e2;
  padding: 1rem;
  border-radius: 0.375rem;
  margin-bottom: 1rem;
}

/* Action buttons */
.profile-edit-button {
  display: inline-block;
  padding: 0.5rem 1rem;
  background-color: $blue;
  color: white;
  text-decoration: none;
  border-radius: 0.375rem;
}

.connect-button {
  background: none;
  font-size: $base-font-size;
  border: none;
  color: $blue;
  font-weight: 600;
  cursor: pointer;
  padding: 0;
}

.connect-button:hover {
  text-decoration: underline;
}

/* Empty state */
.empty-state {
  text-align: center;
  padding: 2rem 0;
}
.connection-item {
  display: grid;
  grid-template-columns: 2fr 3fr 2fr;
  gap: 1.5rem;
  padding: 1rem;
  border-bottom: 1px solid #e5e7eb;
  align-items: start;
}

.connection-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.connection-bio {
  color: #4b5563;
  font-size: $small-font-size;
  line-height: 1.25rem;
  padding: 0 1rem;
  //border-left: 1px solid #e5e7eb;
  // border-right: 1px solid #e5e7eb;
}

.connection-actions {
  display: flex;
  justify-content: flex-end;
  align-items: flex-start;
}

.connection-name {
  font-weight: 600;
  font-size: 1.125rem;
  color: #1f2937;
}

.connection-location {
  font-size: $small-font-size;
  margin-top: 0.25rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.search-filters {
  display: flex;
  gap: 1rem;
}

.filter-select {
  padding: 0.375rem;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  color: #4b5563;
  font-size: $small-font-size;
}

.icon {
  width: 1rem;
  height: 1rem;
  color: $dark-gray;
}