// ABOUTME: Upload system styles for server-side file uploads with progress tracking
// ABOUTME: Includes styles for drop zones, progress indicators, and upload management UI
@import "variables";

// Upload Drop Zone

.upload-drop-zone {
  border: 2px dashed #d1d5db;
  border-radius: 0.5rem;
  padding: 2rem;
  text-align: center;
  background-color: #f9fafb;
  transition: all 0.3s ease;
  cursor: pointer;
  
  &:hover {
    border-color: $primary-color;
    background-color: #f0f9ff;
  }
  
  &.drag-over {
    border-color: $primary-color;
    background-color: #e0f2fe;
    transform: scale(1.02);
  }
  
  .upload-drop-text {
    margin-bottom: 1rem;
    color: #6b7280;
    
    .primary-text {
      color: $text-color;
      font-weight: 500;
    }

    .secondary-text {
      font-size: $small-font-size;
      margin-top: 0.5rem;
    }
  }
  
  input[type="file"] {
    opacity: 0;
    position: absolute;
    pointer-events: none;
  }
}

// Upload Progress Container
.upload-progress-container {
  margin: 1rem 0;
  border: 1px solid $border-color;
  border-radius: 0.5rem;
  background-color: white;
  
  .upload-container-header {
    padding: 1rem;
    border-bottom: 1px solid $border-color;
    background-color: #f8f9fa;
    
    h4 {
      margin: 0;
      color: $text-color;
      font-weight: 500;
    }
  }
}

// Individual Upload Item
.upload-item {
  display: flex;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #f1f5f9;
  gap: 1rem;
  
  &:last-child {
    border-bottom: none;
  }
  
  &.upload-pending {
    background-color: #fefce8;
  }
  
  &.upload-ready {
    background-color: #eff6ff;
  }
  
  &.upload-uploading {
    background-color: #f0f9ff;
  }
  
  &.upload-completed {
    background-color: #f0fdf4;
  }
  
  &.upload-failed {
    background-color: #fef2f2;
  }
  
  &.upload-cancelled {
    background-color: #f8fafc;
    opacity: 0.7;
  }
}

// Upload Info Section
.upload-info {
  flex: 1;
  min-width: 0;
  
  .upload-filename {
    font-weight: 500;
    color: $text-color;
    margin-bottom: 0.25rem;
    overflow-wrap: break-word;
    word-break: break-word; 
  }
  
  .upload-filesize {
    font-size: $small-font-size;
    color: #6b7280;
  }
}

// Upload Progress Section
.upload-progress {
  flex: 1;
  max-width: 200px;
  margin: 0 1rem;
  
  .upload-progress-track {
    width: 100%;
    height: 8px;
    background-color: #e5e7eb;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 0.5rem;
  }
  
  .upload-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, $primary-color 0%, #10b981 100%);
    transition: width 0.3s ease;
    border-radius: 4px;
  }
  
  .upload-progress-text {
    font-size: $small-font-size;
    color: #6b7280;
    text-align: center;
    font-weight: 500;
  }
}

// Multi-Phase Progress Indicator
.upload-phases {
  display: flex;
  justify-content: space-between;
  margin: 10px 0;
  flex: 1;
  max-width: 280px;
  
  .phase {
    display: flex;
    flex-direction: column;
    align-items: center;
    opacity: 0.3;
    transition: opacity 0.3s ease;
    position: relative;
    
    &.active {
      opacity: 1;
    }
    
    .phase-icon {
      font-size: 20px;
      margin-bottom: 4px;
    }
    
    .phase-text {
      font-size: 12px;
      color: #666;
      font-weight: 500;
    }
    
    // Progress line between phases
    &:not(:last-child)::after {
      content: '';
      position: absolute;
      top: 10px;
      left: 100%;
      width: calc(100% + 10px);
      height: 2px;
      background-color: #e5e7eb;
      z-index: -1;
    }
    
    &.active:not(:last-child)::after {
      background-color: $primary-color;
    }
  }
}

// Upload Status
.upload-status {
  flex: 0 0 120px;
  font-size: $small-font-size;
  color: #6b7280;
  text-align: left;
  
  &.upload-status-warning {
    color: #f59e0b;
    font-weight: 500;
  }
  
  &.upload-status-error {
    color: $attention-color;
    font-weight: 500;
  }
}

// Upload Actions
.upload-actions {
  flex: 0 0 auto;
  
  .cancel-upload-btn {
    background: none;
    border: 1px solid #e5e7eb;
    color: #6b7280;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    cursor: pointer;
    font-size: $small-font-size;
    transition: all 0.2s ease;
    
    &:hover {
      background-color: #f3f4f6;
      border-color: #d1d5db;
      color: #374151;
    }
  }
}

// Upload Error Message
.upload-error {
  color: $attention-color;
  font-size: $small-font-size;
  margin-top: 0.5rem;
  padding: 0.5rem;
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 0.375rem;
}

// Network Warning Message
.upload-network-warning {
  display: none;
  align-items: center;
  padding: 8px;
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 4px;
  margin-top: 8px;
  font-size: $small-font-size;
  
  .warning-icon {
    margin-right: 8px;
    font-size: 16px;
  }
  
  .warning-text {
    color: #856404;
    font-weight: 500;
  }
}

// Upload Messages Container
.upload-messages {
  margin: 1rem 0;
  
  .upload-message {
    padding: 0.75rem 1rem;
    border-radius: 0.375rem;
    margin-bottom: 0.5rem;
    font-size: $small-font-size;
    
    &.upload-message-error {
      background-color: #fef2f2;
      color: #dc2626;
      border: 1px solid #fecaca;
    }
    
    &.upload-message-success {
      background-color: #f0fdf4;
      color: #16a34a;
      border: 1px solid #bbf7d0;
    }
    
    &.upload-message-info {
      background-color: #eff6ff;
      color: #2563eb;
      border: 1px solid #bfdbfe;
    }
  }
}

// File Upload Button Styling
.file-upload-button {
  background-color: $primary-color;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.375rem;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
  
  &:hover {
    background-color: darken($primary-color, 10%);
    transform: translateY(-1px);
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
  }
}

// File Input Styling
.custom-file-input {
  position: relative;
  display: inline-block;
  
  input[type="file"] {
    position: absolute;
    left: -9999px;
  }
  
  .file-input-label {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background-color: white;
    color: $text-color;
    border: 2px solid $border-color;
    padding: 0.75rem 1rem;
    border-radius: 0.375rem;
    cursor: pointer;
    transition: all 0.2s ease;
    
    &:hover {
      border-color: $primary-color;
      background-color: #f9fafb;
    }
  }
}

// Upload Animation
@keyframes upload-pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    opacity: 1;
  }
}

.upload-uploading .upload-progress-bar {
  animation: upload-pulse 2s ease-in-out infinite;
}

// Responsive Design
@media (max-width: $breakpoint-mobile) {
  .upload-item {
    flex-direction: column;
    align-items: stretch;
    gap: 0.75rem;
    
    .upload-info,
    .upload-progress,
    .upload-phases,
    .upload-status,
    .upload-actions {
      flex: none;
      max-width: none;
      text-align: left;
    }
    
    .upload-progress {
      margin: 0;
    }
    
    .upload-phases {
      max-width: none;
      justify-content: space-around;
      margin: 0.5rem 0;
      
      .phase {
        .phase-text {
          font-size: 10px;
        }
        
        .phase-icon {
          font-size: 16px;
        }
        
        // Adjust connecting lines for mobile
        &:not(:last-child)::after {
          width: calc(100% - 20px);
          left: 60%;
        }
      }
    }
    
    .upload-status {
      text-align: left;
    }
  }
  
  .upload-drop-zone {
    padding: 1.5rem 1rem;
    
    .upload-drop-text {
      font-size: $small-font-size;
    }
  }
}

// Simple upload notification
.upload-notification {
  background-color: #e6f3ff;
  border: 1px solid #b3d9ff;
  border-radius: 0.375rem;
  padding: 0.75rem 1rem;
  margin-bottom: 1rem;
  font-size: $small-font-size;
  color: #1e40af;
  
  .upload-info-icon {
    margin-right: 0.5rem;
  }
}