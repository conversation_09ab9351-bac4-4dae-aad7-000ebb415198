@import "variables";

// ABOUTME: Minimal admin-specific layout styles only - uses global styles for everything else
// ABOUTME: Flex-table layout for admin dashboard and basic admin container styling

// Admin container - simple wrapper
.admin-container {
  margin: 20px;
  max-width: 100%;
}

// Admin page header
.admin-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  h1 {
    margin: 0;
    color: $secondary-color;
  }
}

// Flex-table for admin dashboard - the only unique admin layout
.admin-flex-table {
  display: flex;
  flex-direction: column;
  border: 1px solid #e5e7eb;
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
  background: white;
}

.admin-flex-table-header,
.admin-flex-table-row {
  display: flex;
  border-bottom: 1px solid #e5e7eb;
  align-items: stretch;
}

.admin-flex-table-header {
  background-color: #f8f9fa;
  font-weight: bold;
}

.admin-flex-table-cell {
  padding: 12px 10px;
  text-align: left;
  overflow: hidden;
  text-overflow: ellipsis;
  border-right: 1px solid #e5e7eb;
  display: flex;
  align-items: center;

  &:last-child {
    border-right: none;
  }

  // Column widths for project admin table
  &.admin-col-id { flex: 0 0 60px; }
  &.admin-col-email { flex: 0 0 280px; }
  &.admin-col-summary { flex: 1; }
  &.admin-col-date { flex: 0 0 150px; }
  &.admin-col-status { flex: 0 0 120px; text-align: center; justify-content: center; }
  &.admin-col-actions { flex: 0 0 320px; justify-content: center; text-align: center; }

  // Column widths for referral codes table
  &.admin-col-code { min-width: 120px; }
  &.admin-col-tier { min-width: 100px; }
  &.admin-col-duration { min-width: 80px; }
  &.admin-col-usage { min-width: 80px; }
  &.admin-col-expires { min-width: 120px; }
  &.admin-col-description { min-width: 200px; flex: 1; }
}

.admin-flex-table-row:nth-child(even) {
  background-color: #f9fafb;
}

.admin-flex-table-row:hover {
  background-color: #f3f4f6;
}

// Simple details layout for show pages
.admin-details-section {
  background: white;
  padding: 20px 0;

  .admin-detail-row {
    display: flex;
    padding: 10px 0;
    border-bottom: 1px solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }

    .admin-detail-label {
      font-weight: bold;
      width: 200px;
      color: #666;
    }

    .admin-detail-value {
      flex: 1;
    }
  }
}

// Muted text-link for less important actions
.text-link-muted {
  font-size: $base-font-size;
  color: #666;
  text-decoration: none;
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;

  &:hover {
    color: $link-color;
    text-decoration: underline;
  }
}

// Mobile responsive
@media (max-width: $breakpoint-mobile) {
  .admin-container {
    margin: 10px;
  }

  .admin-flex-table-header,
  .admin-flex-table-row {
    flex-direction: column;

    .admin-flex-table-cell {
      border-right: none;
      border-bottom: 1px solid #e5e7eb;

      &:last-child {
        border-bottom: none;
      }
    }
  }
}