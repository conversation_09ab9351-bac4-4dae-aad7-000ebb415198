// app/assets/stylesheets/components/_connections.scss
@import "variables";

.connections-container {
  max-width: 64rem;
  margin: 10px auto;
  //padding: 1rem;
}

.connections-search {
  margin-bottom: 1.5rem;

  .search-filters {
    display: flex;
    gap: 1rem;
    align-items: center;
  }

  .filter-select {
    width: 12rem;
    padding: 0.5rem;
    border: 1px solid #e2e8f0;
    border-radius: 0.5rem;
    background-color: white;
  }

  .search-input-wrapper {
    position: relative;
    flex-grow: 1;
  }

  .search-input {
    width: 80%;
    padding: 0.5rem;
    padding-right: 2.5rem;
    border: 1px solid #e2e8f0;
    border-radius: 0.5rem;
  }

  .search-button {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    background: $primary-color;
    border: none;
    color: white;
    cursor: pointer;
  }
}

.connections-list {
  display: flex;
  flex-direction: column;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  background-color: white;
}

.connection-item {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  background-color: white;
  border-bottom: 1px solid #e2e8f0;
  align-items: flex-start;

  &:last-child {
    border-bottom: none;
  }
}

.profile-avatar {
  flex-shrink: 0;

  .avatar-circle {
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    background-color: #d7d7d7;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 1.25rem;
  }
}

.connection-content {
  flex-grow: 1;
  min-width: 0;
}

.connection-header {
  display: flex;
  justify-content: space-between;
  gap: 1rem;
  margin-bottom: 0.5rem;
}

.connection-main-info {
  .connection-name {
    font-weight: 600;
    font-size: 1.125rem;
    
    a {
      color: $blue;
      text-decoration: none;
      
      &:hover {
        color: $blue;
        text-decoration: underline;
      }
    }
  }

  .connection-location {
    font-size: $small-font-size;
    color: $text-color;
  }
}

.connection-bio {
  font-size: $small-font-size;
  color: $text-color;
  line-height: 1.5;
  
  .bio-expand {
    color: $blue;
    text-decoration: none;
    margin-left: 0.5rem;
    font-size: $small-font-size;
    
    &:hover {
      text-decoration: underline;
    }
  }
}

.connection-actions {
  flex-shrink: 0;
}

.status-badge {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: $small-font-size;
  
  &--self {
    background-color: $light-gray;
    color: $text-color;
  }
  
  &--connected {
    background-color: $primary-color;
    color: white;
  }
}

.profile-warning {
  font-size: $small-font-size;
  color: $red;
  margin-bottom: 0.5rem;
}

.profile-edit-button {
  display: inline-block;
  padding: 0.25rem 1rem;
  background-color: $blue;
  color: white;
  text-decoration: none;
  border-radius: 0.5rem;
  font-size: $small-font-size;
  
  &:hover {
    background-color: #1d4ed8;
  }
}

.action-button {
  display: inline-block;
  padding: 0.25rem 1rem;
  text-decoration: none;
  border-radius: 0.5rem;
  font-size: $small-font-size;
  
  &--outline {
    border: 1px solid $blue;
    color: $blue;
    
  }
}

.empty-state {
  text-align: center;
  color: $text-color;
  padding: 2rem;
}

// Secure File Display Styles (Chunk 6)
.file-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.file-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: white;
  transition: all 0.2s ease;
  cursor: pointer;
  position: relative;
  
  &:hover {
    border-color: #3b82f6;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
  }
}

.file-thumbnail-placeholder,
.file-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  border-radius: 6px;
  margin-bottom: 0.5rem;
  
  &.pdf-icon {
    background-color: #fef2f2;
    color: #dc2626;
  }
  
  &.doc-icon {
    background-color: #f0f9ff;
    color: $blue;
  }
}

.file-thumbnail-placeholder {
  background-color: #f9fafb;
  color: $secondary-color;
  border: 2px dashed #d1d5db;
  
  &[data-action="preview"]:hover {
    background-color: #f3f4f6;
    border-color: #9ca3af;
  }
}

.file-meta {
  text-align: center;
  font-size: $small-font-size;
  color: $secondary-color;
  margin-bottom: 0.5rem;
  
  .file-size,
  .file-date {
    display: block;
  }
}

.downloadButton {
  background: none;
  border: none;
  color: $secondary-color;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: color 0.2s ease;
  
  &:hover {
    color: #3b82f6;
    background-color: #f3f4f6;
  }
}

// Loading state
.file-item.loading {
  opacity: 0.6;
  pointer-events: none;
  
  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Mobile responsive
@media (max-width: $breakpoint-mobile) {
  .file-grid {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 0.5rem;
  }
  
  .file-item {
    padding: 0.75rem;
  }
}