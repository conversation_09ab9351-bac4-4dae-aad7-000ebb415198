class UploadChannel < ApplicationCable::Channel
  def subscribed
    upload = Upload.find_signed(params[:upload_signed_id], purpose: :upload_progress)
    stream_for upload
    
    # Immediately send current status when subscribing
    transmit({
      status: upload.status,
      progress: upload.progress_percentage,
      id: upload.id,
      original_filename: upload.original_filename,
      file_size: upload.file_size
    })
  rescue ActiveSupport::MessageVerifier::InvalidSignature
    reject
  end
  
  def unsubscribed
    # Cleanup when channel is unsubscribed
  end
end