module ProjectsHelper
  # Generate safe thumbnail URL for files with error handling
  def safe_thumbnail_url(project, file)
    return nil unless file
    
    # Check if file supports thumbnail generation
    if supports_thumbnail?(file)
      # Check for pre-generated thumbnail (same logic for all supported types)
      thumbnail = project.thumbnail_for_file(file)
      if thumbnail
        # Return URL for pre-generated thumbnail
        file_thumbnail_path(project_id: project.id, file_id: file.id)
      else
        # No thumbnail available - return nil so frontend shows placeholder icon
        nil
      end
    else
      # File type not supported for thumbnails
      nil
    end
  rescue => e
    Rails.logger.error "Failed to generate thumbnail URL for file #{file&.id}: #{e.message}"
    nil
  end

  # Get safe download URL for files
  def safe_download_url(project, file)
    return nil unless file
    
    file_download_path(project_id: project.id, file_id: file.id)
  rescue => e
    Rails.logger.error "Failed to generate download URL for file #{file&.id}: #{e.message}"
    nil
  end

  # Get safe inline viewing URL for files
  def safe_inline_url(project, file)
    return nil unless file
    
    file_inline_path(project_id: project.id, file_id: file.id)
  rescue => e
    Rails.logger.error "Failed to generate inline URL for file #{file&.id}: #{e.message}"
    nil
  end

  # Check if file supports thumbnail generation
  def supports_thumbnail?(file)
    return false unless file
    
    # Handle both ActiveStorage::Attached and ActiveStorage::Attachment
    content_type = file.respond_to?(:content_type) ? file.content_type : file.blob&.content_type
    
    return false unless content_type
    
    # Check if it's an image or PDF
    content_type.start_with?('image/') || content_type == 'application/pdf'
  end

  # Get appropriate icon class for file type
  def file_icon_class(file)
    return 'doc-icon' unless file
    
    # Handle both ActiveStorage::Attached and ActiveStorage::Attachment
    content_type = file.respond_to?(:content_type) ? file.content_type : file.blob&.content_type
    
    return 'doc-icon' unless content_type
    
    if content_type.start_with?('image/')
      'image-icon'
    elsif content_type == 'application/pdf'
      'pdf-icon'
    else
      'doc-icon'
    end
  end
end
