module ApplicationHelper
  include Pagy::Frontend

  def projects_section_active?
    controller_name == 'projects' || 
    current_page?(projects_path) || 
    current_page?(show_my_projects_path) || 
    current_page?(new_project_path) ||
    params[:controller] == 'projects'
  end

  def network_section_active?
    controller_name == 'network_connections' || 
    controller_name == 'connection_requests' ||
    current_page?(network_connections_path) || 
    current_page?(my_network_connections_path) || 
    current_page?(invite_network_connections_path) ||
    params[:controller] == 'network_connections' ||
    params[:controller] == 'connection_requests'
  end

  def profile_section_active?
    controller_name == 'user_profiles' || 
    controller_name == 'registrations' ||
    params[:controller] == 'user_profiles' ||
    (params[:controller] == 'devise/registrations' && params[:action] == 'edit')
  end

  # Subscription tier badge helper methods
  def tier_badge_class(tier)
    case tier.to_s
    when 'free' then 'secondary'
    when 'premium' then 'success'
    when 'beta' then 'primary'
    else 'secondary'
    end
  end

  def status_badge_class(status)
    case status.to_s
    when 'active' then 'success'
    when 'expired' then 'warning'
    when 'used_up' then 'info'
    when 'disabled' then 'danger'
    else 'secondary'
    end
  end

end
