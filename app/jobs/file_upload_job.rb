# ABOUTME: Background job for processing file uploads to S3 using Ready Flag pattern
# ABOUTME: Handles S3 upload, progress broadcasting, and temp file cleanup with comprehensive error handling

class FileUploadJob < ApplicationJob
  queue_as :default
  
  # Retry strategy for temporary failures
  retry_on StandardError, wait: :exponentially_longer, attempts: 5
  
  # Handle permanent failures
  discard_on ActiveRecord::RecordNotFound, Upload::InvalidStateTransition
  
  # GoodJob alternative to after_discard - handle final cleanup after retries exhausted
  # Note: This only catches exceptions that are retryable (i.e., not handled by discard_on)
  rescue_from StandardError do |exception|
    # Skip handling for exceptions that should be discarded immediately
    if exception.is_a?(ActiveRecord::RecordNotFound) || exception.is_a?(Upload::InvalidStateTransition)
      raise exception
    end
    
    upload_id = arguments.first if arguments.present?
    temp_file_path = arguments.second if arguments.length > 1
    upload = Upload.find_by(id: upload_id) if upload_id
    
    # Check if we've exhausted all retry attempts (1 initial + 4 retries = 5 total)
    if self.executions >= 5
      if upload
        upload.mark_failed!("Job failed after all retries: #{exception.message}")
      end
      # Clean up temp file from arguments
      if temp_file_path && File.exist?(temp_file_path)
        FileUtils.rm_f(temp_file_path) rescue nil
      end
      Rails.logger.error "FileUploadJob failed permanently for upload #{upload_id} after #{self.executions} attempts: #{exception.message}"
      # Don't re-raise - job is done
    else
      # Re-raise to trigger retry mechanism
      Rails.logger.info "FileUploadJob failed for upload #{upload_id} on attempt #{self.executions}, will retry: #{exception.message}"
      raise exception
    end
  end

  def perform(upload_id, temp_file_path)
    @upload = Upload.find(upload_id)
    @temp_file_path = temp_file_path
    
    Rails.logger.info "Processing upload #{@upload.id} (#{@upload.original_filename}) with temp path: #{@temp_file_path}"
    
    # Check if cancelled before we start
    if @upload.cancelled?
      Rails.logger.info "Upload #{@upload.id} was cancelled before processing"
      cleanup_temp_file
      return
    end
    
    # Verify upload is in a processable state
    # Allow processing state to handle duplicate job executions gracefully
    unless @upload.pending? || @upload.transferred? || @upload.processing?
      raise Upload::InvalidStateTransition, "Upload #{@upload.id} is not in processable state (status: #{@upload.status})"
    end
    
    # If already processing, check if this is a duplicate job
    if @upload.processing?
      Rails.logger.info "Upload #{@upload.id} already in processing state - checking for duplicate job execution"
      return # Exit gracefully to avoid duplicate processing
    end
    
    # Mark as transferred if still pending
    if @upload.pending?
      @upload.mark_transferred!
    end
    
    # Check again for cancellation after state changes
    @upload.reload
    if @upload.cancelled?
      Rails.logger.info "Upload #{@upload.id} was cancelled after transfer"
      cleanup_temp_file
      return
    end
    
    # Mark upload as processing and broadcast status
    @upload.mark_processing!
    
    begin
      # Upload to S3 with progress tracking
      s3_key = upload_to_s3
      
      # Attach to target model via Active Storage
      attach_to_target_model(s3_key)
      
      # Mark as completed
      @upload.mark_completed!(s3_key)
      
      Rails.logger.info "Upload #{@upload.id} completed successfully: #{s3_key}"
      
    rescue => e
      Rails.logger.error "Upload #{@upload.id} failed: #{e.message}"
      @upload.mark_failed!(e.message)
      raise # Re-raise to trigger retry mechanism
      
    ensure
      # Always cleanup temp file
      if @temp_file_path && File.exist?(@temp_file_path)
        FileUtils.rm_f(@temp_file_path)
        Rails.logger.info "Cleaned up temp file: #{@temp_file_path}"
      end
    end
  end

  private

  def cleanup_temp_file
    if @temp_file_path && File.exist?(@temp_file_path)
      FileUtils.rm_f(@temp_file_path)
      Rails.logger.info "Cleaned up temp file: #{@temp_file_path}"
    end
  end

  def upload_to_s3
    s3_key = generate_s3_key
    s3_object = s3_bucket.object(s3_key)
    
    File.open(@temp_file_path, 'rb') do |file|
      file_size = file.size
      bytes_uploaded = 0
      last_progress_update = 0
      
      # Upload with progress tracking
      s3_object.put(
        body: file,
        content_type: @upload.content_type,
        content_disposition: "attachment; filename=\"#{@upload.original_filename}\"",
        metadata: {
          'original-filename' => @upload.original_filename,
          'upload-id' => @upload.id.to_s,
          'user-id' => @upload.user_id.to_s
        }
      ) do |chunk|
        bytes_uploaded += chunk.length
        progress = (bytes_uploaded.to_f / file_size * 100).round
        
        # Broadcast progress updates (throttled to avoid too many updates)
        if progress - last_progress_update >= 5 || progress == 100
          @upload.update_progress!(progress)
          last_progress_update = progress
        end
      end
    end
    
    s3_key
  end

  def attach_to_target_model(s3_key)
    return unless @upload.target
    
    case @upload.target
    when Project
      attach_to_project(s3_key)
    else
      Rails.logger.warn "Unknown target type for upload #{@upload.id}: #{@upload.target.class}"
    end
  end

  def attach_to_project(s3_key)
    project = @upload.target
    
    # Calculate MD5 checksum from the temp file
    checksum = calculate_file_checksum(@temp_file_path)
    
    # Create Active Storage blob directly referencing the S3 object
    blob = ActiveStorage::Blob.create!(
      key: s3_key,
      filename: @upload.original_filename,
      content_type: @upload.content_type,
      byte_size: @upload.file_size,
      checksum: checksum,
      service_name: 'amazon_uploads', # Use the correct service for uploads
      metadata: {
        'upload_id' => @upload.id,
        'user_id' => @upload.user_id
      }
    )
    
    # INVESTIGATION FIX: Replace Current.skip_purge_duplicates with pessimistic locking
    # Root cause: ActiveRecord race condition in has_many_attached during concurrent access
    # Solution: Serialize attachment operations per project to prevent "last write wins"
    # See: docs/features/file-system/10_bulk-upload-race-condition-investigation.md
    project.with_lock do
      project.private_files.attach(blob)
      Rails.logger.info "Attached upload #{@upload.id} to project #{project.id} as blob #{blob.id}"
    end
  end

  def generate_s3_key
    timestamp = Time.current.strftime('%Y/%m/%d')
    category = UploadUtils.content_type_category(@upload.content_type)
    random_id = SecureRandom.hex(16)
    extension = File.extname(@upload.original_filename)
    
    "uploads/#{timestamp}/#{category}/#{random_id}#{extension}"
  end

  def calculate_file_checksum(file_path)
    # Calculate MD5 checksum that Active Storage expects
    # Active Storage uses base64-encoded MD5 checksum
    require 'digest/md5'
    require 'base64'
    
    md5 = Digest::MD5.new
    File.open(file_path, 'rb') do |file|
      while chunk = file.read(8192)
        md5.update(chunk)
      end
    end
    
    Base64.strict_encode64(md5.digest)
  end

  def s3_bucket
    @s3_bucket ||= begin
      # Use environment-specific credentials that match storage.yml configuration
      aws_key = Rails.env.development? ? :aws_dev : :aws_prod
      
      # Configure client with appropriate timeouts for large file uploads
      s3_client = Aws::S3::Client.new(
        region: Rails.application.credentials.dig(aws_key, :region) || ENV['AWS_REGION'],
        credentials: Aws::Credentials.new(
          Rails.application.credentials.dig(aws_key, :access_key_id) || ENV['AWS_ACCESS_KEY_ID'],
          Rails.application.credentials.dig(aws_key, :secret_access_key) || ENV['AWS_SECRET_ACCESS_KEY']
        ),
        # Timeout configuration for large file uploads
        http_open_timeout: 15,      # Time to establish connection
        http_read_timeout: 120,     # Time to wait for response
        http_idle_timeout: 10,      # Time to wait between packets
        http_continue_timeout: 2,   # Time to wait for 100-continue
        retry_mode: 'adaptive',     # Use adaptive retry mode
        retry_limit: 3              # Retry failed requests
      )
      
      s3_resource = Aws::S3::Resource.new(client: s3_client)
      
      # Use the uploads bucket (matching Active Storage amazon_uploads service)
      bucket_name = Rails.env.development? ? 
        'app-sierra-a7b3c9e2-f8d1-4e56-9c2a-dev-uploads' : 
        'app-sierra-b1c5e9a3-d7f2-4a68-9b4e-prod-uploads'
      
      s3_resource.bucket(bucket_name)
    end
  end

end