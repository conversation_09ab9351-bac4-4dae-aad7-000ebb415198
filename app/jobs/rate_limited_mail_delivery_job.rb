class RateLimitedMailDeliveryJob < ApplicationJob
  class ResendRateLimitError < StandardError; end
  
  include GoodJob::ActiveJobExtensions::Concurrency
  
  # Set queue priority for email jobs
  queue_as :mailers
  
  # Officially documented way to rate limit:
  # Enforce a maximum of 2 jobs performed per second.
  good_job_control_concurrency_with(
    perform_throttle: [2, 1.second],
    key: 'resend_api_rate_limit'
  )
  
  # Retry on Resend's rate limit error with exponential backoff.
  retry_on ResendRateLimitError, wait: :exponentially_longer, attempts: 5
  
  def perform(mailer_class_name, mailer_method, delivery_method, delivery_args)
    mailer_class = mailer_class_name.constantize
    args = delivery_args.fetch(:args, [])
    kwargs = delivery_args.fetch(:kwargs, {})
    params = delivery_args.fetch(:params, {})

    mailer = build_mailer(mailer_class, mailer_method, args: args, kwargs: kwargs, params: params)
    mailer.send(delivery_method)
  rescue Resend::Error => e
    # Only retry on a specific rate limit error, not other API errors.
    if e.message.include?('rate_limit_exceeded')
      raise ResendRateLimitError, "Resend API rate limit exceeded, retrying."
    else
      # For other errors (e.g., validation), don't retry.
      raise e
    end
  end

  private

  def build_mailer(mailer_class, mailer_method, args:, kwargs:, params:)
    if params.any?
      mailer = mailer_class.with(params)
    else
      mailer = mailer_class
    end
    
    if kwargs.any?
      mailer.send(mailer_method, *args, **kwargs)
    else
      mailer.send(mailer_method, *args)
    end
  end
end