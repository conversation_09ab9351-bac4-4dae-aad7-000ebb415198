# ABOUTME: Background job to safely clean up duplicate blobs after Lambda processing
# ABOUTME: Runs periodically to remove orphaned files that were skipped during bulk uploads

class DuplicateCleanupJob < ApplicationJob
  queue_as :default
  
  # Cleanup orphaned and duplicate blobs safely after Lambda has had time to process
  def perform(blob_id = nil)
    if blob_id
      # Clean up specific blob
      cleanup_blob(blob_id)
    else
      # Cleanup old orphaned blobs (older than 24 hours)
      cleanup_orphaned_blobs
    end
  end
  
  private
  
  def cleanup_blob(blob_id)
    blob = ActiveStorage::Blob.find_by(id: blob_id)
    return unless blob
    
    # Only cleanup if blob has no attachments and is older than 1 hour
    if blob.attachments.empty? && blob.created_at < 1.hour.ago
      Rails.logger.info "Cleaning up orphaned blob #{blob.id} (#{blob.filename})"
      blob.purge_later
    end
  end
  
  def cleanup_orphaned_blobs
    # Find blobs with no attachments that are older than 24 hours
    orphaned_blobs = ActiveStorage::Blob
      .joins("LEFT JOIN active_storage_attachments ON active_storage_blobs.id = active_storage_attachments.blob_id")
      .where("active_storage_attachments.id IS NULL")
      .where("active_storage_blobs.created_at < ?", 24.hours.ago)
      .where(service_name: ['amazon_uploads', 'amazon_thumbnails'])
      .limit(100) # Process in batches
    
    Rails.logger.info "Found #{orphaned_blobs.count} orphaned blobs for cleanup"
    
    orphaned_blobs.each do |blob|
      Rails.logger.info "Cleaning up orphaned blob #{blob.id} (#{blob.filename})"
      blob.purge_later
    end
  end
end