require 'open3'

class ThumbnailGenerationJob < ApplicationJob
  queue_as :default
  
  def perform(project, blob_id)
    # Handle both Project objects and IDs
    project = Project.find_by(id: project.id) if project.is_a?(Project)
    return unless project
    
    # Find the specific blob
    blob = ActiveStorage::Blob.find_by(id: blob_id)
    return unless blob
    
    # Find the attachment linking this blob to the project
    file_attachment = project.private_files.find_by(blob: blob)
    return unless file_attachment
    
    Rails.logger.info "Processing thumbnail for blob #{blob.id} (#{blob.filename}) attached to project #{project.id}"
    
    # Check if thumbnail already exists (idempotency)
    if project.thumbnail_for_file(file_attachment).present?
      Rails.logger.info "Thumbnail already exists for blob #{blob.id}, skipping"
      return
    end
    
    # File is guaranteed to exist on S3 at this point (triggered by analyze event)
    # No need for S3 existence checks or retry logic
    generate_and_attach_thumbnail(project, file_attachment)
  end
  
  private
  
  def generate_and_attach_thumbnail(project, file_attachment)
    # DISABLED: All thumbnail generation now handled by <PERSON><PERSON> via webhook
    # case file_attachment.blob.content_type
    # when %r{^image/}
    #   generate_image_thumbnail(project, file_attachment)
    # when 'application/pdf'
    #   # PDFs are handled by Lambda - skip local processing
    #   Rails.logger.info "Skipping PDF thumbnail for #{file_attachment.filename} - handled by Lambda webhook"
    #   return
    # else
    #   Rails.logger.info "Unsupported file type for thumbnail: #{file_attachment.blob.content_type}"
    #   return
    # end
    
    Rails.logger.info "DISABLED: Rails thumbnail generation for #{file_attachment.filename} - Lambda handles all file types via webhook"
    return
  end
  
  def generate_image_thumbnail(project, image_file)
    # DISABLED: Rails image thumbnail generation - Lambda handles all file types
    # begin
    #   # Generate variant using Active Storage's built-in image processing
    #   # File is guaranteed to be analyzed and ready at this point
    #   variant = image_file.variant(resize_to_limit: [300, 200]).processed
    #   thumbnail_data = variant.download
    #   
    #   # Create unique filename for the thumbnail
    #   file_hash = project.generate_secure_file_hash(image_file)
    #   thumbnail_filename = "thumb_#{file_hash[0..8]}_#{image_file.filename.base}.png"
    #   
    #   # Attach the thumbnail to the project
    #   project.pdf_thumbnails.attach(
    #     io: StringIO.new(thumbnail_data),
    #     filename: thumbnail_filename,
    #     content_type: 'image/png'
    #   )
    #   
    #   Rails.logger.info "Successfully generated image thumbnail for file #{image_file.id}"
    #   
    # rescue => e
    #   Rails.logger.error "Image thumbnail generation failed for file #{image_file.id}: #{e.message}"
    #   Rails.logger.error e.backtrace.join("\n")
    # end
    
    Rails.logger.info "DISABLED: Rails image thumbnail generation for file #{image_file.id} - Lambda handles all file types via webhook"
  end
end