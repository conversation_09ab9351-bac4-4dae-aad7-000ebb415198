# ABOUTME: Background job for cleaning up uploads that have been stuck for over 24 hours
# ABOUTME: Runs daily via cron scheduling to maintain system health and prevent disk space issues

class StaleUploadsCleanupJob < ApplicationJob
  queue_as :low
  
  def perform
    stale_uploads = Upload.where(
      status: ['pending', 'transferred', 'processing'],
      created_at: ..24.hours.ago
    )
    
    Rails.logger.info "Starting cleanup of #{stale_uploads.count} stale uploads"
    
    cleanup_count = 0
    
    stale_uploads.find_each do |upload|
      Rails.logger.info "Cleaning up stale upload #{upload.id} (#{upload.original_filename}) - status: #{upload.status}, age: #{time_ago_in_words(upload.created_at)}"
      
      begin
        # Clean up temp file if it exists
        if upload.temp_file_path.present? && File.exist?(upload.temp_file_path)
          FileUtils.rm_f(upload.temp_file_path)
          Rails.logger.info "Removed temp file: #{upload.temp_file_path}"
        end
        
        # Mark as failed with descriptive message
        upload.mark_failed!("Upload timed out after 24 hours")
        
        cleanup_count += 1
        
      rescue => e
        Rails.logger.error "Failed to cleanup upload #{upload.id}: #{e.message}"
        # Continue with other uploads even if one fails
      end
    end
    
    Rails.logger.info "Successfully cleaned up #{cleanup_count} stale uploads"
    
    # Optionally notify administrators if there were many stale uploads
    if cleanup_count > 10
      Rails.logger.warn "High number of stale uploads detected (#{cleanup_count}). Consider investigating system performance."
    end
  end

  private

  def time_ago_in_words(time)
    seconds = Time.current - time
    case seconds
    when 0..59
      "#{seconds.to_i} seconds ago"
    when 60..3599
      "#{(seconds / 60).to_i} minutes ago"
    when 3600..86399
      "#{(seconds / 3600).to_i} hours ago"
    else
      "#{(seconds / 86400).to_i} days ago"
    end
  end
end