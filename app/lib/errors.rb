# ABOUTME: Custom error classes for the application to replace generic StandardError usage
# ABOUTME: Provides specific exception types for better error handling and internationalization support
module Errors
  class RedemptionError < StandardError; end
  class CodeExpiredError < RedemptionError; end
  class CodeLimitReachedError < RedemptionError; end
  class CodeNotActiveError < RedemptionError; end
  class UserAlreadyPremiumError < RedemptionError; end
end