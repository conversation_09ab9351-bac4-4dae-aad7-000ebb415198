# ABOUTME: Custom Devise mailer that overrides invitation_instructions to include inviter's name
# ABOUTME: Fetches inviter user_profile data via invited_by association for personalized email subjects
class CustomDeviseMailer < Devise::Mailer
  helper :application
  include Devise::Controllers::UrlHelpers
  default template_path: 'devise/mailer'

  def invitation_instructions(record, token, opts = {})
    # Make the token available to the mailer view
    @token = token
    
    # record is the invited User instance
    # devise_invitable automatically sets the invited_by association
    inviter = record.invited_by

    # Prepare custom interpolation variables
    interpolation_opts = {
      inviter_first_name: 'A member',
      inviter_last_name: ''
    }

    if inviter&.user_profile
      interpolation_opts[:inviter_first_name] = inviter.user_profile.first_name.presence || 'A member'
      interpolation_opts[:inviter_last_name] = inviter.user_profile.last_name.presence || ''
    end
    
    # Make inviter info available to the view as instance variables
    @inviter_first_name = interpolation_opts[:inviter_first_name]
    @inviter_last_name = interpolation_opts[:inviter_last_name]

    # Manually generate the subject with interpolation
    # This ensures our variables are properly interpolated BEFORE passing to devise_mail
    subject_translation = I18n.t(
      'devise.mailer.invitation_instructions.subject',
      **interpolation_opts
    )

    # Add the generated subject to options, overriding any default
    final_opts = opts.merge(subject: subject_translation)

    # Call devise_mail with the pre-interpolated subject
    devise_mail(record, :invitation_instructions, final_opts)
  end
end