class NotificationMailer < ApplicationMailer
  # Use our custom rate-limited delivery job for Resend API compliance
  self.delivery_job = RateLimitedMailDeliveryJ<PERSON>

  def access_request_notification(project, user)
    @project = project
    @user = user
    
    with_recipient_locale(@project.user) do
      mail(to: @project.user.email, subject: I18n.t('access_request_notification.subject', default: 'New Access Request'))
    end
  end

  def approved_access_notification(project, user, current_user)
    @project = project
    @user = user
    @current_user = current_user
    @locale = @user.user_profile.default_language || I18n.default_locale

    with_recipient_locale(@user) do
      mail(to: @user.email, subject: I18n.t('approved_access.subject', default: 'Access Request Approved'))
    end
  end

  def admin_project_notification(project, admin, current_user)
    @project = project
    @admin = admin
    @user = current_user
    
    Rails.logger.info "🔔 [EMAIL] Sending admin project notification:"
    Rails.logger.info "   - Project: #{@project.id} (#{@project.summary})"
    Rails.logger.info "   - Admin: #{@admin.email}"
    Rails.logger.info "   - User: #{@user.email}"
    
    with_recipient_locale(@admin) do
      mail(to: @admin.email, subject: I18n.t('admin_project_notification.subject', default: 'Summary Approval'))
    end
  end

  def new_project_notification(project, user)
    @project = project
    @user = user
    
    with_recipient_locale(@user) do
      mail(to: @user.email, subject: I18n.t('new_project_notification.subject', default: 'New Project'))
    end
  end

  def user_approval_request_notification(user)
    @user = user
    @user_profile = user.user_profile
    
    Rails.logger.info "🔔 [EMAIL] Sending user approval request notification:"
    Rails.logger.info "   - User: #{@user.email}"
    Rails.logger.info "   - Profile: #{@user_profile.first_name} #{@user_profile.last_name}"
    
    # Send to all super_boss users
    admin_emails = User.where(role: :super_boss).pluck(:email)
    
    if admin_emails.any?
      mail(to: admin_emails, subject: I18n.t('user_approval_request_notification.subject', default: 'New User Awaiting Approval'))
    end
  end

  # Custom bulk notification method that sends immediately
  def self.bulk_new_project_notification(project, user_ids)
    # Process users in batches to respect Resend API limits and avoid memory issues
    User.where(id: user_ids).includes(:user_profile).find_in_batches(batch_size: 50) do |users_batch|
      users_batch.each do |user|
        begin
          # Send individual notification using existing method
          new_project_notification(project, user).deliver_now
        rescue => e
          # Log the error for the specific user but continue with the rest of the batch.
          # This prevents a single failure from causing a full job retry.
          Rails.logger.error "Bulk Notification Failure: Could not send 'new_project_notification' to user #{user.id} for project #{project.id}. Reason: #{e.message}"
        end
        
        # CRITICAL FIX: Sleep after EACH email to respect 2 emails/second rate limit
        rate_limit_sleep
      end
    end
  end

  # Rate limiting method for testability
  def self.rate_limit_sleep
    sleep(0.5)
  end

  private

  def with_recipient_locale(recipient)
    locale = recipient.user_profile.try(:default_language) || I18n.default_locale
    I18n.with_locale(locale) { yield }
  end
end
