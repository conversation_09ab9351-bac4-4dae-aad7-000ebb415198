# SecureFileTokenService
#
# Core security service for generating and validating cryptographically secure, 
# time-limited tokens for private file access. This service ensures zero URL exposure
# while maintaining comprehensive authorization checks.
#
# Security Features:
# - JWT tokens with 5-minute expiration (configurable via JWT_EXPIRATION)
# - Cryptographic nonce for additional randomness
# - No file metadata exposure in tokens
# - Comprehensive error handling and logging
# - Token validation with expiration checks
#
# Usage:
#   token = SecureFileTokenService.generate_token(file_attachment, user)
#   payload = SecureFileTokenService.decode_token(token)

class SecureFileTokenService
  class << self
    # Generate a cryptographically secure JWT token for file access
    #
    # @param file_attachment [ActiveStorage::Attachment] The file to generate token for
    # @param user [User] The user requesting access
    # @param request [ActionDispatch::Request] The current request for session binding
    # @param expires_in [ActiveSupport::Duration] Custom expiration time (for testing)
    # @return [String] JWT token containing encrypted access permissions
    def generate_token(file_attachment, user, request: nil, expires_in: JWT_EXPIRATION)
      # CRITICAL SECURITY: Generate session fingerprint to prevent token sharing
      session_fingerprint = if request&.session&.id
        Digest::SHA256.hexdigest("#{request.session.id}:#{user.id}:#{Rails.application.secret_key_base}")
      else
        # Fallback for test environments
        Digest::SHA256.hexdigest("test:#{user.id}:#{Time.current.to_i}")
      end
      
      payload = {
        file_id: file_attachment.id,
        user_id: user.id,
        project_id: file_attachment.record_id,
        exp: expires_in.from_now.to_i,
        iat: Time.current.to_i,
        nonce: SecureRandom.hex(16),
        session_fp: session_fingerprint,
        ip: request&.remote_ip # Store IP for additional validation
      }
      JWT.encode(payload, JWT_SECRET, JWT_ALGORITHM)
    end
    
    # Decode and validate JWT token
    #
    # @param token [String] JWT token to decode
    # @return [Hash, nil] Decoded payload or nil if invalid/expired
    def decode_token(token)
      JWT.decode(token, JWT_SECRET, true, algorithm: JWT_ALGORITHM)[0]
    rescue JWT::DecodeError, JWT::ExpiredSignature => e
      Rails.logger.warn "[SECURE_FILE_TOKEN] JWT decode failed: #{e.message}"
      nil
    end
    
    # Validate if token is still valid (not expired)
    #
    # @param token [String] JWT token to validate
    # @return [Boolean] true if token is valid and not expired
    def token_valid?(token)
      payload = decode_token(token)
      return false unless payload
      payload['exp'] > Time.current.to_i
    end
    
    # Validate token with session binding and IP verification
    #
    # @param token [String] JWT token to validate
    # @param request [ActionDispatch::Request] Current request for validation
    # @return [Boolean] true if token is valid and bound to current session
    def validate_token_with_session(token, request)
      payload = decode_token(token)
      return false unless payload
      
      # Check expiration first
      return false if payload['exp'] <= Time.current.to_i
      
      # --- BEGIN CRITICAL FIX ---
      # If a session fingerprint exists in the token, validation is mandatory.
      if payload['session_fp']
        # 1. The request MUST have a session.
        return false unless request&.session&.id
        
        # 2. The session fingerprint MUST match the one in the token.
        current_fingerprint = Digest::SHA256.hexdigest(
          "#{request.session.id}:#{payload['user_id']}:#{Rails.application.secret_key_base}"
        )
        # 3. Use a secure comparison to prevent timing attacks.
        unless ActiveSupport::SecurityUtils.secure_compare(payload['session_fp'], current_fingerprint)
          return false
        end
      end
      # --- END CRITICAL FIX ---
      
      # Optional: Validate IP address (can be disabled if too strict)
      if Rails.configuration.try(:validate_token_ip) && payload['ip'] && request&.remote_ip
        return false unless payload['ip'] == request.remote_ip
      end
      
      true
    end
    
    # SECURITY FIX: Restrict test token generation to development and test environments only
    # This prevents misuse in production environments
    if Rails.env.development? || Rails.env.test?
      # Generate a secure token for testing purposes
      # This method is useful for development and testing
      def generate_test_token(user_id: 1, file_id: 1, project_id: 1)
        session_fingerprint = Digest::SHA256.hexdigest("test:#{user_id}:#{Time.current.to_i}")
        
        payload = {
          file_id: file_id,
          user_id: user_id,
          project_id: project_id,
          exp: JWT_EXPIRATION.from_now.to_i,
          iat: Time.current.to_i,
          nonce: SecureRandom.hex(16),
          session_fp: session_fingerprint,
          ip: '127.0.0.1'
        }
        JWT.encode(payload, JWT_SECRET, JWT_ALGORITHM)
      end
    end
  end
end