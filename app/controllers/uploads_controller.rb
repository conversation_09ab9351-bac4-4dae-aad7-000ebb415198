# ABOUTME: Handles server-side file uploads using Ready Flag pattern for race condition prevention
# ABOUTME: Implements secure file validation, temp storage, and background job coordination

class UploadsController < ApplicationController
  include ActionView::Helpers::Date<PERSON>elper  # For time_ago_in_words helper
  
  before_action :authenticate_user!
  before_action :validate_upload_params, only: [:create]
  before_action :check_rate_limit, only: [:create]
  before_action :find_upload, only: [:show, :destroy, :cleanup_stuck]

  # POST /uploads
  # Creates upload records and processes files following Ready Flag pattern
  def create
    @uploads = []
    
    begin
      Upload.transaction do
        files_to_upload.each do |file|
          upload = process_file_upload(file)
          @uploads << upload
        end
      end
      
      # Jobs are explicitly enqueued by controller after upload creation
      
      render json: upload_response, status: :accepted
      
    rescue => e
      Rails.logger.error "Upload failed: #{e.message}"
      cleanup_failed_uploads
      render json: { errors: ["Upload failed: #{e.message}"] }, status: :unprocessable_entity
    end
  end

  # GET /uploads/:id
  # Returns current upload status and progress with diagnostic information
  def show
    render json: {
      id: @upload.id,
      status: @upload.status,
      progress_percentage: @upload.progress_percentage,
      original_filename: @upload.original_filename,
      file_size: @upload.file_size,
      error_message: @upload.error_message,
      created_at: @upload.created_at,
      updated_at: @upload.updated_at,
      # New diagnostic fields
      time_in_status: Time.current - @upload.updated_at,
      job_enqueued: job_exists_for_upload?(@upload),
      temp_file_exists: @upload.temp_file_exists?
    }
  end

  # DELETE /uploads/:id
  # Cancels upload if possible and cleans up temp files
  def destroy
    unless @upload.can_cancel?
      render json: { errors: ["Upload cannot be cancelled in #{@upload.status} state"] }, 
             status: :unprocessable_entity
      return
    end
    
    @upload.mark_cancelled!
    
    render json: { message: "Upload cancelled successfully" }
  end

  # POST /uploads/:id/cleanup_stuck
  # Manual cleanup for stuck uploads (user-initiated only)
  def cleanup_stuck
    unless @upload.stuck?
      render json: { errors: ["Upload is not stuck (updated #{time_ago_in_words(@upload.updated_at)} ago)"] }, 
             status: :unprocessable_entity
      return
    end
    
    success = @upload.cleanup_stuck_upload!
    
    if success
      render json: { 
        message: "Stuck upload cleaned up successfully",
        upload_id: @upload.id,
        previous_status: @upload.status_was || 'unknown',
        cleanup_time: Time.current
      }
    else
      render json: { errors: ["Failed to cleanup stuck upload"] }, 
             status: :unprocessable_entity
    end
  end

  private

  def validate_upload_params
    # Check for files parameter (single or multiple)
    if params[:file].blank? && params[:files].blank?
      render json: { errors: ["File is required"] }, status: :unprocessable_entity
      return
    end
    
    # Validate target if provided
    if params[:target_type].present? && params[:target_id].present?
      validate_target_access
    end
    
    # Check file count limits
    if files_to_upload.count > Rails.application.config.max_uploads_per_request
      render json: { errors: ["Too many files. Maximum #{Rails.application.config.max_uploads_per_request} files per request."] }, 
             status: :unprocessable_entity
      return
    end
    
    # Validate each file
    files_to_upload.each do |file|
      validate_single_file(file)
    end
  end

  def validate_single_file(file)
    # File size validation
    unless UploadUtils.file_size_valid?(file.size)
      max_size = ActiveSupport::NumberHelper.number_to_human_size(Rails.application.config.max_upload_size)
      render json: { errors: ["File size too large. Maximum size is #{max_size}."] }, 
             status: :unprocessable_entity
      return
    end
    
    # Content type validation
    unless UploadUtils.allowed_content_type?(file.content_type)
      render json: { errors: ["Content type '#{file.content_type}' is not allowed."] }, 
             status: :unprocessable_entity
      return
    end
  end

  def validate_target_access
    target_class = params[:target_type].constantize
    @target = target_class.find_by(id: params[:target_id])
    
    unless @target
      render json: { errors: ["Target not found"] }, status: :unprocessable_entity
      return
    end
    
    # Check if user can upload to this target
    unless can_upload_to_target?(@target)
      render json: { errors: ["Not authorized to upload to this target"] }, status: :forbidden
      return
    end
  rescue NameError
    render json: { errors: ["Invalid target type"] }, status: :unprocessable_entity
  end

  def can_upload_to_target?(target)
    case target
    when Project
      target.user == current_user
    else
      false # Add other target types as needed
    end
  end

  def check_rate_limit
    uploads_in_last_hour = current_user.uploads
                                      .where(created_at: 1.hour.ago..Time.current)
                                      .count
    
    if uploads_in_last_hour >= Rails.application.config.upload_rate_limit_per_user_per_hour
      render json: { errors: ["Rate limit exceeded. Try again later."] }, 
             status: :too_many_requests
    end
  end

  def files_to_upload
    @files_to_upload ||= begin
      if params[:files].present?
        Array(params[:files])
      elsif params[:file].present?
        [params[:file]]
      else
        []
      end
    end
  end

  def process_file_upload(file)
    # Create Upload record in pending status
    upload = Upload.create!(
      user: current_user,
      target: @target,
      original_filename: file.original_filename,
      content_type: file.content_type,
      file_size: file.size,
      status: :pending,
      progress_percentage: 0
    )

    # Generate persistent temp path
    temp_filename = UploadUtils.generate_temp_filename(file.original_filename)
    persistent_temp_path = UploadUtils.temp_file_path(temp_filename)
    
    # Ensure directory exists
    FileUtils.mkdir_p(File.dirname(persistent_temp_path))
    
    # Use move instead of copy for better performance
    # This is instant even for large files
    begin
      FileUtils.mv(file.tempfile.path, persistent_temp_path)
    rescue Errno::EXDEV
      # Cross-device move not supported, fall back to copy
      FileUtils.cp(file.tempfile.path, persistent_temp_path)
    end
    
    # Store temp path on upload record for cleanup
    upload.update_column(:temp_file_path, persistent_temp_path)
    
    # Execute job immediately (WebSocket race handled by resilient frontend)
    FileUploadJob.perform_later(upload.id, persistent_temp_path)
    
    # Broadcast upload creation status (callback only triggers on changes, not creation)
    upload.broadcast_status
    
    Rails.logger.info "Upload #{upload.id} created, job scheduled immediately"
    
    upload
  end

  def upload_response
    if @uploads.count == 1
      # Single file upload response
      upload = @uploads.first
      {
        id: upload.id,
        status: upload.status,
        signed_id: upload.generate_secure_signed_id,
        original_filename: upload.original_filename,
        file_size: upload.file_size,
        progress_percentage: upload.progress_percentage,
        content_type: upload.content_type
      }
    else
      # Multiple files upload response
      {
        uploads: @uploads.map do |upload|
          {
            id: upload.id,
            status: upload.status,
            signed_id: upload.generate_secure_signed_id,
            original_filename: upload.original_filename,
            file_size: upload.file_size,
            progress_percentage: upload.progress_percentage,
            content_type: upload.content_type
          }
        end,
        total_count: @uploads.count,
        total_size: @uploads.sum(&:file_size)
      }
    end
  end

  def cleanup_failed_uploads
    @uploads&.each do |upload|
      begin
        upload.cleanup_temp_file! if upload.temp_file_path.present?
        upload.destroy
      rescue => e
        Rails.logger.error "Failed to cleanup upload #{upload.id}: #{e.message}"
      end
    end
  end

  def find_upload
    @upload = current_user.uploads.find_by(id: params[:id])
    
    unless @upload
      render json: { errors: ["Upload not found"] }, status: :not_found
      return
    end
  end


  def job_exists_for_upload?(upload)
    # Check if a job exists in GoodJob queue
    GoodJob::Job.where(
      "serialized_params->>'job_class' = ? AND serialized_params->'arguments'->0 = ?",
      'FileUploadJob',
      upload.id.to_s
    ).where(finished_at: nil).exists?
  end
end