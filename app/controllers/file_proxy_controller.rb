class FileProxyController < ApplicationController
  before_action :authenticate_user!
  before_action :set_project_and_file
  before_action :authorize_access

  # Serve file thumbnails (variants/previews)
  def thumbnail
    serve_file_variant_or_preview
  end

  # Serve full files for download
  def download
    serve_full_file(disposition: 'attachment')
  end

  # Serve full files for inline viewing
  def inline
    serve_full_file(disposition: 'inline')
  end

  private

  def set_project_and_file
    @project = Project.find(params[:project_id])
    @file = @project.private_files.find(params[:file_id])
  rescue ActiveRecord::RecordNotFound
    head :not_found
  end

  def authorize_access
    authorize!(@project, to: :view_full_details?)
  end

  def serve_file_variant_or_preview
    # Simple approach: Only serve pre-generated thumbnails, no on-demand generation
    thumbnail = @project.thumbnail_for_file(@file)
    
    if thumbnail
      # Serve the pre-generated thumbnail
      stream_blob(thumbnail.blob, disposition: 'inline')
    else
      # No thumbnail available - return 404 so frontend can show placeholder icon
      head :not_found
    end
  rescue => e
    Rails.logger.error "Thumbnail serving failed for file #{@file.id}: #{e.message}"
    head :not_found
  end

  def serve_full_file(disposition:)
    # Colorful logging for full file display
    Rails.logger.info "\e[32m[FILE DISPLAY]\e[0m User #{current_user.id} is viewing full file #{@file.id} (#{@file.filename}) from project #{@project.id} - Size: #{@file.blob.byte_size} bytes"
    
    stream_blob(@file.blob, disposition: disposition, filename: @file.filename.to_s)
  end

  # Shared method to handle streaming logic for both full files and representations
  def stream_blob(blob, disposition:, filename: nil)
    response.headers['Content-Type'] = blob.content_type
    response.headers['Content-Disposition'] = ActionDispatch::Http::ContentDisposition.format(
      disposition: disposition,
      filename: filename || blob.filename.to_s
    )
    response.headers['Content-Length'] = blob.byte_size.to_s

    # Essential security headers for file serving
    response.headers['X-Content-Type-Options'] = 'nosniff'
    response.headers['X-Frame-Options'] = 'DENY' if disposition == 'attachment'
    response.headers['Referrer-Policy'] = 'no-referrer'

    # CRITICAL SECURITY FIX: Add Content-Security-Policy to prevent XSS attacks
    # This prevents malicious files (like SVGs with scripts) from executing JavaScript
    if disposition == 'inline'
      # For inline content, prevent script execution while allowing basic content display
      # Use 'self' for images since they're served from our domain, but block all scripts
      response.headers['Content-Security-Policy'] = "default-src 'none'; img-src 'self'; style-src 'unsafe-inline'; sandbox;"
    else
      # For downloads (attachment), use the strictest possible policy
      response.headers['Content-Security-Policy'] = "default-src 'none';"
    end

    # Stream the blob data
    blob.download do |chunk|
      response.stream.write(chunk)
    end
  ensure
    response.stream.close if response.stream.present?
  end
end