class ProjectSharesController < ApplicationController
  before_action :authenticate_user! 
  before_action :set_project_share, only: %i[ show edit update destroy ]

  # GET /project_shares
  def index
    @project_shares = ProjectShare.all
  end

  # GET /project_shares/1
  def show
  end

  # GET /project_shares/new
  def new
    @project_share = ProjectShare.new
  end

  # GET /project_shares/1/edit
  def edit
  end

  # POST /project_shares
  def create
    @project_share = ProjectShare.new(project_share_params)

    if @project_share.save
      redirect_to @project_share, notice: "Project share was successfully created."
    else
      render :new, status: :unprocessable_entity
    end
  end

  # PATCH/PUT /project_shares/1
  def update
    if @project_share.update(project_share_params)
      redirect_to @project_share, notice: "Project share was successfully updated.", status: :see_other
    else
      render :edit, status: :unprocessable_entity
    end
  end

  # DELETE /project_shares/1
  def destroy
    @project_share.destroy
    redirect_to project_shares_url, notice: "Project share was successfully deleted.", status: :see_other
  end

  private
    # Use callbacks to share common setup or constraints between actions.
    def set_project_share
      @project_share = ProjectShare.find(params[:id])
    end

    # Only allow a list of trusted parameters through.
    def project_share_params
      params.require(:project_share).permit(:project_id, :user_id)
    end
end
