class RegistrationsController < Devise::RegistrationsController
  before_action :check_invite_only, only: [:new, :create]

  def create
    super
  end
  
  private

  # Invite only is set default to false. Change INVITE_ONLY env variable to true any time. 
  # This part also affetcs block to change a password
  def check_invite_only
    return unless ENV.fetch('INVITE_ONLY', 'false') == 'true'
    
    token = params.dig(:user, :invitation_token)
    existing_invitation = token.present? && User.find_by_invitation_token(token, true)
    
    unless existing_invitation
      redirect_to public_root_path, alert: 'Registration requires a valid invitation'
    end
  end

end