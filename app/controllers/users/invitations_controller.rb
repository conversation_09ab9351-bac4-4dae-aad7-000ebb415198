class Users::InvitationsController < Devise::InvitationsController
  include Devise::Controllers::Rememberable

  # Overriding the update action which processes the form when user sets their password
  # To add the remember_me functionality for invited users
  def update
    super do |resource|
      if resource.errors.empty? && params[:user] && params[:user][:remember_me] == '1'
        remember_me(resource)
      end
    end
  end
end