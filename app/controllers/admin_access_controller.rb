class AdminAccessController < ApplicationController
  before_action :authenticate_user!

  def index
    # Filter projects based on status parameter
    @status_filter = params[:status]

    @projects = case @status_filter
                when 'draft'
                  Project.drafts
                when 'pending'
                  Project.pending_approval
                when 'published'
                  Project.published
                else
                  Project.all
                end

    @projects = @projects.includes(:user).order(updated_at: :desc)

    # Get counts for status summary
    @status_counts = {
      total: Project.count,
      draft: Project.drafts.count,
      pending: Project.pending_approval.count,
      published: Project.published.count
    }
  end

  def destroy_project
    project = Project.find_by(id: params[:id])
    if project
      project.destroy
      redirect_to admin_dashboard_path, notice: "Project was successfully deleted."
    else
      redirect_to admin_dashboard_path, alert: "Project not found."
    end
  end
  
end