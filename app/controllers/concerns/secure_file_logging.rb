# SecureFileLogging Concern
#
# This concern provides secure logging functionality for file access operations,
# ensuring that sensitive file access patterns are not exposed in application logs
# while maintaining essential security audit information.
#
# Security Features:
# - Silences detailed file access logs to prevent log pollution
# - Logs only essential security information without file details
# - Prevents exposure of file hashes, tokens, or access patterns
# - Maintains audit trail for security monitoring
#
# Usage:
#   class PrivateFilesController < ApplicationController
#     include SecureFileLogging
#   end

module SecureFileLogging
  extend ActiveSupport::Concern
  
  included do
    # Silence detailed file access logs for security-sensitive actions
    around_action :silence_file_access_logs, only: [:stream_content, :request_file_token]
  end
  
  private
  
  # Silence file access logs to prevent sensitive data exposure
  # This prevents tokens, file hashes, and access patterns from appearing in logs
  def silence_file_access_logs
    Rails.logger.silence do
      yield
    end
  end
  
  # Log essential security information without exposing sensitive file details
  #
  # @param action [String] The security action being performed
  # @param project_id [String, Integer] The project ID (for audit purposes)
  # @param success [Boolean] Whether the action was successful
  def log_secure_access(action, project_id, success)
    # Log only essential security info without file details
    # This provides audit trail while maintaining security
    Rails.logger.info "[SECURE_FILE] User:#{current_user&.id} Action:#{action} Project:#{project_id} Success:#{success} IP:#{request.remote_ip}"
  end
  
  # Log security validation failures with context
  #
  # @param reason [String] The reason for validation failure
  # @param context [Hash] Additional context information (sanitized)
  def log_security_violation(reason, context = {})
    sanitized_context = context.except(:token, :file_hash, :secure_token)
    Rails.logger.warn "[SECURE_FILE] SECURITY_VIOLATION Reason:#{reason} User:#{current_user&.id} IP:#{request.remote_ip} Context:#{sanitized_context}"
  end
  
  # Log performance monitoring for DoS protection
  #
  # @param metric [String] The performance metric being tracked
  # @param value [Numeric] The measured value
  # @param threshold [Numeric] The warning threshold
  def log_performance_warning(metric, value, threshold)
    if value > threshold
      Rails.logger.warn "[SECURE_FILE] PERFORMANCE_WARNING Metric:#{metric} Value:#{value} Threshold:#{threshold} User:#{current_user&.id}"
    end
  end
end