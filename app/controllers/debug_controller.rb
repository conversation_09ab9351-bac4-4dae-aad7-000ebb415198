class DebugController < ApplicationController
  def heap_dump
    unless Rails.env.development?
      render plain: "Endpoint only available in development", status: :forbidden
      return
    end

    file_path = Rails.root.join("tmp", "heap-#{Time.now.to_i}.json")
    
    # Force a full garbage collection run before dumping the heap
    # to ensure we're only looking at objects that are truly retained.
    GC.start(full_mark: true, immediate_sweep: true)

    File.open(file_path, "w") do |file|
      ObjectSpace.dump_all(output: file)
    end
    
    render plain: "Heap dumped to #{file_path}"
  end
end 