# ABOUTME: Base controller for admin namespace providing shared authentication and authorization
# ABOUTME: All admin controllers inherit from this to ensure consistent access control
class Admin::BaseController < ApplicationController
  before_action :authenticate_user!
  before_action :ensure_admin!

  private

  def ensure_admin!
    redirect_to root_path, alert: "Access denied" unless current_user.admin?
  end
end