class NetworkConnectionsController < ApplicationController
  before_action :authenticate_user!
  before_action :set_network_connection, only: [:destroy]

  # Authorization: curent_user and scope with_connections_for takes care of which user can see the Summary in the this lists
  def index
    @user_profiles = if params[:filter] == 'all'
      UserProfile.with_connections_for(current_user.id)
    else
      UserProfile.without_connections_for(current_user.id)
    end

    # Handle duplicate UserProfile records by grouping by user_id
    # This is a temporary fix until database constraints are added
    @user_profiles = @user_profiles.group_by(&:user_id).map { |_, profiles| profiles.first }
    
    # Convert back to ActiveRecord::Relation-like structure
    @user_profiles = @user_profiles.select { |profile| profile.last_name.present? }
    
    # Apply filters
    @user_profiles = @user_profiles.to_a

    # Apply name filter
    if params[:name].present?
      query = params[:name].downcase
      @user_profiles = @user_profiles.select do |profile|
        profile.first_name&.downcase&.include?(query) || 
        profile.last_name&.downcase&.include?(query)
      end
    end

    # Apply location filter
    if params[:location].present? && params[:location] != 'Any Location'
      location_query = params[:location].downcase
      @user_profiles = @user_profiles.select do |profile|
        profile.city&.downcase&.include?(location_query) || 
        profile.country&.downcase&.include?(location_query)
      end
    end
    
    # Sort by connections count (since we lost the ORDER BY when converting to array)
    @user_profiles = @user_profiles.sort_by { |profile| -(profile.user.connections_count || 0) }

    # Preload connection data to avoid N+1 queries
    preload_connection_data
  end
  
  # Retrieves and orders network connections for the current user
  # Authorization: current_user
  def my
    @connections = NetworkConnection.for_user(current_user.id)
      .joins("LEFT JOIN users inviter ON network_connections.inviter_id = inviter.id")
      .joins("LEFT JOIN users invitee ON network_connections.invitee_id = invitee.id")
      .includes(inviter: :user_profile, invitee: :user_profile)
      .select("network_connections.*,
              GREATEST(COALESCE(inviter.connections_count, 0),
                      COALESCE(invitee.connections_count, 0)) as max_connections")
      .order("max_connections DESC")

    render :index
  end

  def invite
    @sent_invitations = User.where(invited_by_id: current_user.id)
    render :index
  end

  def resend_invitation
    # current_user is the inviter
    user = User.find(params[:id])
    user.invite!(current_user)
  end

  # Authorization via model method: user is either inviter or invitee
  # inviter_id == user_id || invitee_id == user_id
  def destroy
    if @network_connection.belongs_to_user?(current_user.id)
      other_user_id = @network_connection.connected_user(current_user.id).id
      
      # current_user has access to other_user's projects
      # other_user has access to current_user's projects
      project_auths = ProjectAuth.joins(:project)
          .where("(project_auths.user_id = ? AND projects.user_id = ?) OR (project_auths.user_id = ? AND projects.user_id = ?)", 
                current_user.id, other_user_id,     
                other_user_id, current_user.id)     
          .where("project_auths.user_id != projects.user_id")

      connection_request = ConnectionRequest.where(inviter_id: @network_connection.inviter_id, invitee_id: current_user.id)
                        .or(ConnectionRequest.where(inviter_id: current_user.id, invitee_id: @network_connection.inviter_id))
      
      ActiveRecord::Base.transaction do
        project_auths.destroy_all
        connection_request.destroy_all
        @network_connection.destroy!
        redirect_to network_connections_path, notice: 'Connection removed!'
      rescue ActiveRecord::RecordNotDestroyed => e
        redirect_to network_connections_path, alert: 'Failed to remove connection.'
      end
    else
      redirect_to network_connections_path, alert: 'Not authorized!'
    end
  end
  

  private

  def set_network_connection
    @network_connection = NetworkConnection.find(params[:id])
  end

  def authorize_invitation_response
    unless @network_connection.inviter == current_user
      redirect_to network_connections_path, alert: 'Not authorized!'
    end
  end

  def network_connection_params
    params.require(:network_connection).permit(:invitee_id)
  end

  # Preload all connection data to avoid N+1 queries in the view
  def preload_connection_data
    user_ids = @user_profiles.map(&:user_id)
    
    # Preload existing connections - 1 query instead of N queries
    @connected_user_ids = NetworkConnection.where(
      "(inviter_id = ? AND invitee_id IN (?)) OR (invitee_id = ? AND inviter_id IN (?))",
      current_user.id, user_ids, current_user.id, user_ids
    ).pluck(:inviter_id, :invitee_id)
     .map { |inviter_id, invitee_id| 
       inviter_id == current_user.id ? invitee_id : inviter_id 
     }.to_set

    # Preload connection request statuses - 1 query instead of N queries  
    @connection_statuses = ConnectionRequest.where(
      inviter_id: current_user.id,
      invitee_id: user_ids
    ).pluck(:invitee_id, :status).to_h
  end

  # Helper methods for cleaner view logic
  helper_method :user_connected?, :user_connection_pending?

  def user_connected?(user_id)
    @connected_user_ids&.include?(user_id) || false
  end

  def user_connection_pending?(user_id)
    @connection_statuses&.[](user_id) == 0
  end

end
