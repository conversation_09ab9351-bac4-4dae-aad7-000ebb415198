class PrivateFilesController < ApplicationController
  include SecureFileLogging
  
  before_action :authenticate_user!, except: [:stream_content]
  before_action :set_project, except: [:stream_content]
  before_action :authorize_access, except: [:stream_content]
  
  # Not using this action
  # def show
  #   #redirect_to rails_blob_url(@private_file, disposition: "inline")
    
  #   # Using this route for showing images with image_tag - Rails Active Storage guides
  #   redirect_to url_for(@private_file)
  # end

  #TODO: close this one when stream will be working fine
  # def download
  #   #redirect_to url_for(@private_file)
  #   #redirect_to @private_file.url

  #   # Little bit hacked way of downloading private files, can be used for show as well
  #   # Needs an active_storage custom config file to be created (already there) in config/initializers/active_storage.rb
  #   blob_url = @private_file.url(
  #     disposition: "attachment",
  #     filename: @private_file.filename.to_s,
  #     expires_in: 1.minute
  #   )
  #   redirect_to blob_url, allow_other_host: true  
  # end

  # DEPRECATED - Use stream_content with tokens instead (Chunk 9)
  # def secure_download
  #   send_data_with_stream
  # end

  # Stream content for inline display (e.g., PDFs, images in browser)
  # This action now supports both legacy (with file ID) and new token-based access
  def stream_content
    # Check if this is a token-based request
    token = params[:t]
    
    if token.present?
      # New token-based streaming (no authentication needed here, token validates everything)
      return handle_token_based_streaming(token)
    else
      # CRITICAL SECURITY FIX: Legacy file ID based streaming (requires authentication)
      # Manually invoke authentication and authorization checks that were skipped
      # by the controller-level `except: [:stream_content]` clause
      authenticate_user!
      set_project
      authorize_access
      send_data_with_stream(disposition: 'inline')
    end
  end

  private

  def send_data_with_stream(disposition: 'attachment')
    # CRITICAL SECURITY FIX: Add security headers to prevent XSS attacks
    add_security_headers_for_file_serving(disposition, @private_file.content_type)
    
    response.headers['Content-Type'] = @private_file.content_type
    response.headers['Content-Disposition'] = ActionDispatch::Http::ContentDisposition.format(
      disposition: disposition,
      filename: @private_file.filename.to_s
    )

    @private_file.blob.download do |chunk|
      response.stream.write(chunk)
    end
  ensure
    response.stream.close if response.stream.present?
  end

  def set_project
    @project = Project.find(params[:project_id])
    @private_file = @project.private_files.find(params[:id])
  end

  # Authorization current_user can get project files only if he has explicit ProjectAuth
  def authorize_access
    authorize!(@project, to: :view_full_details?)
  end

  # Handle token-based streaming with full authorization checks
  def handle_token_based_streaming(token)
    # CRITICAL SECURITY: Validate token is bound to current session
    unless SecureFileTokenService.validate_token_with_session(token, request)
      log_secure_access('stream_invalid_session', 'unknown', false)
      return head :forbidden
    end
    
    payload = SecureFileTokenService.decode_token(token)
    unless payload
      log_secure_access('stream_invalid_token', 'unknown', false)
      return head :forbidden
    end
    
    user = User.find_by(id: payload['user_id'])
    file = ActiveStorage::Attachment.find_by(id: payload['file_id'])

    # Security: Ensure file exists and is associated with the correct project from the token.
    # This prevents using a valid token for one project to access a file from another
    # if file IDs are reused after deletion.
    unless user && file && file.record_type == 'Project' && file.record_id == payload['project_id']
      log_secure_access('stream_invalid_file_association', payload&.dig('project_id') || 'unknown', false)
      return head :forbidden
    end
    
    project = file.record
    
    # Use unified authorization logic through ActionPolicy
    # This ensures consistency with the legacy authorization flow
    unless allowed_to?(:view_full_details?, project, context: { user: user })
      log_secure_access('stream_authorization_denied', payload['project_id'], false)
      return head :forbidden
    end
    
    # Prevent any caching
    response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate, max-age=0'
    response.headers['Pragma'] = 'no-cache'
    response.headers['Expires'] = '0'
    
    # CRITICAL SECURITY FIX: Add security headers to prevent XSS attacks
    add_security_headers_for_file_serving('inline', file.content_type)
    
    response.headers['Content-Type'] = file.content_type
    response.headers['Content-Disposition'] = ActionDispatch::Http::ContentDisposition.format(
      disposition: 'inline',
      filename: file.filename.to_s
    )
    
    # Log successful access before streaming
    log_secure_access('stream_success', payload['project_id'], true)
    
    # Stream file content
    file.blob.download do |chunk|
      response.stream.write(chunk)
    end
  rescue ActiveRecord::RecordNotFound => e
    log_secure_access('stream_record_not_found', payload&.dig('project_id') || 'unknown', false)
    head :forbidden
  rescue => e
    log_secure_access('stream_error', payload&.dig('project_id') || 'unknown', false)
    Rails.logger.error "[SECURE_FILE] Streaming error: #{e.class.name}"
    head :internal_server_error
  ensure
    response.stream.close if response.stream.present?
  end

  # Add comprehensive security headers for file serving to prevent XSS and MIME confusion
  def add_security_headers_for_file_serving(disposition, content_type)
    # Prevent MIME type sniffing which can lead to XSS
    response.headers['X-Content-Type-Options'] = 'nosniff'
    
    # Prevent framing to mitigate clickjacking
    response.headers['X-Frame-Options'] = 'DENY'
    
    # Add strict Content Security Policy to prevent script execution
    if disposition == 'inline'
      # --- BEGIN CSP FIX ---
      if content_type == 'application/pdf'
        # For PDFs, a less restrictive policy is needed for the viewer to work.
        # The frontend already places this in a sandboxed iframe, which is the primary defense.
        response.headers['Content-Security-Policy'] = "default-src 'none'; object-src 'self'; style-src 'unsafe-inline';"
      else
        # For all other inline types (especially images like SVG), keep the strongest possible policy.
        response.headers['Content-Security-Policy'] = "default-src 'none'; style-src 'unsafe-inline'; sandbox;"
      end
      # --- END CSP FIX ---
    else
      # For downloads, prevent any execution
      response.headers['Content-Security-Policy'] = "default-src 'none';"
    end
    
    # Additional security headers
    response.headers['X-XSS-Protection'] = '1; mode=block'
    response.headers['Referrer-Policy'] = 'no-referrer'
  end
  
end