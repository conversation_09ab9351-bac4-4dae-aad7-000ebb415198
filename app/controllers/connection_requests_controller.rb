class ConnectionRequestsController < ApplicationController
  before_action :authenticate_user!
  
  # Authorization: current_user 
  # For now using only project requests 
  def count
    if current_user
      result = ActiveRecord::Base.connection.execute(<<-SQL).first
        SELECT 
          COUNT(*) AS total_count,
          EXISTS(SELECT 1 FROM connection_requests 
                WHERE invitee_id = #{current_user.id} 
                AND status = 0 
                AND project_id IS NOT NULL
                LIMIT 1) AS has_project_requests,
          EXISTS(SELECT 1 FROM connection_requests 
                WHERE invitee_id = #{current_user.id} 
                AND status = 0 
                AND project_id IS NULL
                LIMIT 1) AS has_network_requests
        FROM connection_requests
        WHERE invitee_id = #{current_user.id} AND status = 0
      SQL
      puts " \n---------#{result} --------- \n\n "
      render json: { 
        count: result['total_count'], 
        has_project_requests: result['has_project_requests'] == true, 
        has_network_requests: result['has_network_requests'] == true 
      }
    else
      render json: { count: 0 }
    end 
  end

  # Authorization: current_user level 
  def index
    # current_user is an invitee who is going to handle approve/reject
    # We need to get @all_requests where invitee_id is current_user.id
    # To it we need to include: user_profiles of inviter and current_user's projects (if any) - that is where project.user_id is curent user
    # project_requests = @all_requests.joins(:project).where(projects: { user_id: current_user.id })
    # connection_requests = @all_requests.includes(:user_profile).where(project_id: nil)
    @all_requests = ConnectionRequest
      .joins("LEFT JOIN projects ON projects.id = connection_requests.project_id AND projects.user_id = #{current_user.id}")
      .joins("LEFT JOIN users AS inviters ON inviters.id = connection_requests.inviter_id")
      .includes(inviter: :user_profile)
      .where(invitee_id: current_user.id, status: 'pending')
      .order(created_at: :desc)

    puts "------index:   ----#{@all_requests.inspect}--------- \n\n "
  end

  # Authorization: current_user must be invitee or inviter to see the request
  def show
    @connection_request = ConnectionRequest.includes(inviter: :user_profile).find(params[:id])
    authorize! @connection_request
    
    render partial: 'request_detail', layout: false
  end

  # No authorization as user profiles are public
  def user_profile
    @user_profile = UserProfile.find(params[:id])
    
    #fuckngpolicy doesnt work again
    @can_view_contact_details = allowed_to?(:view_contact_details?, @user_profile)

    #@is_connected = @user_profile.connected_with?(current_user.id)
    #@visible_profile_data = @user_profile.visible_attributes_for(current_user.id)
    
    render partial: 'user_profile', layout: false
  end

  # Inviter requests project authorization to see the full details of the Project
  # Inviter can request any user
  # Inviter is limited to Project Auth request when he sees the Project (in Network or when Semipublic Project)
  def new
    # Form to write message
    permitted_params = params.permit(:project_id, :invitee_id)
    project = Project.find(permitted_params[:project_id]) if permitted_params[:project_id]
    user = User.find(permitted_params[:invitee_id]) if permitted_params[:invitee_id]
    
    if project
      @project_auth_request = ConnectionRequest.new(project: project)
      authorize! @project_auth_request, to: :new?

      render partial: 'new_auth_request', layout: false
    elsif user
      @user_profile = user.user_profile
      @network_request = ConnectionRequest.new(
        invitee_id: user.id,
      )
      render partial: 'new_network_request', layout: false
    else
      head :not_found
    end
  end

  # Inviter = "requester", invitee = project owner
  # Inviter can create request - same as new
  # But at the creation of the connection_request, ProjectAuth "pending" is created!
  def create_auth_request
    @project_auth_request = ConnectionRequest.new(auth_request_params)

    authorize! @project_auth_request, to: :create_auth_request?

    @project_auth_request.inviter = current_user
    @project_auth_request.invitee_id = @project_auth_request.project.user_id
    if @project_auth_request.save
      ProjectAuth.create!(
        project: @project_auth_request.project,
        user: @project_auth_request.inviter,
        access_level: 'pending'
      )
      NotificationMailer.access_request_notification(@project_auth_request.project, current_user).deliver_later
      redirect_to projects_path, notice: t('connection_requests.create.success', default: 'Access request sent!')
    else
      alert = @project_auth_request.errors.full_messages.to_sentence
      redirect_to projects_path, alert: t('connection_requests.create.error', default: "Failed to send access request. #{alert} ")
    end
  end

  # Current_user is Invitee = project owner accepts the request
  # and ProjectAuth IS UPDATED WITH FULL DETAILS ACCESS! 
  # Authorization: Current_user is authorized if he is the project.user_id
  def accept_auth_request
    @project_auth_request = ConnectionRequest.find(params[:id])
    #authorize! @project_auth_request.project
    authorize! @project_auth_request, to: :accept_auth_request?
    
    if @project_auth_request.update(status: 'approved')
      @project_auth = ProjectAuth.find_by(project_id: @project_auth_request.project_id, user_id: @project_auth_request.inviter_id)
      
      @project_auth.approve_full!
      NotificationMailer.approved_access_notification(@project_auth_request.project, @project_auth_request.inviter, current_user).deliver_later
      redirect_to connection_requests_path, notice: t('connection_requests.accept.success', default: 'Access request approved!')
    else
      alert = @project_auth_request.errors.full_messages.to_sentence
      redirect_to connection_requests_path, alert: t('connection_requests.accept.error', default: "Failed to approve access request. #{alert}")
    end
  end

  # Current_user is Invitee = project owner rejects the request
  # When rejected ConnectionRequest, ProjectAuth is UPDATED TO DENIED
  # Authorization: Current_user - invitee is authorized if he is the project.user_id
  def reject_auth_request
    @project_auth_request = ConnectionRequest.find(params[:id])
    authorize! @project_auth_request, to: :reject_auth_request?
    
    if @project_auth_request.update(status: 'declined')
      @project_auth = ProjectAuth.find_by(project_id: @project_auth_request.project_id, user_id: @project_auth_request.inviter_id)
      @project_auth.reject!
      redirect_to connection_requests_path, notice: t('connection_requests.decline.success', default: 'Access request rejected!')
    else
      alert = @project_auth_request.errors.full_messages.to_sentence
      redirect_to connection_requests_path, alert: t('connection_requests.decline.error', default: "Failed to reject access request. #{alert}")
    end
  end

  # Current_user is Inviter requests connection from another user - invitee
  def create_network_request
    @network_request = ConnectionRequest.new(connection_request_params)
    @network_request.inviter = current_user
    if @network_request.save
      redirect_to network_connections_path, notice: t('connection_requests.create.success', default: 'Connection request sent!')
    else
      alert = @network_request.errors.full_messages.to_sentence
      redirect_to network_connections_path, alert: t('connection_requests.create.error', default: "Unable to send connection request. #{alert}")
    end
  end

  # Current_user is Invitee accepts the connection request
  # which will create a NetworkConnection from inviter to invitee but will be reciprocal
  # Authorization: Current_user is authorized if he is the invitee_id
  def accept_network_request
    @network_request = ConnectionRequest.find(params[:id])
    authorize! @network_request

    # Check if connection already exists to provide a friendly message
    if NetworkConnection.exists?(inviter_id: @network_request.inviter.id, invitee_id: current_user.id)
      @network_request.update(status: 'approved')
      redirect_to connection_requests_path, notice: t('connection_requests.accept.already_connected', default: 'You are already connected with this user.')
      return
    end

    if @network_request.update(status: 'approved')
      begin
        NetworkConnection.create!(
          inviter_id: @network_request.inviter.id,
          invitee_id: current_user.id
        )
        redirect_to connection_requests_path, notice: t('connection_requests.accept.success', default: 'Connection request approved.')
      rescue ActiveRecord::RecordInvalid => e
        # Handle any other validation errors gracefully
        redirect_to connection_requests_path, alert: t('connection_requests.accept.error', default: e.record.errors.full_messages.to_sentence)
      end
    else
      alert = @network_request.errors.full_messages.to_sentence
      redirect_to connection_requests_path, alert: t('connection_requests.accept.error', default: "Failed to approve connection request. #{alert}")
    end
  end

  # Authorization: same as accept
  def reject_network_request
    @network_request = ConnectionRequest.find(params[:id])
    authorize! @network_request
    
    if @network_request.update(status: 'declined')
      redirect_to connection_requests_path, notice: t('connection_requests.decline.success', default: 'Connection request rejected.')
    else
      alert = @network_request.errors.full_messages.to_sentence
      redirect_to connection_requests_path, alert: t('connection_requests.decline.error', default: "Failed to reject connection request. #{alert}")
    end
  end

  # Current_user is Inviter and can destroy his own request
  # Authorization: current_user can destroy his own request when he is the inviter
  def destroy
    @project_auth_request = ConnectionRequest.find_by(project_id: params[:id], inviter_id: current_user.id)
    @project_auth = ProjectAuth.find_by(project_id: params[:id], user_id: current_user.id)

    puts " \n-----destroy: project_auth_request:   ----#{@project_auth_request.inspect}--------- \n\n "
    
    puts " \n-----destroy: project_auth:   ----#{@project_auth.inspect}--------- \n\n "
    authorize! @project_auth_request
    
    #FIXME: destroy project auth and connection_request 
    #FIXME: allow user to destroy already approved request
    if @project_auth_request && @project_auth
      @project_auth_request.destroy
      @project_auth.destroy
      redirect_to network_connections_path, notice: t('connection_requests.destroy.success', default: 'Request deleted.')
    else
      redirect_to network_connections_path, alert: t('connection_requests.destroy.error', default: 'Unable to delete request.')
    end
  end


  private

  def connection_request_params
    params.require(:connection_request).permit(:invitee_id, :message)
  end 

  def auth_request_params
    params.require(:connection_request).permit(:project_id, :message)
  end 

end