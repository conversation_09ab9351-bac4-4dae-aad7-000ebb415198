# ABOUTME: User-facing controller for referral code redemption
# ABOUTME: Allows authenticated users to redeem referral codes for subscription upgrades
class ReferralCodesController < ApplicationController
  before_action :authenticate_user!
  
  def redeem
    code_string = params[:code]&.strip&.upcase
    
    if code_string.blank?
      redirect_back(fallback_location: root_path, alert: "Please enter a referral code")
      return
    end
    
    referral_code = ReferralCode.find_by(code: code_string)
    
    if referral_code.nil?
      redirect_back(fallback_location: root_path, alert: "Invalid referral code")
      return
    end
    
    begin
      referral_code.redeem!(current_user)
      
      # Success message based on the tier upgrade
      tier_name = referral_code.tier_upgrade_to.humanize
      if referral_code.tier_upgrade_to == 'vanguard'
        success_message = "🎉 Congratulations! Your account has been upgraded to #{tier_name} with permanent access!"
      else
        duration = pluralize(referral_code.duration_months, 'month')
        success_message = "🎉 Congratulations! Your account has been upgraded to #{tier_name} for #{duration}!"
      end
      
      redirect_back(fallback_location: root_path, notice: success_message)
      
    rescue Errors::CodeExpiredError
      error_message = "This referral code has expired"
    rescue Errors::CodeLimitReachedError
      error_message = "This referral code has been used up"
    rescue Errors::CodeNotActiveError
      error_message = "This referral code is not active"
    rescue Errors::UserAlreadyPremiumError
      error_message = "You already have premium access"
    rescue Errors::RedemptionError => e
      # Catch other potential redemption errors
      error_message = "Unable to redeem code: #{e.message}"
    rescue => e
      # Catch any other unexpected errors
      error_message = "An unexpected error occurred. Please try again."
      
      redirect_back(fallback_location: root_path, alert: error_message)
    end
  end
  
  # Display page for code redemption (optional standalone page)
  def new
    @referral_code = ReferralCode.new
  end
end