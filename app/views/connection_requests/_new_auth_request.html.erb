<h3><%= t('common.titles.project_access_request') %></h3>
<%= form_with(model: @connection_request,
              scope: :connection_request,
              url: create_auth_request_connection_requests_path,
              local: true) do |form| %>
  <div class="field">
    <%= form.hidden_field :project_id, value: @project_auth_request.project_id if @project_auth_request&.project_id.present? %>
    <%= form.label :message, t('common.labels.message') %>
    <%= form.text_area :message, rows: 5 %>
  </div>

  <div class="actions">
    <%= form.submit t('common.actions.send') %>
  </div>
<% end %>