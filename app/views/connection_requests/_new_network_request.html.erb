<h3><%= t('common.titles.connect_with_unlisters_network') %></h3>
<h4><%= @user_profile.first_name %> <%= @user_profile.last_name %></h4>
<%= form_with(model: @connection_request,
              scope: :connection_request,
              url: create_network_request_connection_requests_path,
              local: true) do |form| %>
  <div class="field">
    <%= form.hidden_field :invitee_id, value: @network_request.invitee_id if @network_request&.invitee_id.present? %>
    <%= form.label :message, t('common.labels.message') %>
    <%= form.text_area :message, rows: 5, placeholder: t('common.actions.write_a_short_message') %>
  </div>

  <div class="actions">
    <%= form.submit t('common.actions.send') %>
  </div>
<% end %>