<div class="main-box">
  <div class="card">
    <div class="card-upper-actions">
    </div>
    <div class="card-profile">
      <div class="profile-avatar">
        <% initials = "#{@user_profile.first_name&.first}#{@user_profile.last_name&.first}".upcase %>
        <%= content_tag :div, initials, class: 'avatar-circle' %>
      </div>
      <div class="info">
        <div class="name">
          <%= @user_profile.first_name %> <%= @user_profile.last_name %>
        </div>
        <div class="location">
          <%= [@user_profile.city, @user_profile.country].reject(&:blank?).join(', ') %>
        </div>
      </div>
    </div>
    <div class="card-text">

      <% if @can_view_contact_details || @is_connected %>

        <div class="mb-1">
          <strong><%= t('user_profiles.show.email') %>:</strong>
           <%= @user_profile.email %>
        </div>
        <% if @user_profile.phone.present? %>
          <div class="mb-1">
            <strong><%= t('models.user_profile.attributes.phone') %>:</strong>
            <%= @user_profile.phone %></p>
          </div>
        <% end %>

      <% else %>

        <div class="mb-1">
          <strong><%= t('user_profiles.show.email') %>:</strong>
          <em><%= t('user_profiles.show.hidden', default: 'hidden') %></em>
        </div>
        <div class="mb-1">
          <strong><%= t('models.user_profile.attributes.phone') %>:</strong>
          <em><%= t('user_profiles.show.hidden', default: 'hidden') %></em>
        </div>

      <% end %>

        <div class="mb-1">
          <strong><%= t('models.user_profile.attributes.bio') %>:</strong>
          <%= simple_format(h(@user_profile.bio)) %>
        </div>
    </div>
    <div class="card-message">
      
    </div>
    <div class="card-lower-actions">
    </div>
  </div>
  
</div>