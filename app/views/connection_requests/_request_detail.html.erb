
<div class="main-box">
  <div class="card">
    <div class="card-upper-actions">
    </div>
    <div class="card-profile">
      <div class="info">
        <div class="name">
          <h3>
            <% if @connection_request.project_id.present? %>
              <%= t('common.texts.is_requesting_access_to_your_project', default: 'Requesting access to your project') %>
            <% else %>
              <%= t('common.texts.would_like_to_connect_with_you', default: 'Would like to connect with you') %>
            <% end %>
          </h3>
        </div>
        <div class="name">
          <h4>
            <%= @connection_request.inviter.user_profile.first_name %>
            <%= @connection_request.inviter.user_profile.last_name %>  
          </h4>
        </div>
        <div class="data">
          <%= [@connection_request.inviter.user_profile.city, @connection_request.inviter.user_profile.country].reject(&:blank?).join(', ') %>
        </div>
        <div class="data">
          <%= @connection_request.inviter.user_profile.email %>
        </div>
        <div class="data">
          <%= @connection_request.inviter.user_profile.phone %>
        </div>
        <div class="data">
          <%= @connection_request.inviter.user_profile.bio %>
        </div>
      </div>
    </div>
    <div class="card-message">
      <%= simple_format(h(@connection_request.message)) %>
    </div>
    <div class="card-lower-actions">
      <% if @connection_request.project %>
        <% if @connection_request.project.user_id == current_user.id && @connection_request.status == "pending" %>
          <%= button_to t('common.actions.grant_project_access', default: 'Grant Project Access'), 
              accept_auth_request_connection_request_path(@connection_request), 
              method: :post, 
              class: "action-button action-button--primary" %>
          <%= button_to t('common.actions.reject', default: 'Reject'), 
              reject_auth_request_connection_request_path(@connection_request), 
              method: :post, 
              class: "action-button action-button--reject", 
              data: { confirm: t('common.confirmations.reject_request', default: 'Are you sure you want to reject this request?') } %>
        <% end %>
      <% else %>
        <%= button_to t('common.actions.accept', default: 'Accept'), 
            accept_network_request_connection_request_path(@connection_request), 
            method: :post, 
            class: "action-button action-button--primary" %>
        <%= button_to t('common.actions.reject', default: 'Reject'), 
            reject_network_request_connection_request_path(@connection_request), 
            method: :post, 
            class: "action-button action-button--reject", 
            data: { confirm: t('common.confirmations.reject_request', default: 'Are you sure you want to reject this request?') } %>
      <% end %>
    </div>
  </div>
  
</div>
