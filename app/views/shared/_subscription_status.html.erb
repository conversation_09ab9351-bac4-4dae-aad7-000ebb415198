<!-- ABOUTME: User subscription status display component -->
<!-- ABOUTME: Shows current tier, expiration, and upgrade prompts for user dashboard -->

<div class="card subscription-status-card">
  <div class="card-body">
    <div class="d-flex justify-content-between align-items-start">
      <div class="subscription-info">
        <h6 class="card-title mb-2">
          <i class="fas fa-crown text-warning me-2"></i>
          Subscription Status
        </h6>
        
        <div class="tier-badge-container mb-2">
          <span class="badge badge-<%= tier_badge_class(current_user.subscription_tier) %> badge-lg">
            <%= current_user.subscription_tier.humanize %> Plan
          </span>
          
          <% if current_user.active_subscription? %>
            <span class="badge badge-success ms-2">
              <i class="fas fa-check-circle"></i> Active
            </span>
          <% elsif current_user.tier_premium? && current_user.subscription_expired? %>
            <span class="badge badge-warning ms-2">
              <i class="fas fa-exclamation-triangle"></i> Expired
            </span>
          <% end %>
        </div>

        <!-- Expiration Info -->
        <% if current_user.tier_premium? && current_user.subscription_expires_at.present? %>
          <div class="expiry-info">
            <small class="text-muted">
              <% if current_user.subscription_expires_at > Time.current %>
                <% days_remaining = (current_user.subscription_expires_at.to_date - Date.current).to_i %>
                <i class="fas fa-calendar-alt"></i>
                Expires <%= current_user.subscription_expires_at.strftime("%B %d, %Y") %>
                <% if days_remaining <= 7 %>
                  <span class="text-warning">(<%= pluralize(days_remaining, 'day') %> remaining)</span>
                <% end %>
              <% else %>
                <i class="fas fa-times-circle text-danger"></i>
                Expired on <%= current_user.subscription_expires_at.strftime("%B %d, %Y") %>
              <% end %>
            </small>
          </div>
        <% elsif current_user.tier_beta? %>
          <div class="permanent-access">
            <small class="text-muted">
              <i class="fas fa-infinity"></i> Permanent access
            </small>
          </div>
        <% end %>

        <!-- Feature Access Summary -->
        <div class="feature-summary mt-2">
          <small class="text-muted">
            <% if current_user.active_subscription? %>
              <i class="fas fa-check text-success"></i> Full platform access
              <br>
              <i class="fas fa-check text-success"></i> Create unlimited projects
              <br>
              <i class="fas fa-check text-success"></i> Upload files
            <% else %>
              <i class="fas fa-times text-danger"></i> Limited access
              <br>
              <i class="fas fa-times text-danger"></i> Cannot create projects
              <br>
              <i class="fas fa-times text-danger"></i> Cannot upload files
            <% end %>
          </small>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="subscription-actions">
        <% if current_user.tier_free? %>
          <div class="text-center">
            <button type="button" class="btn btn-primary btn-sm" data-toggle="modal" data-target="#upgradeModal">
              <i class="fas fa-arrow-up"></i> Upgrade
            </button>
            <br>
            <button type="button" class="btn btn-link btn-sm text-muted" data-toggle="modal" data-target="#redeemCodeModal">
              Have a code?
            </button>
          </div>
        <% elsif current_user.tier_premium? && current_user.subscription_expired? %>
          <div class="text-center">
            <button type="button" class="btn btn-warning btn-sm" data-toggle="modal" data-target="#renewModal">
              <i class="fas fa-refresh"></i> Renew
            </button>
            <br>
            <button type="button" class="btn btn-link btn-sm text-muted" data-toggle="modal" data-target="#redeemCodeModal">
              Have a code?
            </button>
          </div>
        <% elsif current_user.active_subscription? %>
          <div class="text-center">
            <span class="text-success">
              <i class="fas fa-check-circle"></i>
            </span>
            <br>
            <small class="text-muted">Active</small>
          </div>
        <% end %>
      </div>
    </div>
  </div>
</div>

<!-- Upgrade Modal -->
<div class="modal fade" id="upgradeModal" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">
          <i class="fas fa-crown text-warning"></i>
          Upgrade to Premium
        </h5>
        <button type="button" class="close" data-dismiss="modal">
          <span>&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="text-center">
          <h4 class="text-primary">Premium Plan Benefits</h4>
          <ul class="list-unstyled mt-3">
            <li class="mb-2">
              <i class="fas fa-check text-success"></i>
              Create unlimited projects
            </li>
            <li class="mb-2">
              <i class="fas fa-check text-success"></i>
              Upload files and documents
            </li>
            <li class="mb-2">
              <i class="fas fa-check text-success"></i>
              Full network access
            </li>
            <li class="mb-2">
              <i class="fas fa-check text-success"></i>
              Priority support
            </li>
          </ul>
          
          <div class="alert alert-info">
            <i class="fas fa-info-circle"></i>
            Premium subscriptions will be available soon! Contact support for early access.
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
        <button type="button" class="btn btn-primary" disabled>
          Coming Soon
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Renew Modal (for expired premium users) -->
<div class="modal fade" id="renewModal" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">
          <i class="fas fa-refresh text-warning"></i>
          Renew Premium Subscription
        </h5>
        <button type="button" class="close" data-dismiss="modal">
          <span>&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="text-center">
          <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle"></i>
            Your Premium subscription has expired. Renew now to regain full access.
          </div>
          
          <div class="alert alert-info">
            <i class="fas fa-info-circle"></i>
            Subscription renewal will be available soon! Contact support for assistance.
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
        <button type="button" class="btn btn-warning" disabled>
          Coming Soon
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Redeem Code Modal -->
<div class="modal fade" id="redeemCodeModal" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">
          <i class="fas fa-ticket-alt text-primary"></i>
          Redeem Referral Code
        </h5>
        <button type="button" class="close" data-dismiss="modal">
          <span>&times;</span>
        </button>
      </div>
      <%= form_with url: redeem_referral_code_path, method: :post, local: true do |form| %>
        <div class="modal-body">
          <div class="form-group">
            <%= form.label :code, "Enter your referral code:" %>
            <%= form.text_field :code, 
                class: "form-control", 
                placeholder: "e.g., WELCOME2024",
                required: true,
                style: "text-transform: uppercase;" %>
            <small class="form-text text-muted">
              Enter the referral code exactly as provided to you.
            </small>
          </div>
          
          <div class="alert alert-info">
            <i class="fas fa-info-circle"></i>
            <strong>How it works:</strong>
            <ul class="mb-0 mt-2">
              <li>Enter your valid referral code</li>
              <li>Your account will be upgraded automatically</li>
              <li>Enjoy premium features immediately</li>
            </ul>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
          <%= form.submit "Redeem Code", class: "btn btn-primary" %>
        </div>
      <% end %>
    </div>
  </div>
</div>

<style>
.subscription-status-card {
  border-left: 4px solid #007bff;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.badge-lg {
  font-size: 0.9em;
  padding: 0.5em 0.75em;
}

.tier-badge-container {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.subscription-actions {
  min-width: 80px;
}

.feature-summary {
  max-width: 250px;
}
</style>