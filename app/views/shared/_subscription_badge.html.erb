<!-- ABOUTME: Compact subscription status badge for navigation or sidebar use -->
<!-- ABOUTME: Shows tier status with quick upgrade option for free users -->

<div class="subscription-badge-container">
  <div class="d-flex align-items-center">
    <span class="badge badge-<%= tier_badge_class(current_user.subscription_tier) %> mr-2">
      <% case current_user.subscription_tier %>
      <% when 'free' %>
        <i class="fas fa-user"></i> Free
      <% when 'premium' %>
        <i class="fas fa-star"></i> Premium
      <% when 'beta' %>
        <i class="fas fa-flask"></i> Beta
      <% end %>
    </span>
    
    <% if current_user.tier_premium? && current_user.subscription_expires_at.present? %>
      <% days_remaining = (current_user.subscription_expires_at.to_date - Date.current).to_i if current_user.subscription_expires_at > Time.current %>
      <% if days_remaining && days_remaining <= 7 %>
        <small class="text-warning">
          <i class="fas fa-clock"></i> <%= days_remaining %>d
        </small>
      <% elsif current_user.subscription_expired? %>
        <small class="text-danger">
          <i class="fas fa-times-circle"></i> Expired
        </small>
      <% end %>
    <% end %>
    
    <% if current_user.tier_free? %>
      <button type="button" class="btn btn-link btn-sm p-0 ml-2 text-primary" data-toggle="modal" data-target="#redeemCodeModal" title="Upgrade account">
        <i class="fas fa-arrow-up"></i>
      </button>
    <% end %>
  </div>
</div>