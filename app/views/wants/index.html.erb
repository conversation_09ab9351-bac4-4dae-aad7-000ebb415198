<div id="wants">
  <div class="tab-content">
    <div class="flex g1 justify-start mb-1">
      <div class="flex items-center">
        <%= link_to new_want_path, class:'button-prominent' do %>
          <span class="flex items-center">
            <%= heroicon "plus-circle", variant: :solid, options: { class: "icon-24 mr-2" } %>
            <%= t('wants.my.add') %>
          </span>
        <% end %>
      </div>
    </div>

    <% if params[:controller] == 'wants' && params[:action] == 'index' %>
      <%= render partial: 'all_wants', locals: { wants: @wants } %>
      <%== pagy_nav(@pagy) if @pagy %>
    <% elsif params[:controller] == 'wants' && params[:action] == 'show_my' %>
      <%= render partial: 'my_wants', locals: { wants: @wants } %>
    <% end %>
  </div>
  
</div>