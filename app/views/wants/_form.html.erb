<div class="flex g1">
  <div class="main-content">
      
    <%= form_with(model: want, id: "want_form", html: { class: 'large-form' }) do |form| %>

      <% if want.errors.any? %>
        <div id="error_explanation">
          <ul>
            <% want.errors.each do |error| %>
              <li><%= error.message %></li>
            <% end %>
          </ul>
        </div>
      <% end %>

      <div class="mb-1">
        <div class="summary-field">
          <%= form.label :summary, t('models.want.attributes.summary') + ' *' %>
          <%= form.text_field :summary,
                            value: want.summary,
                            placeholder: t('wants.form.summary_placeholder'),
                            class:'title-input',
                            maxlength: 200,
                            oninput: "updateCharacterCount(this)" %>
          <div class="character-count ml-1">
            <span id="summary-char-count"><%= want.summary&.length || 0 %></span>/200 <%= t('wants.form.characters') %>
          </div>
        </div>
        <script>
          function updateCharacterCount(input) {
            const currentLength = input.value.length;
            document.getElementById('summary-char-count').textContent = currentLength;
          }
        </script>
      </div>

      <div class="mb-1">
        <%= form.label :description, t('models.want.attributes.description') %>
        <%= form.text_area :description,
                          placeholder: t('wants.form.description_placeholder'),
                          class: 'description-input',
                          rows: 4 %>
      </div>

      <div class="mb-1">
        <%= form.label :place, t('models.want.attributes.place') + ' *' %>
        <%= form.text_field :place,
                          id: 'want_place',
                          autocomplete: 'off',
                          placeholder: t('wants.form.place_placeholder') %>
        <%= form.hidden_field :country, id: 'want_country' %>
        <%= form.hidden_field :country_code, id: 'want_country_code' %>
      </div>
      
      <div class="flex mb-1 flex-row g1"> 
        <div class="flex-inline flex-column mb-1">
          <%= form.label :want_type, t('models.want.attributes.want_type') + ' *' %>
          <%= form.select :want_type, 
              Want.translated_want_types, 
              { prompt: t("wants.form.want_type_placeholder") },
              { onchange: "updateCategories(this.value)" } %>
        </div>

        <div class="flex-inline flex-column mb-1">
          <%= form.label :category, t('models.want.attributes.category') + ' *' %>
          <%= form.select :category, 
              @want.want_type ? Want.translated_categories_for(@want.want_type) : [],
              { prompt: t('wants.form.category_placeholder') },
              { onchange: "updateSubcategories(this.value)" } %>
        </div>

        <div class="flex-inline flex-column mb-1">
          <%= form.label :subcategory, t('models.want.attributes.subcategory') %>
          <%= form.select :subcategory,
              @want.category ? Project::CATEGORIES[@want.category.to_sym]&.map { |s| [s.humanize, s] } || [] : [],
              { prompt: t('wants.form.subcategory_placeholder') } %>
        </div>
      </div>

      <div class="mb-1 row g1">
        <div class="flex-inline flex-column mb-1">
          <%= form.label :price_min, t('models.want.attributes.price_min') %>
          <%= form.text_field :price_min %>
        </div>
        <div class="flex-inline flex-column mb-1">
          <%= form.label :price_max, t('models.want.attributes.price_max') %>
          <%= form.text_field :price_max %>
        </div>
        <div class="flex-inline flex-column mb-1">
          <%= form.label :price_currency, t('models.want.attributes.price_currency') %>
          <%= form.select :price_currency, options_for_select([['', ''], ['EUR', 'EUR'], ['USD', 'USD']], selected: want.price_currency) %>
        </div>
      </div>


    <% end %>
  </div>
  
  <!-- Actions sidebar (following projects form pattern) -->
  <div class="side-right">
    <div class="actions">
      <% if want.persisted? %>
        <%= button_to t('common.actions.delete'), want, method: :delete, data: { confirm: t('wants.form.delete_confirm') }, class: "text-link red" %> |
      <% end %>
      <%= link_to t('common.actions.cancel'), wants_path, class: 'text-link' %>
      
      <!-- Single Save Action -->
      <div class="draft-actions">
        <button type="submit" form="want_form" class="button primary">
          <%= want.persisted? ? t('wants.form.update') : t('wants.form.save') %>
        </button>
      </div>
    </div>
  </div>
  
</div>

<script>
  // Dynamic category/subcategory updating
  const wantTypes = <%= raw @cached_want_types.to_json %>;
  const categories = <%= raw @cached_categories.to_json %>;
  const translations = {
    wantTypes: <%= raw @cached_translations[:wantTypes].to_json %>,
    categories: <%= raw @cached_translations[:categories].to_json %>,
    subcategories: <%= raw @cached_translations[:subcategories].to_json %>,
    placeholders: {
      category: '<%= j @cached_placeholders[:category] %>',
      subcategory: '<%= j @cached_placeholders[:subcategory] %>'
    }
  };
  
  function updateCategories(wantType) {
    const categorySelect = document.getElementById('want_category');
    const subcategorySelect = document.getElementById('want_subcategory');
    
    categorySelect.innerHTML = '<option value="">' + translations.placeholders.category + '</option>';
    
    if (wantType && wantTypes[wantType]) {
      wantTypes[wantType].forEach(function(category) {
        const option = document.createElement('option');
        option.value = category;
        option.textContent = translations.categories[category] || category.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
        categorySelect.appendChild(option);
      });
    }
    
    updateSubcategories('');
  }
  
  function updateSubcategories(category) {
    const subcategorySelect = document.getElementById('want_subcategory');
    const currentSubcategory = '<%= @want&.subcategory %>'; 

    subcategorySelect.innerHTML = '<option value="">' + translations.placeholders.subcategory + '</option>';

    if (category && categories[category]) {
      categories[category].forEach(function(subcategory) {
        const option = document.createElement('option');
        option.value = subcategory;
        option.textContent = translations.subcategories[subcategory] || subcategory.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
        
        if (subcategory === currentSubcategory) {
          option.selected = true;
        }

        subcategorySelect.appendChild(option);
      });
    }
  }
  
  // Initialize dropdowns on page load
  document.addEventListener('DOMContentLoaded', function() {
    const wantTypeSelect = document.getElementById('want_want_type');
    const categorySelect = document.getElementById('want_category');
    
    if (wantTypeSelect && wantTypeSelect.value) {
      updateCategories(wantTypeSelect.value);
      
      // Set category if it exists
      setTimeout(() => {
        if (categorySelect && categorySelect.value) {
          updateSubcategories(categorySelect.value);
        }
      }, 100);
    }
  });
</script>