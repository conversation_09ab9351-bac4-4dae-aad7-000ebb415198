<div id="projects">
  <div class="tab-content">
    <div class="projects-filter"></div>
    
    <!-- Title Box -->
    <div class="project-title-box">
      <!-- Green Header Bar -->
      <div class="title-header-bar"></div>
      
      <!-- Want Title -->
      <div class="title-content">
        <h1><%= h(@want.summary) %></h1>
        <p class="location-text">
          <%= heroicon "map-pin", variant: :outline, options: { class: "icon-16" } %>
          <%= @want.place %>
        </p>
      </div>
    </div>

    <!-- Content Grid Container -->
    <div class="project-content-grid">
      <!-- Left Content Box -->
      <div class="project-content-left-box">
        <% if @want.description.present? %>
          <div class="detail-label">
            <%= t('models.want.attributes.description') %>
          </div>
          <div class="description-text">
            <%= simple_format(h(@want.description)) %>
          </div>
        <% end %>

        <div class="project-details-grid">
          <div class="detail-item">
            <div class="detail-label"><%= t('models.want.attributes.want_type') %></div>
            <div class="detail-value"><%= @want.translated_want_type.humanize if @want.want_type %></div>
          </div>
          <div class="detail-item">
            <div class="detail-label"><%= t('models.want.attributes.category') %></div>
            <div class="detail-value"><%= @want.translated_category.humanize if @want.category %></div>
          </div>
          <% if @want.subcategory.present? %>
            <div class="detail-item">
              <div class="detail-label"><%= t('models.want.attributes.subcategory') %></div>
              <div class="detail-value"><%= @want.translated_subcategory.humanize %></div>
            </div>
          <% end %>
        </div>
      </div>

      <!-- Right Content Box -->
      <div class="project-content-right-box">
        <div class="info-rows">
          <% if @want.price_min.present? %>
            <div class="info-row">
              <span class="info-label">Cena od</span>
              <span class="info-value">
                <% if @want.price_currency == 'EUR' %>€<% else %>$<% end %>
                <%= number_with_delimiter(@want.price_min, delimiter: ' ') %>
              </span>
            </div>
          <% end %>
          <% if @want.price_max.present? %>
            <div class="info-row">
              <span class="info-label">Cena do</span>
              <span class="info-value">
                <% if @want.price_currency == 'EUR' %>€<% else %>$<% end %>
                <%= number_with_delimiter(@want.price_max, delimiter: ' ') %>
              </span>
            </div>
          <% end %>
          <div class="info-row">
            <span class="info-label"><%= t('wants.show.want_owner') %></span>
            <span class="info-value"><%= @want.user.user_profile.first_name %> <%= @want.user.user_profile.last_name %></span>
          </div>
          <% if @want.user.user_profile.phone.present? %>
            <div class="info-row">
              <span class="info-label">Tel</span>
              <span class="info-value"><%= @want.user.user_profile.phone %></span>
            </div>
          <% end %>
          <% if @want.user.user_profile.email.present? %>
            <div class="info-row">
              <span class="info-label">Email</span>
              <span class="info-value"><%= @want.user.user_profile.email %></span>
            </div>
          <% end %>
          <div class="info-row">
            <span class="info-label"><%= t('wants.show.last_update') %></span>
            <span class="info-value"><%= @want.updated_at&.strftime("%d. %m. %Y") %></span>
          </div>
          <% if @want.notification %>
            <div class="info-row">
              <span class="info-label"><%= t('models.want.attributes.notification') %></span>
              <span class="info-value"><%= heroicon "check", variant: :solid, options: { class: "icon-16", style: "color: green;" } %></span>
            </div>
          <% end %>
        </div>

        <% if @want.user == current_user %>
          <%= link_to t('common.actions.edit'), edit_want_path(@want), class: "action-button" %>
        <% end %>
      </div>
    </div>
  </div>
</div>