<% content_for :title, "Referral Code Management" %>

<div class="admin-container">
  <div class="admin-header">
    <h1>Referral Code Management</h1>
    <%= link_to "Subscriptions", admin_subscriptions_path, class: "button button-secondary" %>
  </div>

  <!-- Create New Code -->
  <h2>Create New Referral Code</h2>
  <%= form_with model: [:admin, @new_code], local: true, html: { class: "large-form" } do |form| %>
    <% if @new_code.errors.any? %>
      <div id="error_explanation">
        <strong>Please fix the following errors:</strong>
        <ul>
          <% @new_code.errors.full_messages.each do |message| %>
            <li><%= message %></li>
          <% end %>
        </ul>
      </div>
    <% end %>

    <div class="flex g1">
      <div class="field">
        <%= form.label :code %>
        <%= form.text_field :code, class: "form-input", placeholder: "e.g., WELCOME2024" %>
      </div>
      <div class="field">
        <%= form.label :tier_upgrade_to, "Upgrade To" %>
        <%= form.select :tier_upgrade_to,
            options_for_select([
              ['Premium', 'premium'],
              ['Beta', 'beta']
            ]),
            {},
            { class: "form-select" } %>
      </div>
      <div class="field">
        <%= form.label :duration_months, "Duration (months)" %>
        <%= form.number_field :duration_months, class: "form-input", value: 1, min: 1 %>
      </div>
      <div class="field">
        <%= form.label :max_uses, "Max Uses" %>
        <%= form.number_field :max_uses, class: "form-input", value: 1, min: 1 %>
      </div>
      <div class="field">
        <%= form.label :expires_at, "Expires At" %>
        <%= form.datetime_local_field :expires_at,
            class: "form-input",
            value: 1.month.from_now.strftime("%Y-%m-%dT%H:%M") %>
      </div>
    </div>

    <div class="field">
      <%= form.label :description %>
      <%= form.text_area :description, class: "form-input", rows: 2, placeholder: "Optional description for this code" %>
    </div>

    <%= form.submit "Create Code", class: "button button-primary" %>
  <% end %>

  <!-- Existing Codes -->
  <h2 style="margin-top: 40px;">Existing Referral Codes</h2>

  <div class="table-responsive">
    <table class="table table-striped">
      <thead>
        <tr>
          <th>Code</th>
          <th>Status</th>
          <th>Tier</th>
          <th>Expires</th>
          <th class="text-c">Actions</th>
        </tr>
      </thead>
      <tbody>

        <% @codes.each do |code| %>
          <tr class="<%= 'warning' if code.expires_at&.past? %>">
            <td>
              <strong><%= code.code %></strong>
            </td>
            <td>
              <span class="tag <%= code.status == 'active' ? 'ready' : code.status == 'expired' ? 'false' : 'inactive' %>">
                <%= code.status.humanize %>
              </span>
            </td>
            <td>
              <span class="tag <%= code.tier_upgrade_to == 'premium' ? 'attention' : 'ready' %>">
                <%= code.tier_upgrade_to.humanize %>
              </span>
            </td>
            <td>
              <% if code.expires_at %>
                <%= code.expires_at.strftime("%b %d") %>
                <% if code.expires_at.past? %>
                  <br><small class="text-red">Expired</small>
                <% end %>
              <% else %>
                <small class="explanation">Never</small>
              <% end %>
            </td>
            <td class="text-c">
              <div class="flex g1 justify-center">
                <%= link_to "View", admin_referral_code_path(code), class: "text-link" %>
                <% if code.active? %>
                  <%= link_to "Disable", admin_referral_code_path(code),
                      method: :patch,
                      params: { referral_code: { status: 'disabled' } },
                      class: "text-link",
                      confirm: "Disable this code?" %>
                <% end %>
                <%= link_to "Delete", admin_referral_code_path(code),
                    method: :delete,
                    class: "text-link text-red",
                    confirm: "Delete this code?" %>
              </div>
            </td>
          </tr>
        <% end %>
      </tbody>
    </table>
  </div>

  <!-- Pagination -->
  <% if @pagy.pages > 1 %>
    <div style="text-align: center; margin-top: 20px;">
      <%== pagy_nav(@pagy) %>
    </div>
  <% end %>
</div>