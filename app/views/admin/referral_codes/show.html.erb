<% content_for :title, "Referral Code: #{@code.code}" %>

<div class="admin-container">
  <div class="admin-header">
    <h1>Referral Code: <%= @code.code %></h1>
    <%= link_to "Back to Codes", admin_referral_codes_path, class: "button button-secondary" %>
  </div>

  <!-- Code Details -->
  <h2>Code Details</h2>
  <div class="admin-details-section">
    <div class="admin-detail-row">
      <span class="admin-detail-label">Code:</span>
      <span class="admin-detail-value"><strong><%= @code.code %></strong></span>
    </div>
    <div class="admin-detail-row">
      <span class="admin-detail-label">Status:</span>
      <span class="admin-detail-value">
        <span class="tag <%= @code.status == 'active' ? 'ready' : @code.status == 'expired' ? 'false' : 'inactive' %>">
          <%= @code.status.humanize %>
        </span>
      </span>
    </div>
    <div class="admin-detail-row">
      <span class="admin-detail-label">Upgrade To:</span>
      <span class="admin-detail-value">
        <span class="tag <%= @code.tier_upgrade_to == 'premium' ? 'attention' : 'ready' %>">
          <%= @code.tier_upgrade_to.humanize %>
        </span>
      </span>
    </div>
    <div class="admin-detail-row">
      <span class="admin-detail-label">Duration:</span>
      <span class="admin-detail-value"><%= pluralize(@code.duration_months, 'month') %></span>
    </div>
    <div class="admin-detail-row">
      <span class="admin-detail-label">Usage:</span>
      <span class="admin-detail-value">
        <span class="<%= 'text-red' if @code.current_uses >= @code.max_uses %>">
          <%= @code.current_uses %> / <%= @code.max_uses %>
        </span>
        <% usage_percentage = (@code.current_uses.to_f / @code.max_uses * 100).round(1) %>
        <span class="ml-1">(<%= usage_percentage %>% used)</span>
      </span>
    </div>
    <div class="admin-detail-row">
      <span class="admin-detail-label">Created:</span>
      <span class="admin-detail-value"><%= @code.created_at.strftime("%B %d, %Y at %I:%M %p") %></span>
    </div>
    <div class="admin-detail-row">
      <span class="admin-detail-label">Created By:</span>
      <span class="admin-detail-value"><%= @code.created_by.email %></span>
    </div>
    <div class="admin-detail-row">
      <span class="admin-detail-label">Expires:</span>
      <span class="admin-detail-value">
        <% if @code.expires_at %>
          <%= @code.expires_at.strftime("%B %d, %Y at %I:%M %p") %>
          <% if @code.expires_at.past? %>
            <span class="text-red">(Expired)</span>
          <% else %>
            <% days_until_expiry = (@code.expires_at.to_date - Date.current).to_i %>
            <span class="explanation">(<%= pluralize(days_until_expiry, 'day') %> remaining)</span>
          <% end %>
        <% else %>
          <span class="explanation">Never</span>
        <% end %>
      </span>
    </div>
    <% if @code.description.present? %>
      <div class="admin-detail-row">
        <span class="admin-detail-label">Description:</span>
        <span class="admin-detail-value"><%= simple_format(@code.description) %></span>
      </div>
    <% end %>
  </div>

  <!-- Update Code Form -->
  <h2 class="mt-2">Update Code</h2>
  <%= form_with model: [:admin, @code], local: true, html: { class: "large-form" } do |form| %>
    <% if @code.errors.any? %>
      <div id="error_explanation">
        <strong>Please fix the following errors:</strong>
        <ul>
          <% @code.errors.full_messages.each do |message| %>
            <li><%= message %></li>
          <% end %>
        </ul>
      </div>
    <% end %>

    <div class="flex g1">
      <div class="field">
        <%= form.label :status %>
        <%= form.select :status,
            options_for_select([
              ['Active', 'active'],
              ['Expired', 'expired'],
              ['Used Up', 'used_up'],
              ['Disabled', 'disabled']
            ], @code.status),
            {},
            { class: "form-select" } %>
      </div>
      <div class="field">
        <%= form.label :expires_at, "Expires At" %>
        <%= form.datetime_local_field :expires_at,
            value: @code.expires_at&.strftime("%Y-%m-%dT%H:%M"),
            class: "form-input" %>
      </div>
    </div>

    <div class="field">
      <%= form.label :description %>
      <%= form.text_area :description, class: "form-input", rows: 3 %>
    </div>

    <div class="actions">
      <%= form.submit "Update Code", class: "button button-primary" %>
      <%= link_to "Delete Code", admin_referral_code_path(@code),
          method: :delete,
          class: "button-outline ml-1",
          confirm: "Are you sure you want to delete this code? This action cannot be undone." %>
    </div>
  <% end %>
</div>