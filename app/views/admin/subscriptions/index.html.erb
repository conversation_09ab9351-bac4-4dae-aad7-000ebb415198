<% content_for :title, "User Management" %>

<div class="admin-container">
  <div class="admin-header">
    <h1>User Management</h1>
    <%= link_to "Referral Codes", admin_referral_codes_path, class: "button button-secondary" %>
  </div>

  <!-- Status Summary -->
  <div class="flex g1 items-center mb-1 explanation info">
    <span><strong><%= @status_counts[:total] %></strong> Total</span>
    <span>•</span>
    <span><strong><%= @status_counts[:free] %></strong> Free</span>
    <span>•</span>
    <span><strong><%= @status_counts[:premium] %></strong> Premium</span>
    <span>•</span>
    <span><strong><%= @status_counts[:vanguard] %></strong> Vanguard</span>
    <span>•</span>
    <span><strong><%= @status_counts[:approved] %></strong> Approved</span>
    <span>•</span>
    <span><strong><%= @status_counts[:pending] %></strong> Pending</span>
  </div>

  <!-- Filter Buttons -->
  <div class="flex g1 mb-2">
    <%= link_to "All", admin_subscriptions_path, class: "button #{'button-primary' if @status_filter.blank?}" %>
    <%= link_to "Free", admin_subscriptions_path(status: 'free'), class: "button #{'button-primary' if @status_filter == 'free'}" %>
    <%= link_to "Premium", admin_subscriptions_path(status: 'premium'), class: "button #{'button-primary' if @status_filter == 'premium'}" %>
    <%= link_to "Vanguard", admin_subscriptions_path(status: 'vanguard'), class: "button #{'button-primary' if @status_filter == 'vanguard'}" %>
    <%= link_to "Approved", admin_subscriptions_path(status: 'approved'), class: "button #{'button-primary' if @status_filter == 'approved'}" %>
    <%= link_to "Pending", admin_subscriptions_path(status: 'pending'), class: "button #{'button-primary' if @status_filter == 'pending'}" %>
  </div>

  <% if @users && @users.any? %>
    <% if @status_filter.present? %>
      <p class="mb-1">
        Showing <%= @status_filter.capitalize %> users (<%= @users.count %> results)
      </p>
    <% end %>

    <div class="admin-flex-table">
      <div class="admin-flex-table-header">
        <div class="admin-flex-table-cell admin-col-user-id">User ID</div>
        <div class="admin-flex-table-cell admin-col-email">Email</div>
        <div class="admin-flex-table-cell admin-col-profile-basic">Name & Bio</div>
        <div class="admin-flex-table-cell admin-col-profile-contact">Contact & Location</div>
        <div class="admin-flex-table-cell admin-col-tier">Tier</div>
        <div class="admin-flex-table-cell admin-col-approval">Approval</div>
        <div class="admin-flex-table-cell admin-col-actions">Actions</div>
      </div>

      <% @users.each do |user| %>
        <div class="admin-flex-table-row">
          <div class="admin-flex-table-cell admin-col-user-id"><%= user.id %></div>
          <div class="admin-flex-table-cell admin-col-email"><%= user.email %></div>
          <div class="admin-flex-table-cell admin-col-profile-basic">
            <% if user.user_profile %>
              <div class="profile-details">
                <div class="profile-item">
                  <strong><%= user.user_profile.first_name %> <%= user.user_profile.last_name %></strong>
                </div>
                <% if user.user_profile.bio.present? %>
                  <div class="profile-item">
                    <small><%= truncate(user.user_profile.bio, length: 80) %></small>
                  </div>
                <% else %>
                  <div class="profile-item">
                    <small class="explanation">No bio</small>
                  </div>
                <% end %>
              </div>
            <% else %>
              <small class="explanation">No profile</small>
            <% end %>
          </div>
          <div class="admin-flex-table-cell admin-col-profile-contact">
            <% if user.user_profile %>
              <div class="profile-details">
                <% if user.user_profile.phone.present? %>
                  <div class="profile-item">
                    <small><%= user.user_profile.formatted_phone %></small>
                  </div>
                <% else %>
                  <div class="profile-item">
                    <small class="explanation">No phone</small>
                  </div>
                <% end %>
                <% if user.user_profile.city.present? || user.user_profile.country.present? %>
                  <div class="profile-item">
                    <small><%= [user.user_profile.city, user.user_profile.country].compact.join(', ') %></small>
                  </div>
                <% else %>
                  <div class="profile-item">
                    <small class="explanation">No location</small>
                  </div>
                <% end %>
              </div>
            <% else %>
              <small class="explanation">No profile</small>
            <% end %>
          </div>
          <div class="admin-flex-table-cell admin-col-tier">
            <span class="tag <%= user.subscription_tier == 'premium' ? 'attention' : user.subscription_tier == 'vanguard' ? 'ready' : 'info' %>">
              <%= user.subscription_tier.humanize %>
            </span>
          </div>
          <div class="admin-flex-table-cell admin-col-approval">
            <% if user.approved? %>
              <span class="tag ready">Approved</span>
            <% else %>
              <span class="tag attention">Pending</span>
            <% end %>
          </div>

          <div class="admin-flex-table-cell admin-col-actions">
            <div class="flex g1 justify-center">
              <!-- Tier Management -->
              <%= link_to "Free", update_tier_admin_subscription_path(user, subscription_tier: 'free'), method: :patch, class: (user.subscription_tier == 'free' ? 'text-link-muted' : 'text-link') %>
              <%= link_to "Premium", update_tier_admin_subscription_path(user, subscription_tier: 'premium'), method: :patch, class: (user.subscription_tier == 'premium' ? 'text-link-muted' : 'text-link') %>
              <%= link_to "Vanguard", update_tier_admin_subscription_path(user, subscription_tier: 'vanguard'), method: :patch, class: (user.subscription_tier == 'vanguard' ? 'text-link-muted' : 'text-link') %>

              <!-- Approval Management -->
              <% if user.approved? %>
                <%= link_to "Disapprove", update_approval_admin_subscription_path(user, approval_action: 'disapprove'), method: :patch, class: 'text-link-muted' %>
              <% else %>
                <%= link_to "Approve", update_approval_admin_subscription_path(user, approval_action: 'approve'), method: :patch, class: 'text-link' %>
              <% end %>
            </div>
          </div>
        </div>
      <% end %>
    </div>

    <!-- Pagination -->
    <% if @pagy.pages > 1 %>
      <div style="text-align: center; margin-top: 20px;">
        <%= raw pagy_nav(@pagy) %>
      </div>
    <% end %>
  <% else %>
    <p>No users to display.</p>
  <% end %>
</div>