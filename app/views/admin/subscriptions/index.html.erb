<% content_for :title, "User Management" %>

<div class="admin-container">
  <div class="admin-header">
    <h1>User Management</h1>
    <%= link_to "Referral Codes", admin_referral_codes_path, class: "button button-secondary" %>
  </div>

  <!-- Statistics -->
  <div class="flex g1 items-center mb-1 explanation info">
    <span><strong>Tiers:</strong></span>
    <span><strong><%= @tier_stats[:free] %></strong> Free</span>
    <span>•</span>
    <span><strong><%= @tier_stats[:premium] %></strong> Premium</span>
    <span>•</span>
    <span><strong><%= @tier_stats[:beta] %></strong> Beta</span>
    <span style="margin-left: 20px;"><strong>Approval:</strong></span>
    <span><strong><%= @approval_stats[:approved] %></strong> Approved</span>
    <span>•</span>
    <span><strong><%= @approval_stats[:pending] %></strong> Pending</span>
  </div>

  <!-- Users List -->
  <% if @users && @users.any? %>
    <div class="admin-flex-table">
      <div class="admin-flex-table-header">
        <div class="admin-flex-table-cell admin-col-user">User</div>
        <div class="admin-flex-table-cell admin-col-profile">Profile</div>
        <div class="admin-flex-table-cell admin-col-tier">Tier</div>
        <div class="admin-flex-table-cell admin-col-status">Subscription</div>
        <div class="admin-flex-table-cell admin-col-approval">Approval</div>
        <div class="admin-flex-table-cell admin-col-date">Expires</div>
        <div class="admin-flex-table-cell admin-col-actions">Actions</div>
      </div>

      <% @users.each do |user| %>
        <div class="admin-flex-table-row">
          <div class="admin-flex-table-cell admin-col-user">
            <div>
              <strong><%= user.email %></strong>
              <% if user.user_profile %>
                <br><small class="explanation"><%= user.user_profile.first_name %> <%= user.user_profile.last_name %></small>
              <% end %>
            </div>
          </div>
          
          <div class="admin-flex-table-cell admin-col-profile">
            <% if user.user_profile %>
              <div class="profile-details">
                <% if user.user_profile.city.present? || user.user_profile.country.present? %>
                  <div class="profile-item">
                    <strong>📍</strong> <%= [user.user_profile.city, user.user_profile.country].compact.join(', ') %>
                  </div>
                <% end %>
                <% if user.user_profile.phone.present? %>
                  <div class="profile-item">
                    <strong>📞</strong> <%= user.user_profile.formatted_phone %>
                  </div>
                <% end %>
                <% if user.user_profile.bio.present? %>
                  <div class="profile-item">
                    <strong>💬</strong> <%= truncate(user.user_profile.bio, length: 60) %>
                  </div>
                <% end %>
              </div>
            <% else %>
              <small class="explanation">No profile data</small>
            <% end %>
          </div>
          
          <div class="admin-flex-table-cell admin-col-tier">
            <span class="tag <%= user.subscription_tier == 'premium' ? 'attention' : user.subscription_tier == 'beta' ? 'ready' : 'info' %>">
              <%= user.subscription_tier.humanize %>
            </span>
          </div>
          
          <div class="admin-flex-table-cell admin-col-status">
            <% if user.active_subscription? %>
              <span class="tag ready">Active</span>
            <% else %>
              <span class="tag inactive">Inactive</span>
            <% end %>
          </div>
          
          <div class="admin-flex-table-cell admin-col-approval">
            <% if user.approved? %>
              <span class="tag ready">✓ Approved</span>
            <% else %>
              <span class="tag attention">⏳ Pending</span>
            <% end %>
          </div>
          
          <div class="admin-flex-table-cell admin-col-date">
            <% if user.subscription_expires_at %>
              <%= user.subscription_expires_at.strftime("%b %d, %Y") %>
              <% if user.subscription_expires_at < Time.current %>
                <br><small class="text-red">Expired</small>
              <% end %>
            <% else %>
              <small class="explanation">Never</small>
            <% end %>
          </div>
          
          <div class="admin-flex-table-cell admin-col-actions">
            <div class="flex g1 justify-center">
              <!-- Tier Management -->
              <button type="button" class="text-link" data-toggle="modal" data-target="#updateTierModal<%= user.id %>">
                Tier
              </button>
              
              <!-- Approval Management -->
              <%= form_with model: user, url: update_approval_admin_subscription_path(user), method: :patch, local: true, html: { style: 'display: contents;' } do |form| %>
                <% if user.approved? %>
                  <%= form.button "Disapprove", name: "approval_action", value: "disapprove", type: :submit, class: 'text-link-muted', confirm: "Are you sure you want to disapprove this user?" %>
                <% else %>
                  <%= form.button "Approve", name: "approval_action", value: "approve", type: :submit, class: 'text-link', confirm: "Are you sure you want to approve this user?" %>
                <% end %>
              <% end %>
            </div>
          </div>
        </div>
      <% end %>
    </div>

    <!-- Update Tier Modals -->
    <% @users.each do |user| %>
      <div class="modal fade" id="updateTierModal<%= user.id %>" tabindex="-1">
        <div class="modal-dialog">
          <div class="modal-content">
            <%= form_with url: update_tier_admin_subscription_path(user), method: :patch, local: true do |form| %>
              <div class="modal-header">
                <h5 class="modal-title">Update Tier for <%= user.email %></h5>
                <button type="button" class="close" data-dismiss="modal">
                  <span>&times;</span>
                </button>
              </div>
              <div class="modal-body">
                <div class="field">
                  <%= form.label :subscription_tier, "Subscription Tier" %>
                  <%= form.select :subscription_tier,
                      options_for_select([
                        ['Free', 'free'],
                        ['Premium', 'premium'],
                        ['Beta', 'beta']
                      ], user.subscription_tier),
                      {},
                      { class: "form-select" } %>
                </div>
                <div class="field">
                  <%= form.label :subscription_expires_at, "Expires At (leave blank for Beta/permanent)" %>
                  <%= form.datetime_local_field :subscription_expires_at,
                      value: user.subscription_expires_at&.strftime("%Y-%m-%dT%H:%M"),
                      class: "form-input" %>
                  <small style="color: #666; font-size: 0.9em;">
                    Only applies to Premium tier. Beta users never expire.
                  </small>
                </div>
              </div>
              <div class="modal-footer">
                <button type="button" class="button-outline" data-dismiss="modal">Cancel</button>
                <%= form.submit "Update Tier", class: "button button-primary" %>
              </div>
            <% end %>
          </div>
        </div>
      </div>
    <% end %>

    <!-- Pagination -->
    <% if @pagy.pages > 1 %>
      <div style="text-align: center; margin-top: 20px;">
        <%== pagy_nav(@pagy) %>
      </div>
    <% end %>
  <% else %>
    <p class="explanation">No users found.</p>
  <% end %>
</div>

<% content_for :page_scripts do %>
<style>
.profile-details {
  font-size: 0.875rem;
  line-height: 1.4;
}

.profile-item {
  margin-bottom: 4px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.profile-item:last-child {
  margin-bottom: 0;
}

.profile-item strong {
  font-size: 0.75rem;
  opacity: 0.8;
}

.admin-col-profile {
  max-width: 200px;
}

.admin-col-user {
  min-width: 180px;
}

.admin-col-actions {
  min-width: 120px;
}

/* Responsive improvements */
@media (max-width: 768px) {
  .admin-flex-table-cell {
    padding: 8px 4px;
  }
  
  .profile-item {
    font-size: 0.8rem;
  }
}
</style>

<script>
// Auto-clear expiration date when selecting Free or Beta tier
document.querySelectorAll('select[name="user[subscription_tier]"]').forEach(function(select) {
  select.addEventListener('change', function() {
    const expiresField = this.closest('.modal-content').querySelector('input[name="user[subscription_expires_at]"]');
    if (this.value === 'free' || this.value === 'beta') {
      expiresField.value = '';
      expiresField.disabled = true;
    } else {
      expiresField.disabled = false;
    }
  });
});
</script>
<% end %>