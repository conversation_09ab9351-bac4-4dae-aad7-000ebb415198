<h2><%= t "devise.invitations.edit.header" %></h2>

<%= form_for(resource, as: resource_name, url: invitation_path(resource_name), html: { method: :put }) do |f| %>
  <%= render "devise/shared/error_messages", resource: resource %>
  <%= f.hidden_field :invitation_token, readonly: true %>

  <% if f.object.class.require_password_on_accepting %>
    <div class="field">
      <%= f.label :password %><br />
      <%= f.password_field :password %>
    </div>

    <div class="field">
      <%= f.label :password_confirmation %><br />
      <%= f.password_field :password_confirmation %>
    </div>
  <% end %>
  
  <div class="field">
    <%= f.check_box :remember_me %>
    <%= f.label :remember_me, "Remember me" %>
  </div>


  <div class="actions">
    <%= f.submit t("devise.invitations.edit.submit_button") %>
  </div>
<% end %>
