<p><%= t("devise.mailer.email_changed.greeting", default: "Hello %{email}!", email: @resource.email) %></p>

<% if @resource.try(:unconfirmed_email?) %>
  <p><%= t("devise.mailer.email_changed.unconfirmed_email", default: "We're contacting you to notify you that your email is being changed to %{unconfirmed_email}.", unconfirmed_email: @resource.unconfirmed_email) %></p>
<% else %>
  <p><%= t("devise.mailer.email_changed.confirmed_email", default: "We're contacting you to notify you that your email has been changed to %{email}.", email: @resource.email) %></p>
<% end %>

