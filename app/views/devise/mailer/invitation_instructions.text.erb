<%=t("devise.mailer.invitation_instructions.subject", inviter_first_name: @inviter_first_name, inviter_last_name: @inviter_last_name, default: 'Invitation to join the Unlisters Network.')%>
=======================================

<%= t("devise.mailer.invitation_instructions.hello", email: @resource.email) %>

<%= t("devise.mailer.invitation_instructions.someone_invited_you", default:'A member of our professional community has invited you to join the Unlisters Network, a platform for connections and opportunities.') %>

<%= t("devise.mailer.invitation_instructions.accept") %>: <%= accept_invitation_url(@resource, invitation_token: @token) %>

<% if @resource.invitation_due_at %>
<%= t("devise.mailer.invitation_instructions.accept_until", due_date: l(@resource.invitation_due_at, format: :'devise.mailer.invitation_instructions.accept_until_format')) %>
<% end %>

<%= t("devise.mailer.invitation_instructions.features.title", default:'Benefits of Unlisters Network')%>

* <%= t("devise.mailer.invitation_instructions.features.curated_membership", default: "A community of professionals and investors") %>
* <%= t("devise.mailer.invitation_instructions.features.private_sharing", default: "Secure sharing of unlisted opportunities") %>
* <%= t("devise.mailer.invitation_instructions.features.control_visibility", default: "Complete control over your information visibility") %>
* <%= t("devise.mailer.invitation_instructions.features.meaningful_connections", default: "Relevant connections based on shared interests") %>

<%= t("devise.mailer.invitation_instructions.ignore") %>

<%= t("devise.mailer.invitation_instructions.closing", default: "Regards,") %>
<%= t("devise.mailer.invitation_instructions.signature", default: "The Unlisters Team") %>

<%= t("devise.mailer.invitation_instructions.questions_contact", default: "Questions? Contact us at")%> <EMAIL>