<div class="profile-edit">
  <div class="">
    <div class="flex flex-column g1 align-start">

      <h1><%= t('user_profiles.edit.change_password', default: 'Change password') %></h1>

      <%= form_for(resource, as: resource_name, url: registration_path(resource_name), html: { method: :put }) do |f| %>
        <div class="flex g1">
          <div class="main-content">
            <%= render "devise/shared/error_messages", resource: resource %>

            <% if devise_mapping.confirmable? && resource.pending_reconfirmation? %>
              <div>Currently waiting confirmation for: <%= resource.unconfirmed_email %></div>
            <% end %>

            <div class="field">
              <%= f.label :password %> <i><%= t('user_profiles.edit.leave_blank', default: 'leave blank if you don\'t want to change it') %></i>
              <%= f.password_field :password, autocomplete: "new-password" %>
              <% if @minimum_password_length %>

                <em><%= t('user_profiles.edit.characters_minimum', default: 'characters minimum') %> <%= @minimum_password_length %> </em>
              <% end %>
            </div>

            <div class="field">
              <%= f.label :password_confirmation %>
              <%= f.password_field :password_confirmation, autocomplete: "new-password" %>
            </div>

            <div class="field">
              <%= f.label :current_password %> <i><%= t('user_profiles.edit.current_password', default: 'we need your current password to confirm your changes') %></i>
              <%= f.password_field :current_password, autocomplete: "current-password" %>
            </div>

            <div class="actions flex g1">
              <%= link_to t('common.actions.back', default: 'Back'), :back %>
              <%= f.submit t('user_profiles.edit.update_password', default: 'Update password') %>
            </div>
          </div>
        </div>
      <% end %>

    </div>

  </div>
</div>
