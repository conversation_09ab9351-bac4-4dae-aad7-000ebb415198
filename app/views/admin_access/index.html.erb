<div class="admin-container">
  <h1>Projects</h1>

  <!-- Status Summary -->
  <div class="flex g1 items-center mb-1 explanation info">
    <span><strong><%= @status_counts[:total] %></strong> Total</span>
    <span>•</span>
    <span><strong><%= @status_counts[:draft] %></strong> Drafts</span>
    <span>•</span>
    <span><strong><%= @status_counts[:pending] %></strong> Pending</span>
    <span>•</span>
    <span><strong><%= @status_counts[:published] %></strong> Published</span>
  </div>

  <!-- Filter Buttons -->
  <div class="flex g1 mb-2">
    <%= link_to "All", user_admin_dashboard_path, class: "button #{'button-primary' if @status_filter.blank?}" %>
    <%= link_to "Drafts", user_admin_dashboard_path(status: 'draft'), class: "button #{'button-primary' if @status_filter == 'draft'}" %>
    <%= link_to "Pending", user_admin_dashboard_path(status: 'pending'), class: "button #{'button-primary' if @status_filter == 'pending'}" %>
    <%= link_to "Published", user_admin_dashboard_path(status: 'published'), class: "button #{'button-primary' if @status_filter == 'published'}" %>
  </div>

  <% if @projects && @projects.any? %>
    <% if @status_filter.present? %>
      <p class="mb-1">
        Showing <%= @status_filter.capitalize %> projects (<%= @projects.count %> results)
      </p>
    <% end %>
    
    <div class="admin-flex-table">
      <div class="admin-flex-table-header">
        <div class="admin-flex-table-cell admin-col-id">Project ID</div>
        <div class="admin-flex-table-cell admin-col-id">User ID</div>
        <div class="admin-flex-table-cell admin-col-email">User Email</div>
        <div class="admin-flex-table-cell admin-col-summary">Summary</div>
        <div class="admin-flex-table-cell admin-col-date">Last Updated</div>
        <div class="admin-flex-table-cell admin-col-status">Status</div>
        <div class="admin-flex-table-cell admin-col-actions">Actions</div>
      </div>

      <% @projects.each do |project| %>
        <div class="admin-flex-table-row">
          <div class="admin-flex-table-cell admin-col-id"><%= project.id %></div>
          <div class="admin-flex-table-cell admin-col-id"><%= project.user_id %></div>
          <div class="admin-flex-table-cell admin-col-email"><%= project.user&.email || "N/A" %></div>
          <div class="admin-flex-table-cell admin-col-summary" title="<%= project.summary %>"><%= project.summary %></div>
          <div class="admin-flex-table-cell admin-col-date"><%= project.updated_at.strftime("%Y-%m-%d %H:%M") %></div>
          <div class="admin-flex-table-cell admin-col-status">
            <span class="tag <%= project.status == 'published' ? 'ready' : project.status == 'pending' ? 'pending' : 'inactive' %>"><%= project.status_label %></span>
          </div>
          <div class="admin-flex-table-cell admin-col-actions">
            <div class="flex g1 justify-center">
              <%= form_with(model: project, url: update_approval_project_path(project), method: :patch, local: true, html: { style: 'display: contents;' }) do |form| %>
                <% if project.status == 'draft' %>
                  <%= form.button "Approve", name: "project[approved]", value: "true", type: :submit, class: 'text-link-muted' %>
                  <%= form.button "Reject", name: "project[approved]", value: "false", type: :submit, class: 'text-link-muted' %>
                <% elsif project.approved? %>
                  <%= form.button "Reject", name: "project[approved]", value: "false", type: :submit, class: 'text-link-muted' %>
                <% else %>
                  <%= form.button "Approve", name: "project[approved]", value: "true", type: :submit, class: 'text-link' %>
                  <%= form.button "Reject", name: "project[approved]", value: "false", type: :submit, class: 'text-link-muted' %>
                <% end %>
              <% end %>
              <%= form_with(model: project, url: admin_destroy_project_path(project), method: :delete, local: true, data: { confirm: "Are you sure you want to delete this project?" }, html: { style: 'display: contents;' }) do |form| %>
                <% if project.status == 'draft' %>
                  <%= form.button "Delete", type: :submit, class: 'text-link-muted' %>
                <% else %>
                  <%= form.button "Delete", type: :submit, class: 'text-link-muted' %>
                <% end %>
              <% end %>
            </div>
          </div>
        </div>
      <% end %>
    </div>
  <% else %>
    <p>No projects to display.</p>
  <% end %>
</div> 