<%= form_with(model: project_share) do |form| %>
  <% if project_share.errors.any? %>
    <div style="color: red">
      <h2><%= pluralize(project_share.errors.count, "error") %> prohibited this project_share from being saved:</h2>

      <ul>
        <% project_share.errors.each do |error| %>
          <li><%= error.full_message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div>
    <%= form.label :project_id, style: "display: block" %>
    <%= form.text_field :project_id %>
  </div>

  <div>
    <%= form.label :user_id, style: "display: block" %>
    <%= form.text_field :user_id %>
  </div>

  <div>
    <%= form.submit %>
  </div>
<% end %>
