<h1><%= t('application.menu.invite_users', default: 'Invite Users') %></h1>
<div class="connections-container">
  <div class="connections-search">
    <h3><%= t('invitations.invite_prompt', default: "Can't find them? Invite!") %></h3>
    <%= form_with url: invitations_path, method: :post, class: "search-form" do |form| %>
      <div class="search-filters">
        <%= form.email_field :email, placeholder: t('invitations.email_placeholder', default: 'Email'), required: true, class: 'search-input' %>
        <%= select_tag :langlocale, options_for_select([['Slovenčina', 'sk'], ['English', 'en']]), class: 'form-select' %>
        <%= form.submit t('invitations.send_button', default: "Send Invitation"), class: 'search-button' %>
      </div>
    <% end %>
  </div>

  <div class="connections-list">
    <h3><%= t('invitations.people_invited', default: "People You've Invited") %></h3>
    <% if @sent_invitations.present? %>
      <% @sent_invitations.each do |user| %>
        <div class="connection-item">
          <div class="connection-content">
            <div class="connection-header">
              <div class="connection-main-info">
                <div class="connection-name">
                  <%= user.email %>
                </div>
                <div class="connection-location">
                  <% if user.invitation_accepted_at %>
                    <span class="status-badge status-badge--accepted"><%= t('invitations.status.accepted', default: 'Accepted') %></span>
                  <% else %>
                    <span class="status-badge status-badge--pending"><%= t('network_connections.status.pending', default: 'Pending') %></span>
                    <%= t('invitations.sent_label', default: 'Sent:') %> <%= user.invitation_sent_at.strftime("%B %d, %Y") %>
                    <%= button_to t('invitations.resend_button', default: 'Resend Invitation'), resend_invitation_path(user), method: :post, class: 'action-button action-button--outline' %>
                  <% end %>
                </div>
              </div>
            </div>
          </div>
        </div>
      <% end %>
    <% else %>
      <p class="empty-state"><%= t('invitations.no_invitations', default: 'No invitations sent.') %></p>
    <% end %>
  </div>
</div>