<h1><%= t('network_connections.my_connections_title', default: 'My Network') %></h1>
<div class="connections-container">
  <div class="connections-list">
    <% if @connections.present? %>
      <% @connections.each do |connection| %>
        <% profile = connection.connected_user(current_user.id).user_profile %>
        <div class="connection-item">
          <%# Avatar %>
          <div class="profile-avatar">
            <% initials = "#{profile.first_name&.first}#{profile.last_name&.first}".upcase %>
            <%= content_tag :div, initials, class: 'avatar-circle' %>
          </div>
          <div class="connection-content">
            <div class="connection-header">
              <div class="connection-main-info">
                <div class="connection-name">
                    <%= link_to user_profile_connection_request_path(profile.id), 
                            class: 'requester-name modal-trigger' do %>
                    <%= profile.first_name %> <%= profile.last_name %>
                  <% end %>
                </div>
                <div class="connection-location">
                  <%= [profile.city, profile.country].reject(&:blank?).join(', ') %>
                </div>
                <div class="connection-email">
                  <%= profile.email %>
                </div>
              </div>

              <div class="connection-actions">
                <%= button_to t('network_connections.actions.disconnect', default:"Disconnect"),
                  network_connection_path(connection),
                  method: :delete,
                  class: "action-button action-button--reject",
                  data: { confirm: t('network_connections.messages.disconnect_confirm', 
                                      default: "Are you sure you want to disconnect?") } %>  
                </div>
            </div>

            <div class="connection-bio">
              <%= truncate(profile.bio, length: 200, omission: '...') %>
              <% if profile.bio && profile.bio.length > 200 %>
                <%= link_to t('network_connections.actions.show_more', default: 'Show more'),
                            user_profile_connection_request_path(profile.id), 
                            class: 'modal-trigger' %>
              <% end %>
            </div>
          </div>
        </div>
      <% end %>
    <% else %>
      <%= t('network_connections.messages.no_profiles', default: 'No user profiles found.')%>
      <div>
        <div>
          <%= link_to t('network_connections.actions.find_connections', default: 'Find connections'), 
                        network_connections_path %></li>
        </div>
        <div>
          <%= link_to t('network_connections.actions.invite_connections', default: 'Invite connections'),
                        invite_network_connections_path %></li>
        </div>
      </ul>
    <% end %>
  </div>
</div>