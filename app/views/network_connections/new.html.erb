<div class="main-box">
  <h1>New Network Connection</h1>
  <br/>
  <div>
    Search user

    <%= form_tag search_user_profiles_path, method: :post do %>
      <%= text_field_tag :name, params[:name] %>
      <div class="actions">
        <%= submit_tag "Search", class:'button' %>  
      </div>
    <% end %>
  </div>

  <br>

  <div class="list">
    <% if @user_profiles.present? %>
      <% @user_profiles.each do |user_profile| %>
        <div class="list-box">
          <div class="list-item">
            <%= user_profile.first_name %> <%= user_profile.last_name %>
          </div>
          <div class="">
            
              <% if user_profile.user_id == current_user.id %>
                <span class="tag info">that's you</span>
              <% elsif !current_user.user_profile.first_name && !current_user.user_profile.last_name %>
                <div id="error_explanation">
                  Before you could send a connection request, please add your profile name.
                </div>
                <div class="actions">
                  <%= link_to "Add your profile name", edit_user_profile_path, class:"button" %>
                </div>
              <% else %>
                <div class="actions">
                  <%= button_to "Send connection request", network_connections_path(invitee_id: user_profile.user_id), method: :post, class: "button" %>
                </div>
              <% end %>
          </div>
        </div>
      <% end %>
    <% else %>
      <p>No user profiles found.</p>
      <br/>
      <p><%= link_to 'Invite new contacts', invitations_path, class:'button' %></p>
    <% end %>
  </div>
  <br/>
  <div>
    <%= link_to "Back to network connections", network_connections_path %>
  </div>
</div>

