<div class="connections-container">
  <div class="connections-search">
    <%= form_tag search_user_profiles_path, method: :post, class: "search-form" do %>
      <div class="search-filters">
        <%= select_tag :location, options_for_select(['Any Location', 'Same City', 'Same Country']), class: 'filter-select' %>
      </div>
      <div class="search-form">
        <%= text_field_tag :name, params[:name], placeholder: 'Search for the name', class: 'search-input' %>
        <%= submit_tag "Search", class: 'search-button' %>
      </div>
    <% end %>
  </div>
  <div class="connections-list">
    <% if @user_profiles.present? %>
      <% @user_profiles.each do |user_profile| %>
        <div class="connection-item">
          <div class="connection-info">
            <div class="connection-name">
              <%= link_to user_profile_connection_request_path(user_profile.id), class: 'requester-name modal-trigger' do %>
                <%= user_profile.first_name %> <%= user_profile.last_name %>
              <% end %>
            </div>
            <div class="connection-location">
              <%= user_profile.location %>
            </div>
          </div>
          <div class="connection-bio">
            <%= truncate(user_profile.bio, length: 80, omission: '...') %>
            <% if user_profile.bio %>
              <%= link_to 'Show more', '#', class: 'message-expand' %>
            <% end %>
          </div>

          <div class="connection-actions">
            <% if user_profile.user_id == current_user.id %>
              <span class="status-badge status-badge--self">that's you</span>
            <% elsif !current_user.user_profile.first_name && !current_user.user_profile.last_name %>
              <div class="profile-warning">
                Before you could send a connection request, please add your profile name.
              </div>
              <%= link_to "Add your profile name", edit_user_profile_path, class: "profile-edit-button" %>
            <% elsif user_profile.connection_exists.present? %>
              <span class="status-badge status-badge--connected">Connected</span>
            <% else %>
              <%= link_to "Connect", 
                            new_connection_request_path(invitee_id: user_profile.user_id), 
                            class: 'action-button action-button--outline modal-trigger' %>
            <% end %>
          </div>
        </div>
      <% end %>
    <% else %>
      <p class="empty-state">No user profiles found.</p>
    <% end %>
  </div>
</div>