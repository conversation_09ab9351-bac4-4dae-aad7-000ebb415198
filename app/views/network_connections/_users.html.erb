<h1><%= t('network_connections.title', default:"Unlisters Network")%> </h1>
<div class="connections-container">
  <div class="connections-search">
    <%= form_tag network_connections_path, method: :get, class: "search-form" do %>
      <%= hidden_field_tag :filter, params[:filter] %>
      <div class="search-filters">
        <%= select_tag :filter, 
                      options_for_select([[t('network_connections.filters.not_in_network'), ''], 
                                         [t('network_connections.filters.show_all'), 'all']], params[:filter]), 
                      class: 'filter-select' %>
        <%= text_field_tag :location, params[:location], 
                          placeholder: t('network_connections.filters.location_placeholder'), 
                          class: 'search-input' %>
        <%= text_field_tag :name, params[:name], 
                          placeholder: t('network_connections.filters.name_placeholder'), 
                          class: 'search-input' %>
        <%= submit_tag t('network_connections.filters.search'), class: 'search-button' %>
      </div>
    <% end %>
  </div>

  <div class="connections-list">
    <% if @user_profiles.present? %>
      <% @user_profiles.each do |user_profile| %>
        <div class="connection-item">
          <div class="profile-avatar">
            <% initials = "#{user_profile.first_name&.first}#{user_profile.last_name&.first}".upcase %>
            <%= content_tag :div, initials, class: 'avatar-circle' %>
          </div>

          <div class="connection-content">
            <div class="connection-header">
              <div class="connection-main-info">
                <div class="connection-name">
                  <%= link_to user_profile_connection_request_path(user_profile.id), 
                            class: 'requester-name modal-trigger' do %>
                    <%= user_profile.first_name %> <%= user_profile.last_name %>
                  <% end %>
                </div>
                <div class="connection-location">
                  <%= [user_profile.city, user_profile.country].reject(&:blank?).join(', ') %>
                </div>
              </div>

              <div class="connection-actions">
                <% if user_profile.user_id == current_user.id %>
                  <span class="status-badge status-badge--self"><%= t('network_connections.status.self', default:"that's you")%></span>
                <% elsif !current_user.user_profile.first_name && !current_user.user_profile.last_name %>
                  <div class="profile-warning">
                    <%= t('network_connections.messages.profile_warning', 
                            default: "Before you could send a connection request, please add your profile name.") %>
                  </div>
                  <%= link_to t('network_connections.actions.add_profile_name', default:"Add your profile name"), 
                             edit_user_profile_path, 
                             class: "profile-edit-button" %>
                <% elsif user_connected?(user_profile.user_id) %>
                  <span class="status-badge status-badge--connected"><%= t('network_connections.status.connected', default:'Connected')%></span>
                <% elsif user_connection_pending?(user_profile.user_id) %>
                  <span class="status-badge status-badge--pending"><%= t('network_connections.status.pending', default:'Pending')%></span>
                <% else %>
                  <%= link_to t('network_connections.actions.connect', default:"Connect"), 
                             new_connection_request_path(invitee_id: user_profile.user_id), 
                             class: 'action-button action-button--outline modal-trigger' %>
                <% end %>
              </div>
            </div>

            <div class="connection-bio">
              <%= truncate(user_profile.bio, length: 200, omission: '...') %>
              <% if user_profile.bio && user_profile.bio.length > 200 %>
                <%= link_to t('network_connections.actions.show_more', defaukt:'Show more'), user_profile_connection_request_path(user_profile.id), 
                            class: 'modal-trigger' %>
              <% end %>
            </div>
          </div>
        </div>
      <% end %>
    <% else %>
      <p class="empty-state"><%= t('network_connections.messages.no_profiles', default: 'No user profiles found.')%></p>
    <% end %>
  </div>
</div>