<div class="main-box">
  <h1><%= t('user_profiles.show.title', default: 'Your profile') %></h1>
  <h4 class="red">(<%= t('user_profiles.show.visibility_notice', default: 'Your profile is visible to Unlisters Network') %>)</h4>
  <br/>

  <div class="card">
    <div class="card-upper-actions">
    </div>
    <div class="card-profile">
      <div class="circle">
        <%= @user_profile.first_name.first.capitalize %>
      </div>
      <div class="info">
        <div class="name">
          <%= @user_profile.first_name %> <%= @user_profile.last_name %>
        </div>
        <div class="location">
          <%= [@user_profile.city, @user_profile.country].reject(&:blank?).join(', ') %>
        </div>
      </div>
    </div>
    <div class="card-text">
      <div class="mb-1">
        <strong><%= t('user_profiles.show.email', default: 'Email') %>:</strong>
        <%= @user_profile.email %>
      </div>
      <div class="mb-1">
        <strong><%= t('models.user_profile.attributes.city') %>:</strong>
        <%= @user_profile.city %>
      </div>
      <div class="mb-1">
        <strong><%= t('models.user_profile.attributes.country') %>:</strong>
        <%= @user_profile.country %>
      </div>
      <div class="mb-1">
        <strong><%= t('models.user_profile.attributes.bio') %>:</strong>
        <%= simple_format(h(@user_profile.bio)) %>
      </div>
      <div class="mb-1">
        <strong><%= t('models.user_profile.attributes.phone') %>:</strong>
        <%= @user_profile.formatted_phone %>
      </div>
    </div>
    <div class="card-message">
      
    </div>
    <div class="card-lower-actions">
      <%= link_to t('user_profiles.show.edit_profile', default: 'Edit profile'), edit_user_profile_path(current_user.user_profile) %> |
      <%= link_to t('user_profiles.show.change_password', default: 'Change Password'), edit_user_registration_path %>
    </div>
  </div>
  
</div>