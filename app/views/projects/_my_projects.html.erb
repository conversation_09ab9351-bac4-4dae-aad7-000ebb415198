<h1><%= t('projects.my.title', default: 'My Projects') %></h1>
<div class="projects-filter">
  <div class="projects-list">
    <% if @projects_auths.present? %>
      <% @projects_auths.group_by(&:id).each do |project_id, project_auths| %>
        <% project = project_auths.first %>
        <div class="project-item">
          <div class="project-main">
            <h3 class="project-title">
              <%= link_to project.summary.presence || t('projects.common.no_title'), project_path(project) %>
            </h3>
            <div class="project-meta">
              <div class="project-location">
                <%= heroicon "map-pin", variant: :solid, options: { class: "icon-sm" } %>
                <span><%= project.location %></span>
              </div>
            </div>
          </div>
          
          <div class="project-category">
            <div>
              <%= project.project_type ? project.translated_project_type.humanize : t('projects.form.draft', default: 'Draft') %>
            </div>
            <div>
              <%= project.category ? project.translated_category.humanize : '' %>
            </div>
            <div>
              <%= project.subcategory ? project.translated_subcategory.humanize : '' %>
            </div>
            <div>
              <% if project.project_status && !project.approved? %>
                <div class="awaiting-approval">
                  <span class="status-badge pending"><%= t('projects.form.pending_approval', default: 'Awaiting Approval') %></span>
                </div>
              <% end %>
            </div>
          </div>
          
          <div class="project-actions">
            <div class="project-sharing">
              <%= heroicon "users", variant: :solid, options: { class: "icon-sm" } %>
              <span><%= project.summary_only ? t('projects.common.title_only') : t('projects.common.everything') %></span>
              <span><%= project.semi_public ? t('projects.common.everyone') : t('projects.common.my_network') %></span>
            </div>
            <%= link_to t('common.actions.edit'), edit_project_path(project), class: "action-link" %>
            <% if project.pending_auths_count.to_i > 0 %>
              <span class="red">
                (<%= pluralize(project.pending_auths_count,
                      t('projects.index.project_item.request')) %>
                      <%= t('projects.index.project_item.pending')%>)
              </span>
            <% end %>
          </div>
          
          <div class="project-date">
            <%= project.updated_at.strftime("%b %d '%y") %>
          </div>
        </div>
      <% end %>
    <% else %>
      <p><%= t('projects.index.empty') %></p>
    <% end %>
  </div>
</div>