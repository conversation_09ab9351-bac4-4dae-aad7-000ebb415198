<div class="requests-container">
  <div class="request-list">
    <% if reqs.present? %>
      <h1><%= t('connection_requests.auth_title', default: 'Connection Requests')%></h1>
      <% reqs.each do |req| %>
        <div class="request-card">
          <div class="request-content">
            <div class="request-avatar">
              <%= req.inviter.user_profile.first_name.first.capitalize %>
            </div>
            
            <div class="request-info">
              <%= link_to user_profile_connection_request_path(req.inviter.user_profile.id), class: 'requester-name modal-trigger' do %>
                <%= req.inviter.user_profile.first_name %> <%= req.inviter.user_profile.last_name %>
              <% end %>
              
              <div class="requester-location">
                <%= [req.inviter.user_profile.city, req.inviter.user_profile.country].reject(&:blank?).join(', ') %>
              </div>
              
              <% if req.message %>
                <div class="request-message">
                  <%= truncate(req.message, length: 100, omission: '...') %>
                  <%= link_to t('connection_requests.actions.show_more', default: 'Show more'),
                    connection_request_path(req),
                    class: 'modal-trigger' %>
                </div>
              <% end %>
              
              <div class="request-actions">
                <% if req.project %>
                  <% if req.project.user_id == current_user.id && req.status == "pending" %>
                  <%= button_to t('connection_requests.actions.grant_access', default: 'Grant Project Access'), 
                    accept_auth_request_connection_request_path(req), 
                    method: :post, 
                    class: "action-button action-button--primary" %>
                  <%= button_to t('common.actions.reject', default: 'Reject'), 
                    reject_auth_request_connection_request_path(req), 
                    method: :post, 
                    class: "action-button action-button--reject", 
                    data: { confirm: t('connection_requests.confirmations.reject', default: 'Are you sure you want to reject this request?') } %>
                  <% end %>
                <% else %>
                 <%= button_to t('common.actions.accept', default: 'Accept'), 
                  accept_network_request_connection_request_path(req), 
                  method: :post, 
                  class: "action-button action-button--primary" %>
                <%= button_to t('common.actions.reject', default: 'Reject'), 
                  reject_network_request_connection_request_path(req), 
                  method: :post, 
                  class: "action-button action-button--reject", 
                  data: { confirm: t('connection_requests.confirmations.reject', default: 'Are you sure you want to reject this request?') } %>
                <% end %>
              </div>
            </div>
          </div>
        </div>
      <% end %>
    <% end %>
    
    <% if auths.present? %>
      <h1><%= t('projects.form.connected_users', default: 'Connected Users')%></h1>
      <% auths.each do |auth| %>
        <div class="request-card">
          <div class="request-content">
            <div class="request-avatar">
              <%= auth.user.user_profile.first_name.first.capitalize %>
            </div>
            
            <div class="request-info">
              <%= link_to user_profile_connection_request_path(auth.user.user_profile.id), class: 'requester-name modal-trigger' do %>
                <%= auth.user.user_profile.first_name %> <%= auth.user.user_profile.last_name %>
              <% end %>
              
              <div class="requester-location">
                <%= [auth.user.user_profile.city, auth.user.user_profile.country].reject(&:blank?).join(', ') %>
              </div>
              
              <div class="request-actions">
                 <span class="access-badge access-badge--<%= auth.access_level %>">
                  <%= auth.access_level.humanize %>
                </span>

              <%= button_to t('common.actions.delete', default: 'Delete'), delete_access_project_path(auth), method: :delete, 
                      data: { confirm: t('projects.full_details_users.remove_access_confirm', default: 'Remove access to this project?') },
                      class: 'text-link red' %>
              </div>
            </div>
          </div>
        </div>
      <% end %>
    <% else %>
      <%= t('projects.form.no_connected_users') %>
    <% end %>

    <div style="height:200px"></div>
  </div>
</div>