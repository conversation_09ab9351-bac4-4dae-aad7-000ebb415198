<div id="projects">
  <div class="tab-content">
    <div class="flex g1 justify-start mb-1">
      <div class="flex items-center">
        <%= link_to new_project_path, class:'button-prominent' do %>
          <span class="flex items-center">
            <%= heroicon "plus-circle", variant: :solid, options: { class: "icon-24 mr-2" } %>
            <%= t('projects.my.add', default: "Add New Offer") %>
          </span>
        <% end %>
      </div>
    </div>

    <% if params[:controller] == 'projects' && (params[:action] == 'index' || request.path == root_path(locale: I18n.locale)) %>
      <%= render partial: 'all_projects', locals: { projects: @projects } %>
      <%== pagy_nav(@pagy) if @pagy %>
    <% elsif params[:controller] == 'projects' && params[:action] == 'show_my' %>
      <%= render partial: 'my_projects', locals: { projects: @projects } %>
    <% end %>
  </div>
  
</div>