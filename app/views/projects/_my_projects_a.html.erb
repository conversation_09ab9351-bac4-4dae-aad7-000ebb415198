<%# app/views/projects/_my_projects.html.erb %>
<div class="container">
  <div class="my-projects flex">
    <!-- Projects List Sidebar -->
    <div class="side-right p-1">
      <div class="flex space mb-1">
        <%= link_to "Add new project", new_project_path, class: 'button' %>
      </div>
      
      <div class="projects-overview">
        <% first_project = true %>

        <% projects.each do |project| %>
          <% if current_user == project.user %>
            <div class="project-card sidelink" onclick="showProjectAuth('<%= project.id %>')">
              <div class="">
                <div>
                  <p><%= truncate(project.summary, length: 50) %></p>
                  <p class="project-location"><%= project.location %></p>
                </div>
                <!--span class="tag info">
                  <%= project.project_auths.count %> users
                </span -->
              </div>
            </div>
            <% if first_project %>
              <script>
                document.addEventListener('DOMContentLoaded', function() {
                  showProjectAuth('<%= project.id %>');
                });
              </script>
              <% first_project = false %>
            <% end %>
          <% end %>
        <% end %>
      </div>
    </div>

    <div class="main-content project-card">
      <% projects.each do |project| %>
        <% if current_user == project.user %>
          <div id="project-auth-<%= project.id %>" class="project-auth-panel hidden">
            <div class="">
              <div class="project-content">
                <p><%= project.summary %></h1>
                <p class="project-location"><%= project.location %></p>
              </div>
              <div class="project-actions">
                <%= link_to "Edit", edit_project_path(project), class: 'action-link' %> |
              </div>
            </div>

            <% if project.project_auths.where(access_level: :pending).any? %>
              <div class="mt-1">
                <h3 class="bold mb-1">Pending Requests (<%= project.project_auths.where(access_level: :pending).count %>)</h3>
                <table class="w-1">
                  <tbody>
                    <% project.project_auths.where(access_level: :pending).each do |auth| %>
                      <tr class="">
                        <td>
                          <%= auth.user.user_profile.first_name %>
                          <%= auth.user.user_profile.last_name %>
                        </td>
                        <td class="text-r">
                          <%= link_to "Review",
                              connection_request_path(@pending_requests.find_by(
                                project_id: project.id,
                                inviter_id: auth.user_id
                              )),
                              class: 'modal-trigger' %>
                        </td>
                      </tr>
                    <% end %>
                  </tbody>
                </table>
              </div>
            <% end %>

            <div class="mt-1">
              <h3 class="bold mb-1">Approved Users (<%= project.project_auths.where(access_level: :full_details).count %>)</h3>
              <table class="w-1">
                <tbody>
                  <% project.project_auths.where(access_level: :full_details).each do |auth| %>
                    <tr class="">
                      <td>
                        <%= auth.user.user_profile.first_name %>
                        <%= auth.user.user_profile.last_name %>
                      </td>
                      <td>
                        <span class="tag full_details">Full Details</span>
                      </td>
                      <td class="text-r">
                        <%= button_to 'Remove permissions', 
                            delete_access_project_path(auth),
                            method: :delete,
                            class: 'text-link red',
                            data: { confirm: 'Remove access to this project?' } %>
                      </td>
                    </tr>
                  <% end %>
                </tbody>
              </table>
            </div>
          </div>
        <% end %>
      <% end %>

      <div id="no-selection" class="flex center-v center mt-4 text-lg">
        Select a project to manage access
      </div>
    </div>
  </div>
</div>

<script>
function showProjectAuth(projectId) {
  document.querySelectorAll('.project-auth-panel').forEach(panel => panel.classList.add('hidden'));
  document.getElementById('no-selection').classList.add('hidden');
  document.getElementById(`project-auth-${projectId}`).classList.remove('hidden');
  document.querySelectorAll('.project-card').forEach(box => box.classList.remove('active'));
  event.currentTarget.classList.add('active');
}
</script>