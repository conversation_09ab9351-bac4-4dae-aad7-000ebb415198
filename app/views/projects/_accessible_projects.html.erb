<div class="projects-filter">
    <div class="filter-controls">
      <div>
      </div>
      <div>
      </div>
      <div>
      </div>
    </div>
</div>
<div class="projects-list">
  <% if projects.present? %>
    <% projects.each do |project_with_auth| %>
      <div class="project-card">
        <div class="project-visibility">
          <% if current_user == project_with_auth.user %>
              <span class="visibility-badge">
                Sharing: <strong><%= project_with_auth.summary_only ? "Title only " : "Everything " %></strong>
                with: <strong><%= project_with_auth.semi_public ? "Everyone " : "My Network " %></strong>
              </span>
          <% end %>

          <% if project_with_auth.auth_level %>
            <span class="access-badge access-badge--<%= ProjectAuth.access_levels.key(project_with_auth.auth_level) %>">
              Project access: <%= ProjectAuth.access_levels.key(project_with_auth.auth_level).humanize %>
            </span>
          <% end %>
        </div>

        <div class="project-content">
          <p><%= project_with_auth.summary %></p>
          <p class="project-location"><%= project_with_auth.location %></p>
          <p class="project-timestamp">Last update: <%= project_with_auth.updated_at.strftime("%b %d, %Y") %></p>
        </div>

        <div class="project-actions">
          <% if current_user.id == project_with_auth.user_id %>
          
            <%= link_to "Edit", edit_project_path(project_with_auth), class: 'action-link' %> |
            <%= link_to "Show this project", project_with_auth, class: 'action-link' %>
          
          <% else %>
            <% if project_with_auth.full_access %>
              <%= link_to "Show this project", project_with_auth, class: 'action-link' %>
            <% elsif project_with_auth.summary_only %>
              <% if !project_with_auth.auth_level %>
                <%= link_to "Request access", 
                          new_connection_request_path(project_id: project_with_auth.id), 
                          class: 'modal-trigger action-link' %>
              <% elsif project_with_auth.auth_level == 3 %>
                <%= link_to "Show this project", project_with_auth, class: 'action-link' %>
              <% else %>
                <%= button_to "Delete Request",
                              connection_request_path(project_with_auth.auth_id),
                              method: :post, class: "action-button action-button--reject" %>
              <% end %>
            <% end %>
          <% end %>
        </div>
      </div>
    <% end %>
  <% end %>
</div>