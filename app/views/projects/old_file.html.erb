<div id="projects" class="list">
  <div class="list-item">
    <div>
      <%= link_to "My projects", my_projects_projects_path %> |
      <%= link_to "Requested projects", my_projects_projects_path %> 
    </div>
    <div>
      <%= link_to "Add new project", new_project_path, class:'button' %>
    </div>
  </div>
  <br/>
  <% if @projects.present? %>
    <% @projects.each do |project| %>
      <div class="list-box">
        <div>
          <p>
            <%= project.summary %>
          </p>
          <br/>
          <p>
            Last update: <%= project.updated_at.strftime("%m/%d/%Y") %>
          </p>
          <br/>

          <% if current_user == project.user %> 
            <div class="bg-light p-1">
              <p>Project owner's information</p>
              <p>
                <strong>Public visible:</strong>
                <%= project.public_visible %>

                <strong>Title only:</strong>
                <%= project.summary_only %>

                <strong>Full access:</strong>
                <%= project.full_access %>

              </p>
            </div>
          <% end %>
        
        </div>

        <div class="actions">
          <% if current_user == project.user %>
            <%= link_to "Edit", edit_project_path(project) %> |
          <% else %>
            <% if project.full_access %>
              <%= link_to "Show this project", project %>
            <% elsif project.summary_only %>
            <% else %>
            <% end %>
          <% end %>
        </div>
      </div>
    <% end %>
  <% else %>
    <p>No projects found.</p>
  <% end %>
</div>




