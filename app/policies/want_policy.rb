# ABOUTME: WantPolicy that follows exact same authorization patterns as ProjectPolicy  
# ABOUTME: Implements same permission checks and access control rules

class WantPolicy < ApplicationPolicy
  def index?
    true # All authenticated users can view wants
  end

  def show_my?
    user.present?
  end

  def show?
    true # All authenticated users can view individual wants
  end

  def create?
    user.present?
  end

  def new?
    create?
  end

  def edit?
    owner?
  end

  def update?
    owner?
  end

  def destroy?
    owner?
  end

  private

  def owner?
    user == record.user
  end
end 