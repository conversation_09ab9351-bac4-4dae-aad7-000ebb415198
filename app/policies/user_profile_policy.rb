class UserProfilePolicy < ApplicationPolicy

  def view_contact_details?
    # Allow viewing contact details if it's the user's own profile
    return true if record.user_id == user.id

    # Allow viewing contact details if there is a connection between the user and the record
    NetworkConnection.where(inviter: [record.user_id, user.id])
    .where(invitee: [record.user_id, user.id])
    .exists?

  end
  
end