document.addEventListener('DOMContentLoaded', () => {
  const fileDetailView = document.getElementById('file-detail-view');
  if (!fileDetailView) return;

  const fullFileContainer = document.getElementById('full-file-container');
  const mainProjectView = document.querySelector('.show-box'); 
  const backToGridViewBtn = document.getElementById('back-to-grid-view');
  const thumbnailList = document.getElementById('thumbnail-list');

  // --- Functions ---

  function displayFile(fileElement) {
    const inlineUrl = fileElement.dataset.inlineUrl;
    const contentType = fileElement.dataset.contentType;
    
    fullFileContainer.innerHTML = ''; // Clear previous content

    if (contentType.startsWith('image/')) {
      const img = document.createElement('img');
      img.src = inlineUrl;
      img.alt = fileElement.dataset.filename;
      fullFileContainer.appendChild(img);
    } else if (contentType === 'application/pdf') {
      const object = document.createElement('object');
      object.data = inlineUrl;
      object.type = 'application/pdf';
      object.width = '100%';
      object.height = '100%';
      
      const fallbackLink = document.createElement('a');
      fallbackLink.href = inlineUrl;
      fallbackLink.textContent = 'Download PDF';
      object.appendChild(fallbackLink);
      
      fullFileContainer.appendChild(object);
    } else {
      // Fallback for other file types using an iframe
      const iframe = document.createElement('iframe');
      iframe.src = inlineUrl;
      iframe.width = '100%';
      iframe.height = '100%';
      iframe.style.border = 'none';
      fullFileContainer.appendChild(iframe);
    }

    // Update active state in thumbnail sidebar
    const fileId = fileElement.dataset.fileId;
    document.querySelectorAll('.thumbnail-item').forEach(thumb => {
      thumb.classList.remove('active');
      if (thumb.dataset.fileId === fileId) {
        thumb.classList.add('active');
      }
    });
  }

  function showDetailView(fileElement) {
    displayFile(fileElement);
    fileDetailView.classList.add('visible');
    mainProjectView.style.display = 'none';
    document.body.style.overflow = 'hidden';
  }

  function hideDetailView() {
    fileDetailView.classList.remove('visible');
    mainProjectView.style.display = '';
    document.body.style.overflow = '';
    fullFileContainer.innerHTML = ''; // Clean up
  }

  // --- Event Listeners ---

  // Open detail view from the grid
  document.querySelectorAll('.file-item[data-action="view-file"]').forEach(item => {
    item.addEventListener('click', (e) => {
      e.preventDefault();
      showDetailView(item);
    });
  });

  // Select a file from the sidebar
  thumbnailList.addEventListener('click', (e) => {
    const thumbItem = e.target.closest('.thumbnail-item[data-action="select-file"]');
    if (thumbItem) {
      displayFile(thumbItem);
    }
  });

  // Close detail view
  backToGridViewBtn.addEventListener('click', () => {
    hideDetailView();
  });
  
  // Close with escape key
  document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape' && fileDetailView.classList.contains('visible')) {
      hideDetailView();
    }
  });

}); 