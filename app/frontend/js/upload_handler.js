// ABOUTME: Modern file upload handler for server-side uploads with progress tracking
// ABOUTME: Integrates with Action Cable for real-time updates and Ready Flag pattern

class UploadHandler {
  constructor() {
    this.activeUploads = new Map();
    this.maxFileSize = 100 * 1024 * 1024; // 100MB in bytes
    this.maxFiles = 10;
    this.allowedTypes = [
      'application/pdf',
      'image/png', 
      'image/jpeg',
      'image/tiff',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'text/plain',
      'text/csv'
    ];
    
    // Initialize translations
    this.translations = this.loadTranslations();
    
    this.initializeEventListeners();
  }

  loadTranslations() {
    const translationsElement = document.getElementById('js-translations');
    if (translationsElement) {
      return {
        file_uploads: translationsElement.dataset.fileUploads || 'File Uploads',
        loading_file: translationsElement.dataset.loadingFile || 'Loading file',
        select_files_first: translationsElement.dataset.selectFilesFirst || 'Please save the project first before uploading files',
        upload_failed: translationsElement.dataset.uploadFailed || 'Upload failed',
        upload_successful: translationsElement.dataset.uploadSuccessful || 'uploaded successfully',
        too_many_files: translationsElement.dataset.tooManyFiles || 'Maximum %{max} files allowed per upload',
        file_too_large: translationsElement.dataset.fileTooLarge || '%{filename} exceeds the maximum size of %{max_size}',
        unsupported_file_type: translationsElement.dataset.unsupportedFileType || '%{filename} has unsupported file type: %{type}',
        rate_limit_exceeded: translationsElement.dataset.rateLimitExceeded || 'Rate limit exceeded. Try again later.',
        statuses: {
          preparing: translationsElement.dataset.statusPreparing || 'Preparing upload',
          pending: translationsElement.dataset.statusPending || 'Uploading',
          transferred: translationsElement.dataset.statusTransferred || 'Transfer',
          processing: translationsElement.dataset.statusProcessing || 'Processing',
          completed: translationsElement.dataset.statusCompleted || 'Upload complete!',
          failed: translationsElement.dataset.statusFailed || 'Upload failed - please try again',
          cancelled: translationsElement.dataset.statusCancelled || 'Upload cancelled'
        },
        actions: {
          cancel: translationsElement.dataset.actionCancel || 'Cancel'
        }
      };
    }
    
    // Fallback to English
    return {
      file_uploads: 'File Uploads',
      loading_file: 'Loading file',
      select_files_first: 'Please save the project first before uploading files',
      upload_failed: 'Upload failed',
      upload_successful: 'uploaded successfully',
      too_many_files: 'Maximum %{max} files allowed per upload',
      file_too_large: '%{filename} exceeds the maximum size of %{max_size}',
      unsupported_file_type: '%{filename} has unsupported file type: %{type}',
      rate_limit_exceeded: 'Rate limit exceeded. Try again later.',
      statuses: {
        preparing: 'Preparing upload',
        pending: 'Preparing upload',
        transferred: 'Transfer',
        processing: 'Processing',
        completed: 'Upload complete!',
        failed: 'Upload failed - please try again',
        cancelled: 'Upload cancelled'
      },
      actions: {
        cancel: 'Cancel'
      }
    };
  }

  initializeEventListeners() {
    // Handle file input changes
    document.addEventListener('change', (e) => {
      if (e.target.matches('input[type="file"][data-upload-handler]')) {
        this.handleFileSelection(e.target);
      }
    });

    // Handle drag and drop
    document.addEventListener('dragover', (e) => {
      const dropZone = e.target.closest('.upload-drop-zone');
      if (dropZone) {
        e.preventDefault();
        dropZone.classList.add('drag-over');
      }
    });

    document.addEventListener('dragleave', (e) => {
      const dropZone = e.target.closest('.upload-drop-zone');
      if (dropZone && !dropZone.contains(e.relatedTarget)) {
        dropZone.classList.remove('drag-over');
      }
    });

    document.addEventListener('drop', (e) => {
      const dropZone = e.target.closest('.upload-drop-zone');
      if (dropZone) {
        e.preventDefault();
        dropZone.classList.remove('drag-over');
        
        const fileInput = dropZone.querySelector('input[type="file"]');
        if (fileInput) {
          fileInput.files = e.dataTransfer.files;
          this.handleFileSelection(fileInput);
        }
      }
    });

    // Handle drop zone clicks to trigger file picker
    document.addEventListener('click', (e) => {
      const dropZone = e.target.closest('.upload-drop-zone');
      if (dropZone && !e.target.matches('input[type="file"]')) {
        const fileInput = dropZone.querySelector('input[type="file"]');
        if (fileInput) {
          fileInput.click();
        }
      }
    });

    // Handle upload cancellation
    document.addEventListener('click', (e) => {
      if (e.target.matches('.cancel-upload-btn')) {
        const uploadId = e.target.dataset.uploadId;
        this.cancelUpload(uploadId);
      }
    });
  }

  async handleFileSelection(fileInput) {
    const files = Array.from(fileInput.files);
    const targetType = fileInput.dataset.targetType || 'Project';
    const targetId = fileInput.dataset.targetId;
    
    console.log('UploadHandler: File selection started', { fileCount: files.length, targetType, targetId });
    
    if (!this.validateFiles(files)) {
      return;
    }

    if (!targetId) {
      this.showError(this.translations.select_files_first);
      return;
    }

    // Show upload progress container
    this.showUploadContainer();

    // Process files in batches to avoid overwhelming the server
    const batches = this.createFileBatches(files, 5);
    
    for (const batch of batches) {
      await this.uploadBatch(batch, targetType, targetId);
    }
  }

  validateFiles(files) {
    const errors = [];

    if (files.length === 0) {
      errors.push('Please select files to upload');
    }

    if (files.length > this.maxFiles) {
      errors.push(this.translations.too_many_files.replace('%{max}', this.maxFiles));
    }

    for (const file of files) {
      if (file.size > this.maxFileSize) {
        errors.push(this.translations.file_too_large
          .replace('%{filename}', file.name)
          .replace('%{max_size}', this.formatFileSize(this.maxFileSize)));
      }

      if (!this.allowedTypes.includes(file.type)) {
        errors.push(this.translations.unsupported_file_type
          .replace('%{filename}', file.name)
          .replace('%{type}', file.type));
      }
    }

    if (errors.length > 0) {
      this.showErrors(errors);
      return false;
    }

    this.clearErrors();
    return true;
  }

  createFileBatches(files, batchSize) {
    const batches = [];
    for (let i = 0; i < files.length; i += batchSize) {
      batches.push(files.slice(i, i + batchSize));
    }
    return batches;
  }

  async uploadBatch(files, targetType, targetId) {
    const formData = new FormData();
    
    // Add files to form data
    files.forEach(file => {
      formData.append('files[]', file);
    });
    
    formData.append('target_type', targetType);
    formData.append('target_id', targetId);

    // Create placeholder upload UI elements immediately for better UX
    const placeholderUploads = files.map((file, index) => ({
      id: `placeholder-${Date.now()}-${index}`,
      original_filename: file.name,
      file_size: file.size,
      status: 'Uploading',
      progress_percentage: 0,
      isPlaceholder: true
    }));

    // Show placeholders immediately
    placeholderUploads.forEach(upload => {
      this.createUploadProgressElement(upload);
    });

    try {
      console.log('UploadHandler: Sending upload request to /uploads');
      
      // Use XMLHttpRequest for upload progress tracking
      const result = await this.uploadWithProgress(formData, placeholderUploads);
      console.log('UploadHandler: Upload result parsed', result);
      
      // Handle single or multiple upload responses
      const uploads = result.uploads || [result];
      
      // Replace placeholders with real upload tracking
      placeholderUploads.forEach((placeholder, index) => {
        this.removeUploadElement(placeholder.id);
        if (uploads[index]) {
          this.trackUpload(uploads[index]);
        }
      });

    } catch (error) {
      console.error('Upload batch failed:', error);
      // Remove placeholders on error
      placeholderUploads.forEach(upload => {
        this.removeUploadElement(upload.id);
      });
      this.showError(`${this.translations.upload_failed}: ${error.message}`);
    }
  }

  uploadWithProgress(formData, placeholderUploads) {
    return new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest();
      let startTime = Date.now();
      let lastLoaded = 0;
      let lastTime = Date.now();
      
      // Track upload progress for user -> server phase
      xhr.upload.addEventListener('progress', (e) => {
        if (e.lengthComputable) {
          const uploadProgress = Math.round((e.loaded / e.total) * 100);
          const currentTime = Date.now();
          const elapsedSeconds = (currentTime - lastTime) / 1000;
          
          console.log(`Upload progress: ${uploadProgress}%`);
          
          // Calculate network speed with moving window (avoid division by zero and noisy initial readings)
          if (elapsedSeconds > 1) {
            const bytesPerSecond = (e.loaded - lastLoaded) / elapsedSeconds;
            
            // Detect slow network (less than 100KB/s) after 10% progress to avoid false positives
            if (bytesPerSecond < 100000 && uploadProgress > 10) {
              placeholderUploads.forEach(upload => {
                this.showSlowNetworkWarning(upload.id);
              });
            }
            
            lastLoaded = e.loaded;
            lastTime = currentTime;
          }
          
          // Update all placeholder progress bars
          placeholderUploads.forEach(upload => {
            this.updatePlaceholderProgress(upload.id, uploadProgress);
          });
        }
      });
      
      xhr.addEventListener('load', () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          try {
            const result = JSON.parse(xhr.responseText);
            resolve(result);
          } catch (e) {
            reject(new Error('Invalid JSON response'));
          }
        } else {
          try {
            const errorData = JSON.parse(xhr.responseText);
            reject(new Error(errorData.errors?.join(', ') || 'Upload failed'));
          } catch (e) {
            reject(new Error(`Upload failed with status ${xhr.status}`));
          }
        }
      });
      
      xhr.addEventListener('error', () => {
        reject(new Error('Network error during upload'));
      });
      
      xhr.addEventListener('timeout', () => {
        reject(new Error('Upload timeout'));
      });
      
      // Configure request
      xhr.open('POST', '/uploads');
      xhr.setRequestHeader('X-CSRF-Token', document.querySelector('meta[name="csrf-token"]')?.getAttribute('content'));
      xhr.timeout = 300000; // 5 minutes timeout
      
      // Send the request
      xhr.send(formData);
    });
  }

  updatePlaceholderProgress(uploadId, progress) {
    const uploadElement = document.getElementById(`upload-${uploadId}`);
    if (!uploadElement) return;

    const progressBar = uploadElement.querySelector('.upload-progress-bar');
    const progressText = uploadElement.querySelector('.upload-progress-text');

    if (progressBar) {
      progressBar.style.width = `${progress}%`;
    }

    if (progressText) {
      progressText.textContent = `${progress}%`;
    }
  }

  trackUpload(upload) {
    // Add to active uploads
    this.activeUploads.set(upload.id, upload);

    // Create progress UI element
    this.createUploadProgressElement(upload);

    // Subscribe to Action Cable for real-time updates
    this.subscribeToUploadProgress(upload);

    // Start watchdog timer
    this.startUploadWatchdog(upload);
  }

  subscribeToUploadProgress(upload) {
    if (window.App && window.App.cable) {
      // Use Action Cable when available
      const subscription = window.App.cable.subscriptions.create(
        {
          channel: 'UploadChannel',
          upload_signed_id: upload.signed_id
        },
        {
          received: (data) => {
            upload.lastUpdateTime = Date.now();
            upload.isStalled = false;
            this.updateUploadProgress(data);
          },
          
          connected: () => {
            console.log(`Connected to upload ${upload.id}`);
            // Immediately poll for current status to eliminate race condition
            this.pollUploadStatus(upload);
          },
          
          disconnected: () => {
            console.log(`Disconnected from upload ${upload.id}, attempting reconnect...`);
            this.handleWebSocketDisconnect(upload);
          }
        }
      );

      // Store subscription for cleanup
      upload.subscription = subscription;
    } else {
      // Fallback to polling
      console.log('Action Cable not available, using polling fallback');
      this.startPollingUpload(upload);
    }
  }

  handleWebSocketDisconnect(upload) {
    upload.reconnectAttempts = (upload.reconnectAttempts || 0) + 1;

    if (upload.reconnectAttempts > 10) {
      console.error(`Failed to reconnect for upload ${upload.id} after 10 attempts.`);
      this.handlePermanentFailure(upload, 'Connection to server lost.');
      return;
    }

    // Exponential backoff: 1s, 2s, 4s, 8s, 16s, max 30s
    const delay = Math.min(30000, Math.pow(2, upload.reconnectAttempts - 1) * 1000);

    upload.reconnectTimer = setTimeout(() => {
      console.log(`Reconnect attempt ${upload.reconnectAttempts} for upload ${upload.id}`);

      // Clean up old subscription before reconnecting
      if (upload.subscription) {
        upload.subscription.unsubscribe();
      }

      // Reconnect
      this.subscribeToUploadProgress(upload);
    }, delay);
  }

  handlePermanentFailure(upload, message) {
    console.error(`Permanent failure for upload ${upload.id}: ${message}`);
    
    // Update UI to show permanent failure
    const statusElement = document.querySelector(`#upload-${upload.id} .upload-status`);
    if (statusElement) {
      statusElement.textContent = message;
      statusElement.classList.add('upload-status-error');
    }

    // Show error in upload element
    const uploadElement = document.getElementById(`upload-${upload.id}`);
    if (uploadElement) {
      uploadElement.className = 'upload-item upload-failed';
      const errorText = uploadElement.querySelector('.upload-error');
      if (errorText) {
        errorText.textContent = message;
        errorText.style.display = 'block';
      }
    }

    // Clean up and remove from active uploads
    this.cleanupUploadSubscription(upload);
    this.activeUploads.delete(upload.id);
  }

  startUploadWatchdog(upload) {
    upload.lastUpdateTime = Date.now();
    upload.watchdogTimer = setInterval(() => {
      const timeSinceUpdate = Date.now() - upload.lastUpdateTime;

      if (timeSinceUpdate > 30000 && !upload.isStalled) {
        this.handleStalledUpload(upload);
      }
    }, 5000);
  }

  handleStalledUpload(upload) {
    upload.isStalled = true;
    console.warn(`Upload ${upload.id} appears stalled, attempting recovery...`);

    // Update UI to show stalled state
    const statusElement = document.querySelector(`#upload-${upload.id} .upload-status`);
    if (statusElement) {
      statusElement.textContent = 'Connection lost - retrying...';
      statusElement.classList.add('upload-status-warning');
    }

    // Force a status check via polling
    this.pollUploadStatus(upload);
  }

  async pollUploadStatus(upload) {
    try {
      const response = await fetch(`/uploads/${upload.id}`);
      if (response.ok) {
        const data = await response.json();
        upload.lastUpdateTime = Date.now();
        upload.isStalled = false;
        this.updateUploadProgress(data);
      } else {
        console.warn(`Status poll failed for upload ${upload.id}: ${response.status}`);
      }
    } catch (error) {
      console.error(`Status poll error for upload ${upload.id}:`, error);
    }
  }

  startPollingUpload(upload) {
    const pollInterval = setInterval(async () => {
      try {
        const response = await fetch(`/uploads/${upload.id}`);
        if (response.ok) {
          const data = await response.json();
          this.updateUploadProgress(data);
          
          // Stop polling when upload is complete or failed
          if (data.status === 'completed' || data.status === 'failed' || data.status === 'cancelled') {
            clearInterval(pollInterval);
          }
        } else {
          // Upload might have been deleted, stop polling
          clearInterval(pollInterval);
        }
      } catch (error) {
        console.error('Polling error:', error);
        clearInterval(pollInterval);
      }
    }, 1000); // Poll every second

    // Store interval for cleanup
    upload.pollInterval = pollInterval;
  }

  updateUploadProgress(data) {
    const uploadElement = document.getElementById(`upload-${data.id}`);
    if (!uploadElement) return;

    const upload = this.activeUploads.get(data.id);

    // Stop any existing animation interval
    if (upload && upload.statusAnimationInterval) {
      clearInterval(upload.statusAnimationInterval);
      upload.statusAnimationInterval = null;
    }

    const progressBar = uploadElement.querySelector('.upload-progress-bar');
    const statusText = uploadElement.querySelector('.upload-status');
    const progressText = uploadElement.querySelector('.upload-progress-text');

    if (progressBar) {
      progressBar.style.width = `${data.progress || data.progress_percentage || 0}%`;
    }

    if (progressText) {
      progressText.textContent = `${data.progress || data.progress_percentage || 0}%`;
    }

    if (statusText) {
      const baseText = this.getStatusText(data.status);
      statusText.textContent = baseText;

      const ongoingStates = ['preparing', 'pending', 'transferred', 'processing'];
      if (ongoingStates.includes(data.status)) {
        let dotCount = 0;
        const animateEllipsis = () => {
          dotCount = (dotCount + 1) % 4;
          const ellipsis = '.'.repeat(dotCount);
          statusText.textContent = baseText + ellipsis;
        };

        if (upload) {
          upload.statusAnimationInterval = setInterval(animateEllipsis, 400);
        }
      }
    }

    // Update upload element class based on status
    uploadElement.className = `upload-item upload-${data.status}`;

    // Update phase indicators
    this.updatePhaseIndicators(uploadElement, data.status);

    // Update cancel button visibility based on status
    this.updateCancelButton(uploadElement, data.status);

    // Handle completion
    if (data.status === 'completed') {
      this.handleUploadComplete(data);
    } else if (data.status === 'failed') {
      this.handleUploadFailed(data);
    }
  }

  handleUploadComplete(data) {
    const upload = this.activeUploads.get(data.id);
    this.cleanupUploadSubscription(upload);
    this.activeUploads.delete(data.id);

    // Show success message
    setTimeout(() => {
      this.removeUploadElement(data.id);
      this.showSuccessMessage(`${data.original_filename} ${this.translations.upload_successful}`);
      
      // Refresh the page to show new files (could be improved with dynamic updates)
      if (this.activeUploads.size === 0) {
        setTimeout(() => window.location.reload(), 1000);
      }
    }, 2000);
  }

  handleUploadFailed(data) {
    const upload = this.activeUploads.get(data.id);
    this.cleanupUploadSubscription(upload);
    this.activeUploads.delete(data.id);

    const uploadElement = document.getElementById(`upload-${data.id}`);
    if (uploadElement) {
      const errorText = uploadElement.querySelector('.upload-error');
      if (errorText) {
        errorText.textContent = data.error_message || 'Upload failed';
        errorText.style.display = 'block';
      }
    }
  }

  updateCancelButton(uploadElement, status) {
    const cancelBtn = uploadElement.querySelector('.cancel-upload-btn');
    if (!cancelBtn) return;

    // Only allow cancellation in pending and transferred states
    // Match the backend can_cancel? logic
    const canCancel = status === 'pending' || status === 'transferred';
    
    if (canCancel) {
      cancelBtn.style.display = 'block';
      cancelBtn.disabled = false;
    } else {
      // Hide cancel button for processing, completed, failed, cancelled states
      cancelBtn.style.display = 'none';
      
      // Update status text for processing state to be informative
      /* This is now handled in updateUploadProgress
      if (status === 'processing') {
        const statusText = uploadElement.querySelector('.upload-status');
        if (statusText) {
          statusText.textContent = 'Uploading to cloud (cannot cancel)...';
        }
      }
      */
    }
  }

  async cancelUpload(uploadId) {
    try {
      const response = await fetch(`/uploads/${uploadId}`, {
        method: 'DELETE',
        headers: {
          'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content'),
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const upload = this.activeUploads.get(parseInt(uploadId));
        this.cleanupUploadSubscription(upload);
        this.activeUploads.delete(parseInt(uploadId));
        this.removeUploadElement(uploadId);
        console.log(`Upload ${uploadId} cancelled successfully`);
      } else {
        // Handle server errors (like upload in non-cancellable state)
        const errorData = await response.json();
        console.error(`Cancel failed: ${errorData.errors?.join(', ') || 'Unknown error'}`);
        
        // Update UI to show the error
        const uploadElement = document.getElementById(`upload-${uploadId}`);
        if (uploadElement) {
          const statusText = uploadElement.querySelector('.upload-status');
          if (statusText) {
            statusText.textContent = errorData.errors?.[0] || 'Cancellation failed';
          }
        }
      }
    } catch (error) {
      console.error('Cancel upload failed:', error);
    }
  }

  cleanupUploadSubscription(upload) {
    if (!upload) return;
    
    // Cleanup Action Cable subscription
    if (upload.subscription) {
      upload.subscription.unsubscribe();
    }
    
    // Cleanup polling interval
    if (upload.pollInterval) {
      clearInterval(upload.pollInterval);
    }

    // Cleanup watchdog timer
    if (upload.watchdogTimer) {
      clearInterval(upload.watchdogTimer);
    }

    // Cleanup reconnect timer
    if (upload.reconnectTimer) {
      clearTimeout(upload.reconnectTimer);
    }
  }

  createUploadProgressElement(upload) {
    const container = this.getUploadContainer();
    
    const uploadElement = document.createElement('div');
    uploadElement.id = `upload-${upload.id}`;
    uploadElement.className = `upload-item upload-${upload.status || 'pending'}`;
    
    uploadElement.innerHTML = `
      <div class="upload-info">
        <div class="upload-filename">${upload.original_filename}</div>
        <div class="upload-filesize">${this.formatFileSize(upload.file_size)}</div>
      </div>
      
      <!-- Multi-phase progress indicator (commented out) -->
      <!--
      <div class="upload-phases">
        <div class="phase phase-upload ${this.getPhaseActiveClass(upload.status, 'pending')}">
          <span class="phase-icon">📤</span>
          <span class="phase-text">Upload</span>
        </div>
        <div class="phase phase-transfer ${this.getPhaseActiveClass(upload.status, 'transferred')}">
          <span class="phase-icon">💾</span>
          <span class="phase-text">Save</span>
        </div>
        <div class="phase phase-process ${this.getPhaseActiveClass(upload.status, 'processing')}">
          <span class="phase-icon">☁️</span>
          <span class="phase-text">Store</span>
        </div>
        <div class="phase phase-complete ${this.getPhaseActiveClass(upload.status, 'completed')}">
          <span class="phase-icon">✅</span>
          <span class="phase-text">Done</span>
        </div>
      </div>
      -->
      
      <div class="upload-progress">
        <div class="upload-progress-track">
          <div class="upload-progress-bar" style="width: 0%"></div>
        </div>
        <div class="upload-progress-text">0%</div>
      </div>
      
      <div class="upload-status">${this.getStatusText(upload.status || 'preparing')}</div>
      
      <!-- Cancel button (commented out) -->
      <!--
      <div class="upload-actions">
        <button type="button" class="cancel-upload-btn" data-upload-id="${upload.id}">
          ${this.translations.actions.cancel}
        </button>
      </div>
      -->
      
      <div class="upload-error" style="display: none;"></div>
      <div class="upload-network-warning" style="display: none;">
        <span class="warning-icon">⚠️</span>
        <span class="warning-text">Slow network detected - upload may take longer</span>
      </div>
    `;
    
    container.appendChild(uploadElement);
    
    // Initialize cancel button state based on upload status
    this.updateCancelButton(uploadElement, upload.status || 'pending');
  }

  removeUploadElement(uploadId) {
    const element = document.getElementById(`upload-${uploadId}`);
    if (element) {
      element.remove();
    }

    // Hide container if no uploads remaining
    if (this.getUploadContainer().children.length === 0) {
      this.hideUploadContainer();
    }
  }

  getUploadContainer() {
    let container = document.getElementById('upload-progress-container');
    if (!container) {
      container = this.createUploadContainer();
    }
    return container;
  }

  createUploadContainer() {
    const container = document.createElement('div');
    container.id = 'upload-progress-container';
    container.className = 'upload-progress-container';
    container.style.display = 'none';
    
    const header = document.createElement('div');
    header.className = 'upload-container-header';
    header.innerHTML = `<h4>${this.translations.file_uploads}</h4>`;
    
    container.appendChild(header);
    
    // Insert after file upload section
    const fileSection = document.querySelector('.form-card');
    if (fileSection) {
      fileSection.parentNode.insertBefore(container, fileSection.nextSibling);
    }
    
    return container;
  }

  showUploadContainer() {
    const container = this.getUploadContainer();
    container.style.display = 'block';
  }

  hideUploadContainer() {
    const container = document.getElementById('upload-progress-container');
    if (container) {
      container.style.display = 'none';
    }
  }

  getStatusText(status) {
    return this.translations.statuses[status] || status;
  }

  getPhaseActiveClass(currentStatus, phaseStatus) {
    const statusOrder = ['pending', 'transferred', 'processing', 'completed'];
    const currentIndex = statusOrder.indexOf(currentStatus);
    const phaseIndex = statusOrder.indexOf(phaseStatus);
    
    if (currentIndex >= phaseIndex) {
      return 'active';
    }
    return '';
  }

  updatePhaseIndicators(uploadElement, status) {
    const phases = uploadElement.querySelectorAll('.phase');
    
    phases.forEach(phase => {
      phase.classList.remove('active');
    });
    
    // Activate phases based on current status
    const statusToPhaseMap = {
      'pending': 'phase-upload',
      'transferred': 'phase-transfer', 
      'processing': 'phase-process',
      'completed': 'phase-complete'
    };
    
    const statusOrder = ['pending', 'transferred', 'processing', 'completed'];
    const currentIndex = statusOrder.indexOf(status);
    
    for (let i = 0; i <= currentIndex; i++) {
      const phaseClass = statusToPhaseMap[statusOrder[i]];
      const phaseElement = uploadElement.querySelector(`.${phaseClass}`);
      if (phaseElement) {
        phaseElement.classList.add('active');
      }
    }
  }

  showSlowNetworkWarning(uploadId) {
    const warning = document.querySelector(`#upload-${uploadId} .upload-network-warning`);
    if (warning && warning.style.display === 'none') {
      warning.style.display = 'flex';
      console.log(`Slow network detected for upload ${uploadId}`);
    }
  }

  formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  showError(message) {
    this.showMessage(message, 'error');
  }

  showErrors(errors) {
    errors.forEach(error => this.showError(error));
  }

  showSuccessMessage(message) {
    this.showMessage(message, 'success');
  }

  showMessage(message, type = 'info') {
    // Create or update message container
    let messageContainer = document.getElementById('upload-messages');
    if (!messageContainer) {
      messageContainer = document.createElement('div');
      messageContainer.id = 'upload-messages';
      messageContainer.className = 'upload-messages';
      
      // Insert after file section
      const fileSection = document.querySelector('.form-card');
      if (fileSection) {
        fileSection.parentNode.insertBefore(messageContainer, fileSection.nextSibling);
      }
    }

    const messageElement = document.createElement('div');
    messageElement.className = `upload-message upload-message-${type}`;
    messageElement.textContent = message;
    
    messageContainer.appendChild(messageElement);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
      if (messageElement.parentNode) {
        messageElement.remove();
      }
      
      // Remove container if empty
      if (messageContainer.children.length === 0) {
        messageContainer.remove();
      }
    }, 5000);
  }

  clearErrors() {
    const messageContainer = document.getElementById('upload-messages');
    if (messageContainer) {
      messageContainer.remove();
    }
  }

  // Enable uploads for a specific target (called after project is saved)
  enableUploadsForTarget(targetType, targetId) {
    const fileInputs = document.querySelectorAll('input[type="file"][data-upload-handler]');
    fileInputs.forEach(input => {
      if (input.dataset.targetType === targetType) {
        input.dataset.targetId = targetId;
        
        // Enable the drop zone
        const dropZone = input.closest('.upload-drop-zone');
        if (dropZone) {
          dropZone.classList.remove('disabled');
          
          // Hide the notice
          const notice = dropZone.querySelector('.upload-notice');
          if (notice) {
            notice.style.display = 'none';
          }
          
          // Update the text
          const primaryText = dropZone.querySelector('.primary-text');
          if (primaryText) {
            primaryText.textContent = 'Drop files here or click to browse';
          }
        }
      }
    });
  }

  // Disable uploads (used for new projects)
  disableUploads() {
    const dropZones = document.querySelectorAll('.upload-drop-zone');
    dropZones.forEach(dropZone => {
      dropZone.classList.add('disabled');
      dropZone.style.cursor = 'not-allowed';
      dropZone.style.opacity = '0.6';
    });
  }
}

// Export for use in other modules
window.UploadHandler = UploadHandler;

// Auto-initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  window.uploadHandler = new UploadHandler();
});