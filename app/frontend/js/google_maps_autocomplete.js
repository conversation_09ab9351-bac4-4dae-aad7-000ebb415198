export function initializeAutocomplete(inputId) {
  const input = document.getElementById(inputId);
  
  if (!input) return;
  
  const autocomplete = new google.maps.places.Autocomplete(input, {
    types: ['geocode']
  });
  
  autocomplete.addListener('place_changed', function() {
    const place = autocomplete.getPlace();
    
    if (!place.geometry) {
      return;
    }

    const isProjectForm = inputId === 'project_location';
    
    const latFieldId = isProjectForm ? 'project_latitude' : 'latitude';
    const lngFieldId = isProjectForm ? 'project_longitude' : 'longitude';
    const countryFieldId = isProjectForm ? 'project_country' : 'country';
    const countryCodeFieldId = isProjectForm ? 'project_country_code' : 'search_country_code';


    const latField = document.getElementById(latFieldId);
    const lngField = document.getElementById(lngFieldId);
    const countryField = document.getElementById(countryFieldId);
    const countryCodeField = document.getElementById(countryCodeFieldId);
    const isCountrySearchField = document.getElementById('is_country_search');

    // const latField = document.getElementById('project_latitude');
    // const lngField = document.getElementById('project_longitude');
    // const countryField = document.getElementById('project_country');
    // const countryCodeField = document.getElementById('project_country_code');
    // const isCountrySearchField = document.getElementById('is_country_search');

    if (latField) latField.value = place.geometry.location.lat();
    if (lngField) lngField.value = place.geometry.location.lng();
    
    console.log(countryField);
    console.log(place.address_components);

    if (place.address_components) {
      const countryComponent = place.address_components.find(
        component => component.types.includes('country')
      );

      if (countryComponent) {
        if (countryField) countryField.value = countryComponent.long_name;
        if (countryCodeField) countryCodeField.value = countryComponent.short_name;

        const isCountryOnly = place.types && 
                             place.types.includes('country') && 
                             !place.types.some(type => 
                               ['locality', 'administrative_area_level_1', 'administrative_area_level_2'].includes(type)
                             );

        if (isCountrySearchField) {
          isCountrySearchField.value = isCountryOnly ? 'true' : 'false';
        }

      } else if (countryField) {
        if (place.formatted_address && place.formatted_address.includes(',')) {
          const addressParts = place.formatted_address.split(',');
          const lastPart = addressParts[addressParts.length - 1].trim();
          
          if (lastPart.length > 2 && !/^\d+/.test(lastPart)) {
            countryField.value = lastPart;
          }
        }
      }
    }
                    
    // if (countryField && place.address_components) {
    //   const countryComponent = place.address_components.find(
    //     component => component.types.includes('country')
    //   );
      
    //   if (countryComponent) {
    //     if (countryField) countryField.value = countryComponent.long_name;
    //     if (countryCodeField) countryCodeField.value = countryComponent.short_name;

    //     const isCountryOnly = place.types && 
    //                          place.types.includes('country') && 
    //                          !place.types.some(type => 
    //                            ['locality', 'administrative_area_level_1', 'administrative_area_level_2'].includes(type)
    //                          );
        
    //     if (isCountrySearchField) {
    //       isCountrySearchField.value = isCountryOnly ? 'true' : 'false';
    //     }

    //   } else {
    //     if (place.formatted_address && place.formatted_address.includes(',')) {
    //       const addressParts = place.formatted_address.split(',');
    //       const lastPart = addressParts[addressParts.length - 1].trim();
          
    //       if (lastPart.length > 2 && !/^\d+/.test(lastPart)) {
    //         countryField.value = lastPart;
    //       }
    //     }
    //   }
    // }

  });
  
  input.addEventListener('keydown', function(e) {
    if (e.key === 'Enter') {
      e.preventDefault();
    }
  });
}