#!/usr/bin/env ruby
# ABOUTME: Test script to verify full_details filter shows both explicit and automatic access
# ABOUTME: Tests projects with ProjectAuth records AND projects with sharing-based access

require_relative 'config/environment'

# Test user setup - use first available user
test_user = User.find_by(email: '<EMAIL>') || User.first
unless test_user
  puts "❌ No users found in database."
  exit 1
end

puts "Testing with user: #{test_user.email} (ID: #{test_user.id})"
puts "=" * 60

# Clean up any test projects from previous runs
Project.where(summary: ['TEST: Explicit Access Project', 'TEST: Auto Access Semi-Public', 'TEST: Auto Access Network']).destroy_all

# Create test projects
owner = User.where.not(id: test_user.id).first
unless owner
  puts "❌ Need at least one other user to be project owner"
  exit 1
end

puts "Using owner: #{owner.email} (ID: #{owner.id})"

# Ensure users are connected for network_only test
unless NetworkConnection.where(
  '(inviter_id = ? AND invitee_id = ?) OR (inviter_id = ? AND invitee_id = ?)',
  test_user.id, owner.id, owner.id, test_user.id
).exists?
  puts "Creating network connection between test user and owner..."
  NetworkConnection.create!(inviter: test_user, invitee: owner)
end

# 1. Project with explicit ProjectAuth (full_details)
# Use admin_approver to bypass approval validation
project_explicit = owner.projects.new  # Use new instead of build to avoid callbacks
project_explicit.assign_attributes(
  summary: 'TEST: Explicit Access Project',
  location: 'Bratislava',
  project_status: true,
  full_access: false,  # Doesn't matter for explicit auth
  summary_only: true,
  semi_public: false,
  network_only: true,
  project_type: 0,  # Use numeric value to avoid enum override
  category: 1,      # Use numeric value
  subcategory: 5    # Use numeric value
)
# Set admin approval context to bypass validation
admin = User.find_by(role: 'super_boss') || User.find_by(role: 'admin') || owner
project_explicit.admin_approver = admin
project_explicit.is_admin_approval_action = true
project_explicit.approved = true
project_explicit.save!

# Grant explicit access
ProjectAuth.create!(
  project: project_explicit,
  user: test_user,
  access_level: 'full_details'
)

# 2. Project with automatic access via semi_public + full_access
project_auto_public = owner.projects.new
project_auto_public.assign_attributes(
  summary: 'TEST: Auto Access Semi-Public',
  location: 'Košice',
  project_status: true,
  full_access: true,   # Share everything
  summary_only: false,
  semi_public: true,   # Everyone
  network_only: false,
  project_type: 1,     # business
  category: 6,         # franchise
  subcategory: 15      # unspecified
)
project_auto_public.admin_approver = admin
project_auto_public.is_admin_approval_action = true
project_auto_public.approved = true
project_auto_public.save!

# 3. Project with automatic access via network_only + full_access
project_auto_network = owner.projects.new
project_auto_network.assign_attributes(
  summary: 'TEST: Auto Access Network',
  location: 'Prešov',
  project_status: true,
  full_access: true,   # Share everything
  summary_only: false,
  semi_public: false,
  network_only: true,  # My network (test user is connected)
  project_type: 1,     # business
  category: 5,         # private_equity
  subcategory: 12      # startup
)
project_auto_network.admin_approver = admin
project_auto_network.is_admin_approval_action = true
project_auto_network.approved = true
project_auto_network.save!

# 4. Project with NO access (control case)
project_no_access = owner.projects.new
project_no_access.assign_attributes(
  summary: 'TEST: No Access Project',
  location: 'Žilina',
  project_status: true,
  full_access: false,
  summary_only: true,  # Title only
  semi_public: false,
  network_only: true,  # My network but title only
  project_type: 2,     # intellectual_property
  category: 8,         # patent
  subcategory: 15      # unspecified
)
project_no_access.admin_approver = admin
project_no_access.is_admin_approval_action = true
project_no_access.approved = true
project_no_access.save!

puts "\nCreated test projects:"
puts "1. Explicit Access: #{project_explicit.summary} (ID: #{project_explicit.id})"
puts "2. Auto Semi-Public: #{project_auto_public.summary} (ID: #{project_auto_public.id})"
puts "3. Auto Network: #{project_auto_network.summary} (ID: #{project_auto_network.id})"
puts "4. No Access: #{project_no_access.summary} (ID: #{project_no_access.id})"

# Test the queries
puts "\n" + "=" * 60
puts "TESTING FILTERS:"
puts "=" * 60

# Test without filter (all projects)
all_projects = Project.full_list_for_user(test_user)
  .active.approved
  .where("summary LIKE 'TEST:%'")

puts "\n📋 ALL projects visible to user (no access filter):"
all_projects.each do |p|
  auth_status = if p.auth_level.present?
    "ProjectAuth level: #{p.auth_level}"
  else
    "No ProjectAuth"
  end
  puts "  - #{p.summary} (#{auth_status})"
end
puts "  Total: #{all_projects.count}"

# Test with full_details filter
full_details_query = Project.full_list_for_user(test_user)
  .active.approved
  .where("summary LIKE 'TEST:%'")

# Apply the new filter logic
full_details_query = full_details_query.where(
  '(project_auths.access_level = :level) OR 
   (projects.full_access = true AND 
    (projects.semi_public = true OR 
     (projects.network_only = true AND network_connections.id IS NOT NULL)))',
  level: ProjectAuth.access_levels[:full_details]
)

puts "\n✅ FULL DETAILS filter (should show 3 projects):"
full_details_query.each do |p|
  access_reason = []
  if p.auth_level == ProjectAuth.access_levels[:full_details]
    access_reason << "Explicit Auth"
  end
  if p.full_access && p.semi_public
    access_reason << "Auto: semi_public"
  end
  if p.full_access && p.network_only
    access_reason << "Auto: network_only"
  end
  puts "  - #{p.summary} (#{access_reason.join(', ')})"
end
puts "  Total: #{full_details_query.count}"

# Test with pending filter (should be empty for test projects)
pending_query = Project.full_list_for_user(test_user)
  .active.approved
  .where("summary LIKE 'TEST:%'")
  .where('project_auths.access_level = ?', ProjectAuth.access_levels[:pending])

puts "\n⏳ PENDING filter (should show 0 test projects):"
pending_query.each do |p|
  puts "  - #{p.summary}"
end
puts "  Total: #{pending_query.count}"

# Verify each project individually
puts "\n" + "=" * 60
puts "INDIVIDUAL ACCESS VERIFICATION:"
puts "=" * 60

[project_explicit, project_auto_public, project_auto_network, project_no_access].each do |p|
  has_access = p.user_has_access?(test_user)
  puts "\n#{p.summary}:"
  puts "  user_has_access?: #{has_access ? '✅' : '❌'}"
  puts "  full_access: #{p.full_access}"
  puts "  semi_public: #{p.semi_public}"
  puts "  network_only: #{p.network_only}"
  
  auth = ProjectAuth.find_by(project: p, user: test_user)
  if auth
    puts "  ProjectAuth: #{auth.access_level}"
  else
    puts "  ProjectAuth: none"
  end
end

# Summary
puts "\n" + "=" * 60
puts "TEST SUMMARY:"
puts "=" * 60

expected_count = 3
actual_count = full_details_query.count

if actual_count == expected_count
  puts "✅ SUCCESS: Full details filter correctly shows #{actual_count} projects"
  puts "  - 1 with explicit ProjectAuth"
  puts "  - 1 with automatic access (semi_public + full_access)"
  puts "  - 1 with automatic access (network_only + full_access)"
else
  puts "❌ FAILURE: Expected #{expected_count} projects, got #{actual_count}"
end

# Clean up test projects
puts "\nCleaning up test projects..."
Project.where(summary: ['TEST: Explicit Access Project', 'TEST: Auto Access Semi-Public', 'TEST: Auto Access Network', 'TEST: No Access Project']).destroy_all
puts "✅ Cleanup complete"