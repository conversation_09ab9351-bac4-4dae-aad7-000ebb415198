# Vite Configuration Issue - Complete Documentation

## Issue Summary

The browser is stuck in an infinite loop trying to connect to `http://0.0.0.0:3036/vite-dev/`, resulting in continuous `net::ERR_ADDRESS_INVALID` errors.

## Test Results Documentation

### 1. Preflight Check Results (`test_vite_configuration.rb`)

**PASSED (8)**:
- ✓ config/vite.json exists
- ✓ vite.config.ts exists  
- ✓ No conflicting environment variables found
- ✓ Host is set to 0.0.0.0 (allows network access)
- ✓ Port is set to 3036
- ✓ RubyPlugin is configured
- ✓ Vite server is accessible on localhost:3036
- ✓ ViteRuby can generate asset paths

**WARNINGS (1)**:
- ⚠ hmr_host not set - vite-plugin-ruby should handle this automatically

**ERRORS (6)**:
- ✗ Found 'server' block in vite.config.ts - this overrides vite-plugin-ruby!
  - → server.host is set to '0.0.0.0'
  - → hmr.host is set to '0.0.0.0'
- ✗ CRITICAL: Client will try to connect to http://0.0.0.0:3036 (INVALID!)
  - → This causes the infinite connection loop you're experiencing
- ✗ HMR is configured to use 0.0.0.0 - WebSocket will fail!

### 2. RSpec Test Results (`spec/vite_configuration_spec.rb`)

**Failed Tests (6 out of 8)**:
1. ❌ vite.json configuration check - Failed because server block exists in vite.config.ts
2. ❌ Server block detection - Found server block that overrides plugin
3. ❌ Client URL generation - Test setup issue (helper methods not available)
4. ❌ HMR WebSocket - Explicitly configured to use 0.0.0.0
5. ❌ Production behavior - Test setup issue
6. ❌ Network accessibility - Client tries to connect to 0.0.0.0 instead of localhost

### 3. Health Check Results (`bin/vite-health-check`)

**Development Environment**:
- ✓ Vite dev server is required for optimal DX
- ✓ Vite dev server is running on port 3036
- ❌ Configuration sanity - Server block will cause 0.0.0.0 errors
- ❌ Client URL generation - Will try invalid http://0.0.0.0:3036

## Root Cause Confirmed

The tests confirm the exact issue:

1. **Server block in vite.config.ts** is overriding vite-plugin-ruby's configuration
2. This causes the browser to receive URLs with `0.0.0.0` instead of `localhost`
3. Browser cannot connect to `0.0.0.0` (it's not a valid client address)
4. The Vite client retries endlessly, creating the infinite loop

## Current Configuration State

### vite.config.ts (PROBLEMATIC)
```typescript
server: {
  host: '0.0.0.0',     // Server listens on all interfaces ✓
  port: 3036,
  hmr: {
    host: '0.0.0.0'    // Client tries to connect here ✗
  }
}
```

### config/vite.json
```json
{
  "development": {
    "host": "0.0.0.0",  // Plugin can't translate because of override
    "port": 3036
  }
}
```

## Why Previous Fixes Failed

Based on git history and current state:
- The server block was added TODAY (uncommitted changes)
- Previous fixes likely only changed one file, not both
- The server block completely overrides plugin behavior
- Without removing the server block, no amount of vite.json tweaking will help

## Next Steps

The tests are now in place to verify when the issue is fixed. They will pass when:
1. Server block is removed from vite.config.ts
2. vite-plugin-ruby is allowed to manage the configuration
3. Client receives proper localhost URLs instead of 0.0.0.0