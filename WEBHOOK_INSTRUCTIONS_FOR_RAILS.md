# Webhook Instructions for Rails Application

This guide provides the exact steps needed to integrate the Lambda thumbnail generator webhook with your existing Rails application that already has Active Storage configured.


## Required Changes

### 1. Add Webhook Route

Add to your `config/routes.rb`:

```ruby
Rails.application.routes.draw do
  # ... your existing routes ...
  
  post '/wh/thumb_ready', to: 'webhooks/thumbnails#create'
end
```

### 2. Create Webhook Controller

Create `app/controllers/webhooks/thumbnails_controller.rb`:

```ruby
class Webhooks::ThumbnailsController < ApplicationController
  # Disable CSRF protection for webhook endpoint
  skip_before_action :verify_authenticity_token
  before_action :verify_webhook_signature!

  def create
    # Find the original blob by its S3 key
    original_blob = ActiveStorage::Blob.find_by(key: params.require(:original_blob_key))
    
    unless original_blob
      Rails.logger.warn "Webhook received for unknown original blob key: #{params[:original_blob_key]}"
      head :not_found
      return
    end

    # Find the parent record that has this blob attached
    parent_record = original_blob.attachments.first&.record
    
    unless parent_record
      Rails.logger.warn "Could not find parent record for blob key: #{params[:original_blob_key]}"
      head :unprocessable_entity
      return
    end

    # Create the thumbnail blob and attach it
    ActiveRecord::Base.transaction do
      # Create new blob for the thumbnail
      thumbnail_params = params.require(:thumbnail).permit(:key, :filename, :content_type, :byte_size, :checksum)
      thumbnail_blob = ActiveStorage::Blob.create!(thumbnail_params)

      # Attach thumbnail to the parent record
      # Assumes your model has `has_one_attached :thumbnail`
      parent_record.thumbnail.attach(thumbnail_blob)
      
      Rails.logger.info "Successfully attached thumbnail to #{parent_record.class.name} ##{parent_record.id}"
    end

    head :ok
    
  rescue ActionController::ParameterMissing => e
    Rails.logger.error "Invalid webhook payload: #{e.message}"
    render json: { error: e.message }, status: :bad_request
    
  rescue ActiveRecord::RecordInvalid => e
    Rails.logger.error "Failed to create thumbnail blob: #{e.record.errors.full_messages}"
    render json: { error: e.record.errors.full_messages }, status: :unprocessable_entity
  end

  private

  def verify_webhook_signature!
    timestamp = request.headers['X-Signature-Timestamp']
    received_signature = request.headers['X-Signature-Hmac-Sha256']
    
    # Check if headers are present
    unless timestamp && received_signature
      Rails.logger.warn "Webhook missing signature headers"
      head :unauthorized
      return
    end

    # Check timestamp (prevent replay attacks)
    request_time = Time.at(timestamp.to_i)
    if request_time < 5.minutes.ago || request_time > 1.minute.from_now
      Rails.logger.warn "Webhook timestamp outside acceptable range: #{request_time}"
      head :unauthorized
      return
    end

    # Verify HMAC signature
    request_body = request.raw_post
    string_to_sign = "#{timestamp}.#{request_body}"
    
    expected_signature = OpenSSL::HMAC.hexdigest(
      'sha256',
      Rails.application.credentials.thumbnail_webhook_secret,
      string_to_sign
    )

    unless ActiveSupport::SecurityUtils.secure_compare(expected_signature, received_signature)
      Rails.logger.warn "Invalid webhook signature"
      head :unauthorized
      return
    end
  end
end
```

### 3. Add Webhook Secret to Rails Credentials

Edit your Rails credentials:

```bash
EDITOR=nano rails credentials:edit
```

Add this line:
```yaml
thumbnail_webhook_secret: your_generated_webhook_secret_here
```

**Note:** You'll get the webhook secret when you deploy the Lambda function.


## Webhook Payload Structure

The Lambda function will send this payload:

```json
{
  "original_blob_key": "uploads/path/to/document.pdf",
  "thumbnail": {
    "key": "thumbnails/path/to/document.png",
    "filename": "document.png",
    "content_type": "image/png",
    "byte_size": 12345,
    "checksum": "base64encodedmd5hash"
  }
}
```

## Security Features

- **HMAC Authentication**: Uses SHA256 HMAC with shared secret
- **Timestamp Validation**: Prevents replay attacks (5-minute window)
- **CSRF Protection**: Disabled for webhook endpoint only
- **Request Validation**: Validates payload structure and required fields

## Rails Security Hardening (IMPORTANT)

For production environments, add these security enhancements to your webhook controller:

```ruby
class Webhooks::ThumbnailsController < ApplicationController
  skip_before_action :verify_authenticity_token
  before_action :verify_webhook_signature!
  before_action :check_request_size
  before_action :rate_limit_webhook

  def create
    # Find the original blob by its S3 key
    original_blob = ActiveStorage::Blob.find_by(key: params.require(:original_blob_key))
    
    unless original_blob
      Rails.logger.warn "Webhook received for unknown original blob key: #{params[:original_blob_key]}"
      head :not_found
      return
    end

    # Find the parent record that has this blob attached
    parent_record = original_blob.attachments.first&.record
    
    unless parent_record
      Rails.logger.warn "Could not find parent record for blob key: #{params[:original_blob_key]}"
      head :unprocessable_entity
      return
    end

    # SECURITY: Check if thumbnail already exists (idempotency)
    if parent_record.thumbnail.attached?
      Rails.logger.info "Thumbnail already exists for #{parent_record.class.name} ##{parent_record.id}"
      head :ok
      return
    end

    # Create the thumbnail blob and attach it
    ActiveRecord::Base.transaction do
      thumbnail_params = params.require(:thumbnail).permit(:key, :filename, :content_type, :byte_size, :checksum)
      thumbnail_blob = ActiveStorage::Blob.create!(thumbnail_params)
      parent_record.thumbnail.attach(thumbnail_blob)
      Rails.logger.info "Successfully attached thumbnail to #{parent_record.class.name} ##{parent_record.id}"
    end

    head :ok
    
  rescue ActionController::ParameterMissing => e
    Rails.logger.error "Invalid webhook payload: #{e.message}"
    render json: { error: e.message }, status: :bad_request
    
  rescue ActiveRecord::RecordInvalid => e
    Rails.logger.error "Failed to create thumbnail blob: #{e.record.errors.full_messages}"
    render json: { error: e.record.errors.full_messages }, status: :unprocessable_entity
  end

  private

  def check_request_size
    if request.content_length > 5.kilobytes
      Rails.logger.warn "Webhook request too large: #{request.content_length} bytes"
      head :payload_too_large
      return
    end
  end

  def rate_limit_webhook
    # Simple rate limiting - use rack-attack for production
    key = "webhook:#{request.remote_ip}"
    if Rails.cache.read(key).to_i >= 10 # Max 10 requests per minute
      Rails.logger.warn "Rate limit exceeded for webhook from #{request.remote_ip}"
      head :too_many_requests
      return
    end
    Rails.cache.write(key, Rails.cache.read(key).to_i + 1, expires_in: 1.minute)
  end

  def verify_webhook_signature!
    # ... (same as before)
  end
end
```

## S3 Bucket Verification

After Lambda deployment, verify these S3 configurations in your Rails bucket:

### 1. Check Event Notifications
```bash
aws s3api get-bucket-notification-configuration --bucket floating-sierra-56086fd6-d570-470e-82e6-80e348975de7-dev
```

You should see Lambda function triggers for `uploads/*.pdf` files.

### 2. Verify Upload Prefix Configuration
Ensure your `config/storage.yml` has:
```yaml
upload:
  prefix: uploads/
```

### 3. Test S3 Permissions
The Lambda should have these restricted permissions:
- **Read**: `uploads/*` (original PDFs)  
- **Write**: `thumbnails/*` (generated thumbnails)
- **HeadObject**: `thumbnails/*` (metadata collection)

## URL for Lambda Configuration

Your webhook URL will be:
```
https://unlisters.com/wh/thumb_ready
```