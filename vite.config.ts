import { defineConfig } from 'vite'
import RubyPlugin from 'vite-plugin-ruby'

export default defineConfig({
  plugins: [
    RubyPlugin(),
  ],
  // IMPORTANT: Do NOT add a `server` configuration block here.
  // All server settings (host, port, HMR) are managed by `vite-plugin-ruby`
  // via `config/vite.json` to ensure correct behavior across different
  // development environments (local, Docker, WSL).
  // The plugin handles the translation of 0.0.0.0 (server binding) to 
  // localhost (client connection) automatically.
})
