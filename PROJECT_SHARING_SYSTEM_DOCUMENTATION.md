# Project Sharing System - ✅ WORKING AS DESIGNED

## Overview

The Unlisters App implements a **dual-dimensional project sharing system** with automatic and explicit authorization controls. This document provides comprehensive documentation of how project sharing works in the **current implementation** (Updated December 2025).

## Core Architecture

### Two-Dimensional Sharing Model

Projects use a **two-dimensional access control system**:

1. **Visibility Dimension** (Who can see the project exists)
   - `network_only`: Only connected users see the project in listings
   - `semi_public`: All registered users see the project in listings

2. **Detail Level Dimension** (What they can see)
   - `summary_only`: Shows title, location, category only - requires explicit approval
   - `full_access`: **✅ FULLY FUNCTIONAL** - provides automatic access based on visibility settings

### ✅ Implementation Status

**The `full_access` attribute now controls automatic authorization as expected.** Users get the sharing behavior they configure in the UI, with appropriate security controls.

## ✅ Current Authorization Behavior (December 2025)

### Automatic Access Rules (`full_access: true`)

When a project has `full_access: true`, the system automatically grants access based on visibility settings:

- **`full_access + semi_public`**: All authenticated users can view full project details
- **`full_access + network_only`**: Only users connected to the project owner can view full details
- **Security Requirement**: Project must be `approved: true` for any non-owner access

### Manual Approval Rules (`summary_only: true`)

When a project has `summary_only: true`, access requires explicit approval:

- Users see basic project information (title, location, category)
- Full details require requesting access and owner approval
- Creates ProjectAuth entries for approved access

### Owner Access

Project owners always have full access to their projects, regardless of approval status or sharing settings.

### Security Controls

- **Approval Gate**: Only `approved: true` projects are accessible to non-owners
- **Guest Protection**: Unauthenticated users are redirected to sign-in
- **Connection Verification**: Network-only projects verify actual user connections

## Project Visibility Logic

### Who Can See a Project in Listings

**Implementation**: `Project.full_list_for_user` scope in `app/models/project.rb:189-222`

```ruby
# A user can see a project if:
1. They are the owner (projects.user_id = user.id), OR
2. Project is network_only = true AND they have a network connection, OR  
3. Project is semi_public = true
```

**Network Connection Check**:
```sql
LEFT JOIN network_connections ON 
  (network_connections.invitee_id = user.id AND network_connections.inviter_id = projects.user_id) OR
  (network_connections.inviter_id = user.id AND network_connections.invitee_id = projects.user_id)
```

### Visibility Combinations

| Setting | Behavior |
|---------|----------|
| `network_only = true` | Only connected users + owner see the project |
| `semi_public = true` | All registered users + owner see the project |
| Both false | **Invalid** - validation requires one to be true |
| Both true | `semi_public` takes precedence (broader access) |

## Authorization System

### Full Details Access Control

**Implementation**: `ProjectPolicy#view_full_details?` in `app/policies/project_policy.rb:24-33`

```ruby
def view_full_details?
  record.user_id == user.id || 
    record.project_auths.exists?(user_id: user.id, access_level: 'full_details')
end
```

**Access Requirements**:
1. **Owner**: Always has full access
2. **Everyone else**: Must have ProjectAuth entry with `access_level: 'full_details'`

### ProjectAuth States

**Model**: `ProjectAuth` in `app/models/project_auth.rb`

```ruby
enum access_level: { 
  denied: 0,        # Permanently rejected
  pending: 1,       # Awaiting owner approval  
  limited: 2,       # Reserved for future use
  full_details: 3   # Approved for full access
}
```

## Complete User Experience Flow

### For Project Owners

1. **Create Project** with sharing settings:
   - Choose visibility: "My Network" (`network_only`) or "Everyone" (`semi_public`)
   - Choose detail level: "Title Only" (`summary_only`) or "Everything" (`full_access`)

2. **Manage Access Requests**:
   - Receive notifications when users request access
   - Approve/deny requests via connection requests interface
   - Can revoke access by deleting ProjectAuth entries

### For Users Viewing Projects

#### Step 1: Project Discovery
Based on visibility settings and network connections:

```ruby
# User sees projects where:
- (network_only = true AND connected to owner) OR
- (semi_public = true) OR  
- (owner = current_user)
```

#### Step 2: Viewing Projects
When clicking on a project:

**Case A: Owner**
- Always sees full details via `show_full_details.html.erb`
- Can manage files, access requests, and project settings

**Case B: Non-Owner**
Authorization check: `allowed_to?(:view_full_details?, @project)`

- **Has ProjectAuth with full_details**: Shows full details
- **No ProjectAuth or pending/denied**: Shows summary only

#### Step 3: Access Request Process (Non-Owners)

**If user sees summary-only view**:

1. **Request Access**: Click "Request access" button
2. **System Creates**:
   - `ConnectionRequest` record (inviter: user, invitee: owner, project: project)
   - `ProjectAuth` record (user: user, project: project, access_level: 'pending')
3. **Owner Notification**: Email sent to project owner
4. **Owner Action**: 
   - **Approve**: Updates ProjectAuth to 'full_details' + ConnectionRequest to 'approved'
   - **Deny**: Updates ProjectAuth to 'denied' + ConnectionRequest to 'declined'
5. **User Notification**: Email sent about decision

## Current Behavior Matrix

### All Possible Sharing Combinations

| Visibility | Detail Setting | UI Displays | Actual Behavior |
|------------|---------------|-------------|-----------------|
| Network Only | Title Only | "Title Only" + "My Network" | ✅ Connected users see summary, must request full access |
| Network Only | Everything | "Everything" + "My Network" | ❌ Connected users see summary, must request full access |
| Everyone | Title Only | "Title Only" + "Everyone" | ✅ All users see summary, must request full access |
| Everyone | Everything | "Everything" + "Everyone" | ❌ All users see summary, must request full access |

### Summary View Content

**Template**: `show_summary.html.erb`

**Always Shown**:
- Project title (summary)
- Location
- Category/subcategory  
- Project owner name
- Last update date
- "Request access" button

**Never Shown in Summary**:
- Full description
- Price details
- Files/attachments
- Commission information

### Full Details View Content

**Template**: `show_full_details.html.erb`

**Includes Everything**:
- All summary content
- Full description
- Price and commission details
- All attached files with thumbnails
- Download capabilities
- Inline file viewing

## Technical Implementation Details

### Database Schema

**Projects Table Key Columns**:
```ruby
t.boolean :summary_only, default: true
t.boolean :full_access, default: false  
t.boolean :network_only, default: true
t.boolean :semi_public, default: false
```

**ProjectAuth Relationship**:
```ruby
# Project model
has_many :project_auths, dependent: :destroy
has_many :shared_with_users, through: :project_auths, source: :user

# User can check their access level
project.auth_level_for(user)  # Returns access_level or nil
```

### Controller Logic

**ProjectsController#show** (`app/controllers/projects_controller.rb:92-109`):

```ruby
def show
  @project = Project.find(params[:id])
  
  # Basic visibility check
  unless (@project.user == current_user) || 
         (@project.project_status == true && @project.approved == true)
    redirect_to projects_path, alert: 'Not available'
    return
  end
  
  # Authorization determines view
  if allowed_to?(:view_full_details?, @project)
    render 'show_full_details'  # Full access
  else
    render 'show_summary'       # Summary only + request button
  end
end
```

### Form Interface

**Project Creation/Edit Form** (`app/views/projects/_form.html.erb`):

**Sharing Controls** (lines 391-411):
```erb
<!-- What to share -->
<%= form.check_box :summary_only %>
<%= form.label "Title Only" %>

<%= form.check_box :full_access %>  
<%= form.label "Everything" %>

<!-- Who to share with -->
<%= form.check_box :network_only %>
<%= form.label "My Network" %>

<%= form.check_box :semi_public %>
<%= form.label "Everyone" %>
```

**JavaScript Validation**: Ensures exactly one option selected per dimension.

## Business Logic Patterns

### Automatic Access Scenarios

**NONE** - All non-owner access requires explicit ProjectAuth approval.

The system implements a **permission-first security model** where:
- No automatic access based on sharing settings
- All full-detail access is explicit and auditable  
- Project owners maintain complete control over who sees what

### Connection vs Project Access

**Important Distinction**:
- **Network Connection**: Bidirectional relationship for general networking
- **Project Access**: Unidirectional, project-specific authorization

Having a network connection with someone does NOT automatically grant project access, even if project is set to "Share Everything" with "My Network".

### Admin Override

**Admin/Super Boss Users**:
- Can approve/disapprove projects for publication
- Cannot override project access controls
- Project owners retain exclusive control over file access

## Security Considerations

### Access Control Enforcement

1. **File Access**: All file downloads require ProjectAuth verification
2. **URL Protection**: Direct project URLs check authorization before rendering
3. **API Consistency**: Same authorization logic across web and potential API endpoints

### Audit Trail

Every access grant/denial is recorded via:
- `ProjectAuth` records with timestamps
- `ConnectionRequest` records showing request/approval flow
- Email notifications for transparency

## Known Limitations & Issues

### 1. UI/Backend Misalignment

**Issue**: Form allows setting "Share Everything" but backend ignores it
**Impact**: User confusion about sharing behavior
**Workaround**: All projects effectively work as "Title Only" with explicit approval

### 2. Sharing Setting Validation

**Code Location**: `app/models/project.rb:358-366`

```ruby
def validate_sharing_options
  unless summary_only? || full_access?
    errors.add(:base, "Either 'Title Only' or 'Everything' must be selected")
  end
  
  unless network_only? || semi_public?  
    errors.add(:base, "Either 'My Network' or 'Everyone' must be selected")
  end
end
```

**Problem**: Validates that `full_access` can be true, but authorization system ignores it.

### 3. Legacy Code Comments

**File**: `app/policies/project_policy.rb:23`
```ruby
#TODO: Both policies are identical. Check if needed both
```

Indicates awareness that `show?` and `view_full_details?` have identical logic.

## Future Considerations

### Option 1: Honor Full Access Settings

Modify `ProjectPolicy#view_full_details?` to check `full_access` attribute:

```ruby
def view_full_details?
  return true if record.user_id == user.id
  
  # Check explicit ProjectAuth
  return true if record.project_auths.exists?(user_id: user.id, access_level: 'full_details')
  
  # NEW: Honor full_access setting for connected users
  if record.full_access?
    if record.network_only?
      return record.user.connected_with?(user)
    elsif record.semi_public?
      return true  # Any authenticated user
    end
  end
  
  false
end
```

### Option 2: Remove Full Access Option

Remove `full_access` from UI and force explicit approval model:
- Simplifies user mental model  
- Makes security model more predictable
- Reduces UI/backend complexity

### Option 3: Hybrid Approach

Keep current security-first model but improve UI clarity:
- Relabel "Everything" to "Everything (Requires Approval)"
- Add explanatory text about approval process
- Show approval status more prominently

## Conclusion

The current project sharing system implements a **security-first, explicit-approval model** where all non-owner access to full project details requires manual approval, regardless of sharing settings. While this provides maximum security and control for project owners, it creates a disconnect between user expectations (based on UI options) and actual behavior.

This is a **critical architectural decision** that affects user experience and should be clearly documented and consistently applied across all interfaces.

---

**Last Updated**: December 2024  
**Review Required**: When considering changes to authorization logic or sharing UI