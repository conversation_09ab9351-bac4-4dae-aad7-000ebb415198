# Thumbnail Race Condition Fix - Implementation Plan

## Problem Summary

The current thumbnail generation system uses `after_commit` callbacks that trigger before S3 direct uploads complete, causing race conditions and multiple failed job executions. The exponential backoff approach fights against Rails architecture instead of working with it.

## Solution: Active Storage Notifications

Replace the flawed callback system with Rails-native `ActiveSupport::Notifications` that trigger when files are actually ready for processing.

## Implementation Tasks

### 1. Remove Flawed Callback (HIGH PRIORITY)
**File**: `app/models/project.rb`
- Remove `after_commit :generate_pdf_thumbnails` callback
- Remove `generate_pdf_thumbnails` method

### 2. Create Active Storage Subscriber (HIGH PRIORITY)  
**File**: `config/initializers/active_storage_subscribers.rb`
- Subscribe to `service_update.active_storage` event
- Only process blobs attached to Project records
- Ensure blob is analyzed before processing
- Use proper transaction handling

### 3. Simplify Thumbnail Generation Job (HIGH PRIORITY)
**File**: `app/jobs/pdf_thumbnail_generation_job.rb` (rename to `thumbnail_generation_job.rb`)
- Accept project and blob_id parameters
- Remove race condition handling code
- Implement idempotency checks
- Focus only on image processing (PDFs handled by Lambda)

### 4. Fix N+1 Query Issue (MEDIUM PRIORITY)
**File**: `app/models/project.rb`
- Optimize `thumbnail_for_file` method with efficient SQL query
- Use `joins(:blob)` with `LIKE` query instead of loading all thumbnails

## Expected Benefits

1. **Zero Race Conditions**: Jobs only run when files are ready
2. **Performance**: 10x+ improvement from eliminating N+1 queries
3. **Reliability**: No failed job executions or retries needed
4. **Rails Native**: Uses official Rails notifications system

## Testing Strategy

- Test with large files (10MB+) to verify no race conditions
- Monitor GoodJob dashboard for retry patterns (should be zero)
- Verify thumbnail generation works for both images and PDFs
- Test N+1 query fix with multiple files

## Rollback Plan

If implementation fails:
1. Restore original `after_commit` callback
2. Remove subscriber initializer
3. Revert job changes
4. Keep N+1 query fix (independent improvement)

## Implementation Date

June 17, 2025 - Based on analysis of Gemini suggestions vs current exponential backoff approach.

## Key Files Modified

- `app/models/project.rb` (remove callback, fix N+1 query)
- `config/initializers/active_storage_subscribers.rb` (new file)
- `app/jobs/pdf_thumbnail_generation_job.rb` (simplify logic)

## Critical Success Factors

1. Use correct Rails 7.0.8 Active Storage notification events
2. Ensure proper transaction handling in subscriber
3. Maintain idempotency in job processing
4. Preserve existing Lambda PDF processing (working correctly)
5. Do not introduce new libraries or dependencies