#!/usr/bin/env ruby

# Test if the new Lambda naming pattern works
project = Project.find(66)
file = project.private_files.find(187)

puts "Testing Lambda naming pattern..."
puts "Original file: #{file.filename} (key: #{file.blob.key})"

# Create a test thumbnail blob using Lambda naming pattern
lambda_filename = "#{file.blob.key.gsub('.pdf', '')}.png"
puts "Lambda thumbnail filename: #{lambda_filename}"

# Check if blob already exists
existing_blob = ActiveStorage::Blob.find_by(key: "gu0oc5wt0utc5c0r7ijnayo3k61m")
if existing_blob
  puts "✅ Found existing S3 blob: #{existing_blob.filename}"
  
  # Update the filename to match Lambda pattern
  existing_blob.update!(filename: lambda_filename)
  puts "✅ Updated filename to: #{existing_blob.filename}"
  
  # Check if it's already attached
  existing_attachment = project.pdf_thumbnails.find { |att| att.blob == existing_blob }
  
  if existing_attachment
    puts "✅ Already attached to project"
  else
    puts "Attaching to project..."
    project.pdf_thumbnails.attach(existing_blob)
    puts "✅ Attached to project"
  end
  
  # Test the lookup method
  found_thumbnail = project.thumbnail_for_file(file)
  if found_thumbnail
    puts "✅ SUCCESS: thumbnail_for_file found the Lambda thumbnail!"
    puts "   Thumbnail filename: #{found_thumbnail.blob.filename}"
  else
    puts "❌ FAILED: thumbnail_for_file could not find the Lambda thumbnail"
  end
  
else
  puts "❌ No existing S3 blob found - need Lambda to create one"
end